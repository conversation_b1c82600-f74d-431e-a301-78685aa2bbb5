<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0934a722-dcc1-4337-8d9f-9c3c22096eec" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/ruoyi-common/ruoyi-common-excel/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/ruoyi-common/ruoyi-common-excel/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/interview/ReportController.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/interview/ReportController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/learning/QuestionCommentController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/QuestionVO.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionBankBookmarkMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionBankBookmarkMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionBankMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionBankMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionCommentMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionCommentMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-system/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/backend/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-system/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/META-INF/mps/autoMapper" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/META-INF/mps/autoMapper" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/META-INF/mps/mappers" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/META-INF/mps/mappers" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="30Js7PDYRplZDTU9FeCp1ZcedIx" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/softwart-xunfei-code",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "pnpm",
    "project.structure.last.edited": "全局库",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "ts.external.directory.path": "C:\\Users\\<USER>\\Desktop\\softwart-xunfei-code\\front\\unibest-main\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="0934a722-dcc1-4337-8d9f-9c3c22096eec" name="更改" comment="" />
      <created>1753356845612</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753356845612</updated>
      <workItem from="1753356847750" duration="236000" />
      <workItem from="1753507208941" duration="523000" />
      <workItem from="1753667719510" duration="728000" />
      <workItem from="1754014820569" duration="692000" />
      <workItem from="1754018331569" duration="22000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
    <option name="exactExcludedFiles">
      <list>
        <option value="$PROJECT_DIR$/front/unibest-main/src/typings.d.ts" />
      </list>
    </option>
  </component>
</project>