{"doc": " 对象工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "notNullGetter", "paramTypes": ["java.lang.Object", "java.util.function.Function"], "doc": " 如果对象不为空，则获取对象中的某个字段 ObjectUtils.notNullGetter(user, User::getName);\n\n @param obj  对象\n @param func 获取方法\n @return 对象字段\n"}, {"name": "notNullGetter", "paramTypes": ["java.lang.Object", "java.util.function.Function", "java.lang.Object"], "doc": " 如果对象不为空，则获取对象中的某个字段，否则返回默认值\n\n @param obj          对象\n @param func         获取方法\n @param defaultValue 默认值\n @return 对象字段\n"}, {"name": "notNull", "paramTypes": ["java.lang.Object", "java.lang.Object"], "doc": " 如果值不为空，则返回值，否则返回默认值\n\n @param obj          对象\n @param defaultValue 默认值\n @return 对象字段\n"}], "constructors": []}