{"doc": " <h1>Excel下拉可选项</h1>\n 注意：为确保下拉框解析正确，传值务必使用createOptionValue()做为值的拼接\n\n <AUTHOR>\n", "fields": [{"name": "DELIMITER", "doc": " 分隔符\n"}, {"name": "index", "doc": " 一级下拉所在列index，从0开始算\n"}, {"name": "nextIndex", "doc": " 二级下拉所在的index，从0开始算，不能与一级相同\n"}, {"name": "options", "doc": " 一级下拉所包含的数据\n"}, {"name": "nextOptions", "doc": " 二级下拉所包含的数据Map\n <p>以每一个一级选项值为Key，每个一级选项对应的二级数据为Value</p>\n"}], "enumConstants": [], "methods": [{"name": "createOptionValue", "paramTypes": ["java.lang.Object[]"], "doc": " <h2>创建每个选项可选值</h2>\n <p>注意：不能以数字，特殊符号开头，选项中不可以包含任何运算符号</p>\n\n @param vars 可选值内包含的参数\n @return 合规的可选值\n"}, {"name": "analyzeOptionValue", "paramTypes": ["java.lang.String"], "doc": " 将处理后合理的可选值解析为原始的参数\n\n @param option 经过处理后的合理的可选项\n @return 原始的参数\n"}, {"name": "buildLinkedOptions", "paramTypes": ["java.util.List", "int", "java.util.List", "int", "java.util.function.Function", "java.util.function.Function", "java.util.function.Function"], "doc": " 创建级联下拉选项\n\n @param parentList                  父实体可选项原始数据\n @param parentIndex                 父下拉选位置\n @param sonList                     子实体可选项原始数据\n @param sonIndex                    子下拉选位置\n @param parentHowToGetIdFunction    父类如何获取唯一标识\n @param sonHowToGetParentIdFunction 子类如何获取父类的唯一标识\n @param howToBuildEveryOption       如何生成下拉选内容\n @return 级联下拉选项\n"}], "constructors": [{"name": "<init>", "paramTypes": ["int", "java.util.List"], "doc": " 创建只有一级的下拉选\n"}]}