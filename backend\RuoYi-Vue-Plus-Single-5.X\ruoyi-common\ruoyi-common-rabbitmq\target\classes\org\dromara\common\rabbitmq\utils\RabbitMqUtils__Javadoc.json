{"doc": " RabbitMQ 工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "createExchange", "paramTypes": ["java.lang.String", "java.lang.String", "boolean", "boolean"], "doc": " 创建交换机\n\n @param exchangeName 交换机名称\n @param exchangeType 交换机类型\n @param durable      是否持久化\n @param autoDelete   是否自动删除\n"}, {"name": "createQueue", "paramTypes": ["java.lang.String", "boolean", "boolean", "boolean"], "doc": " 创建队列\n\n @param queueName  队列名称\n @param durable    是否持久化\n @param exclusive  是否独占\n @param autoDelete 是否自动删除\n @return Queue对象\n"}, {"name": "createQueue", "paramTypes": ["java.lang.String", "boolean", "boolean", "boolean", "java.util.Map"], "doc": " 创建队列（带参数）\n\n @param queueName  队列名称\n @param durable    是否持久化\n @param exclusive  是否独占\n @param autoDelete 是否自动删除\n @param args       队列参数\n @return Queue对象\n"}, {"name": "createDeadLetterQueue", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "long"], "doc": " 创建死信队列\n\n @param queueName      队列名称\n @param deadExchange   死信交换机\n @param deadRoutingKey 死信路由键\n @param messageTtl     消息过期时间（毫秒）\n @return Queue对象\n"}, {"name": "createDelayQueue", "paramTypes": ["java.lang.String"], "doc": " 创建延迟队列\n\n @param queueName 队列名称\n @return Queue对象\n"}, {"name": "bindQueue", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 绑定队列到交换机\n\n @param queueName    队列名称\n @param exchangeName 交换机名称\n @param routingKey   路由键\n"}, {"name": "unbindQueue", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 解除队列绑定\n\n @param queueName    队列名称\n @param exchangeName 交换机名称\n @param routingKey   路由键\n"}, {"name": "deleteExchange", "paramTypes": ["java.lang.String"], "doc": " 删除交换机\n\n @param exchangeName 交换机名称\n"}, {"name": "deleteQueue", "paramTypes": ["java.lang.String"], "doc": " 删除队列\n\n @param queueName 队列名称\n"}, {"name": "purge<PERSON><PERSON>ue", "paramTypes": ["java.lang.String"], "doc": " 清空队列消息\n\n @param queueName 队列名称\n"}, {"name": "getQueueMessageCount", "paramTypes": ["java.lang.String"], "doc": " 获取队列消息数量\n\n @param queueName 队列名称\n @return 消息数量\n"}, {"name": "queueExists", "paramTypes": ["java.lang.String"], "doc": " 检查队列是否存在\n\n @param queueName 队列名称\n @return 是否存在\n"}], "constructors": []}