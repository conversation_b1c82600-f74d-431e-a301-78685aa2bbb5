{"doc": " 登录鉴权助手\n <p>\n user_type 为 用户类型 同一个用户表 可以有多种用户类型 例如 pc,app\n deivce 为 设备类型 同一个用户类型 可以有 多种设备类型 例如 web,ios\n 可以组成 用户类型与设备类型多对多的 权限灵活控制\n <p>\n 多用户体系 针对 多种用户类型 但权限控制不一致\n 可以组成 多用户类型表与多设备类型 分别控制权限\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "login", "paramTypes": ["org.dromara.common.core.domain.model.LoginUser", "cn.dev33.satoken.stp.parameter.SaLoginParameter"], "doc": " 登录系统 基于 设备类型\n 针对相同用户体系不同设备\n\n @param loginUser 登录用户信息\n @param model     配置参数\n"}, {"name": "getTenantId", "paramTypes": [], "doc": " 获取租户ID\n"}, {"name": "appLogin", "paramTypes": ["org.dromara.common.core.domain.model.AppLoginUser", "cn.dev33.satoken.stp.parameter.SaLoginParameter"], "doc": " App登录\n\n @param appLoginUser App登录用户信息\n @param model        配置参数\n"}, {"name": "getLoginUser", "paramTypes": [], "doc": " 获取用户(多级缓存)\n"}, {"name": "getLoginUser", "paramTypes": ["java.lang.String"], "doc": " 获取用户基于token\n"}, {"name": "getUserId", "paramTypes": [], "doc": " 获取用户id\n"}, {"name": "getUserIdStr", "paramTypes": [], "doc": " 获取用户id\n"}, {"name": "getUsername", "paramTypes": [], "doc": " 获取用户账户\n"}, {"name": "getDeptId", "paramTypes": [], "doc": " 获取部门ID\n"}, {"name": "getDeptName", "paramTypes": [], "doc": " 获取部门名\n"}, {"name": "getDeptCategory", "paramTypes": [], "doc": " 获取部门类别编码\n"}, {"name": "getExtra", "paramTypes": ["java.lang.String"], "doc": " 获取当前 Token 的扩展信息\n\n @param key 键值\n @return 对应的扩展数据\n"}, {"name": "getUserType", "paramTypes": [], "doc": " 获取用户类型\n"}, {"name": "isSuperAdmin", "paramTypes": ["java.lang.Long"], "doc": " 是否为超级管理员\n\n @param userId 用户ID\n @return 结果\n"}, {"name": "isSuperAdmin", "paramTypes": [], "doc": " 是否为超级管理员\n\n @return 结果\n"}, {"name": "is<PERSON>ogin", "paramTypes": [], "doc": " 检查当前用户是否已登录\n\n @return 结果\n"}], "constructors": []}