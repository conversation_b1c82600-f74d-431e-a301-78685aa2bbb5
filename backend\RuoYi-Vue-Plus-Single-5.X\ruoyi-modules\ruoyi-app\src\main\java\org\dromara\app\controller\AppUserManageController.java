package org.dromara.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.app.domain.bo.AppUserManageBo;
import org.dromara.app.domain.vo.AppUserManageVo;
import org.dromara.app.service.IAppUserManageService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应用用户管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/manage/user")
public class AppUserManageController extends BaseController {

    private final IAppUserManageService appUserManageService;

    /**
     * 查询应用用户列表
     */
    @SaCheckPermission("app:user:list")
    @GetMapping("/list")
    public TableDataInfo<AppUserManageVo> list(AppUserManageBo bo, PageQuery pageQuery) {
        return appUserManageService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出应用用户列表
     */
    @SaCheckPermission("app:user:export")
    @Log(title = "应用用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppUserManageBo bo, HttpServletResponse response) {
        List<AppUserManageVo> list = appUserManageService.exportUserList(bo);
        ExcelUtil.exportExcel(list, "应用用户", AppUserManageVo.class, response);
    }

    /**
     * 获取应用用户详细信息
     */
    @SaCheckPermission("app:user:query")
    @GetMapping("/{userId}")
    public R<AppUserManageVo> getInfo(@NotNull(message = "主键不能为空")
                                @PathVariable Long userId) {
        return R.ok(appUserManageService.queryById(userId));
    }

    /**
     * 新增应用用户
     */
    @SaCheckPermission("app:user:add")
    @Log(title = "应用用户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppUserManageBo bo) {
        return toAjax(appUserManageService.insertByBo(bo));
    }

    /**
     * 修改应用用户
     */
    @SaCheckPermission("app:user:edit")
    @Log(title = "应用用户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppUserManageBo bo) {
        return toAjax(appUserManageService.updateByBo(bo));
    }

    /**
     * 删除应用用户
     */
    @SaCheckPermission("app:user:remove")
    @Log(title = "应用用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] userIds) {
        return toAjax(appUserManageService.deleteWithValidByIds(List.of(userIds), true));
    }

    /**
     * 重置用户密码
     */
    @SaCheckPermission("app:user:resetPwd")
    @Log(title = "应用用户", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public R<Void> resetPwd(@RequestBody AppUserManageBo bo) {
        return toAjax(appUserManageService.resetUserPassword(bo.getUserId(), bo.getPassword()));
    }

    /**
     * 状态修改
     */
    @SaCheckPermission("app:user:edit")
    @Log(title = "应用用户", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody AppUserManageBo bo) {
        return toAjax(appUserManageService.changeUserStatus(bo.getUserId(), bo.getStatus()));
    }

}
