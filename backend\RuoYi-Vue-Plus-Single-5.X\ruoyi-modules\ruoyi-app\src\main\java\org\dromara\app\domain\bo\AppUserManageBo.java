package org.dromara.app.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.app.domain.AppUser;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 应用用户管理业务对象 app_user
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AppUser.class, reverseConvertGenerate = false)
public class AppUserManageBo extends BaseEntity {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确", groups = {AddGroup.class, EditGroup.class})
    private String phone;

    /**
     * 用户邮箱
     */
    @Email(message = "邮箱格式不正确", groups = {AddGroup.class, EditGroup.class})
    @NotBlank(message = "用户邮箱不能为空", groups = {AddGroup.class})
    @Size(max = 50, message = "用户邮箱长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String email;

    /**
     * 用户姓名
     */
    @NotBlank(message = "用户姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 30, message = "用户姓名长度不能超过30个字符", groups = {AddGroup.class, EditGroup.class})
    private String realName;

    /**
     * 用户性别（男/女）
     */
    @Pattern(regexp = "^[男女]$", message = "用户性别只能是男或女", groups = {AddGroup.class, EditGroup.class})
    private String gender;

    /**
     * 学生学号
     */
    @Size(max = 20, message = "学生学号长度不能超过20个字符", groups = {AddGroup.class, EditGroup.class})
    private String studentId;

    /**
     * 专业
     */
    @Size(max = 50, message = "专业长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String major;

    /**
     * 年级
     */
    @Size(max = 10, message = "年级长度不能超过10个字符", groups = {AddGroup.class, EditGroup.class})
    private String grade;

    /**
     * 学校名称
     */
    @Size(max = 100, message = "学校名称长度不能超过100个字符", groups = {AddGroup.class, EditGroup.class})
    private String school;

    /**
     * 个人简介
     */
    @Size(max = 500, message = "个人简介长度不能超过500个字符", groups = {AddGroup.class, EditGroup.class})
    private String introduction;

    /**
     * 密码
     */
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间", groups = {AddGroup.class})
    private String password;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 帐号状态（0正常 1停用）
     */
    @Pattern(regexp = "^[01]$", message = "帐号状态只能是0或1", groups = {AddGroup.class, EditGroup.class})
    private String status;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private String delFlag;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 注册时间
     */
    private Date registeredAt;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符", groups = {AddGroup.class, EditGroup.class})
    private String remark;

}
