package org.dromara.app.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.app.domain.AppUser;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 应用用户管理视图对象 app_user
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AppUser.class)
public class AppUserManageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 用户手机号
     */
    @ExcelProperty(value = "用户手机号")
    private String phone;

    /**
     * 用户邮箱
     */
    @ExcelProperty(value = "用户邮箱")
    private String email;

    /**
     * 用户姓名
     */
    @ExcelProperty(value = "用户姓名")
    private String realName;

    /**
     * 用户性别（男/女）
     */
    @ExcelProperty(value = "用户性别")
    private String gender;

    /**
     * 学生学号
     */
    @ExcelProperty(value = "学生学号")
    private String studentId;

    /**
     * 专业
     */
    @ExcelProperty(value = "专业")
    private String major;

    /**
     * 年级
     */
    @ExcelProperty(value = "年级")
    private String grade;

    /**
     * 学校名称
     */
    @ExcelProperty(value = "学校名称")
    private String school;

    /**
     * 个人简介
     */
    @ExcelProperty(value = "个人简介")
    private String introduction;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 帐号状态（0正常 1停用）
     */
    @ExcelProperty(value = "帐号状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_normal_disable")
    private String status;

    /**
     * 最后登录IP
     */
    @ExcelProperty(value = "最后登录IP")
    private String loginIp;

    /**
     * 最后登录时间
     */
    @ExcelProperty(value = "最后登录时间")
    private Date loginDate;

    /**
     * 注册时间
     */
    @ExcelProperty(value = "注册时间")
    private Date registeredAt;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 简历数量（非数据库字段）
     */
    private Integer resumeCount;

    /**
     * 最近活跃时间（非数据库字段）
     */
    private Date lastActiveTime;

}
