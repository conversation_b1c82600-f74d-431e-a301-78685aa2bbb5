package org.dromara.app.service;

import org.dromara.app.domain.bo.AppUserManageBo;
import org.dromara.app.domain.vo.AppUserManageVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 应用用户管理Service接口
 *
 * <AUTHOR>
 */
public interface IAppUserManageService {

    /**
     * 查询应用用户
     *
     * @param userId 用户主键
     * @return 应用用户
     */
    AppUserManageVo queryById(Long userId);

    /**
     * 查询应用用户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页查询条件
     * @return 应用用户分页列表
     */
    TableDataInfo<AppUserManageVo> queryPageList(AppUserManageBo bo, PageQuery pageQuery);

    /**
     * 查询应用用户列表
     *
     * @param bo 查询条件
     * @return 应用用户列表
     */
    List<AppUserManageVo> queryList(AppUserManageBo bo);

    /**
     * 导出应用用户列表
     *
     * @param bo 查询条件
     * @return 应用用户列表
     */
    List<AppUserManageVo> exportUserList(AppUserManageBo bo);

    /**
     * 新增应用用户
     *
     * @param bo 应用用户信息
     * @return 新增结果
     */
    Boolean insertByBo(AppUserManageBo bo);

    /**
     * 修改应用用户
     *
     * @param bo 应用用户信息
     * @return 修改结果
     */
    Boolean updateByBo(AppUserManageBo bo);

    /**
     * 校验并批量删除应用用户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 删除结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean checkEmailUnique(AppUserManageBo user);

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean checkPhoneUnique(AppUserManageBo user);

    /**
     * 校验学号是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean checkStudentIdUnique(AppUserManageBo user);

    /**
     * 重置用户密码
     *
     * @param userId      用户ID
     * @param newPassword 新密码
     * @return 重置结果
     */
    Boolean resetUserPassword(Long userId, String newPassword);

    /**
     * 修改用户状态
     *
     * @param userId 用户ID
     * @param status 状态
     * @return 修改结果
     */
    Boolean changeUserStatus(Long userId, String status);

}
