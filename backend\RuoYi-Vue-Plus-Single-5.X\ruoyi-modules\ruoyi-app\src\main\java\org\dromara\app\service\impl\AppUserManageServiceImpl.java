package org.dromara.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.AppUser;
import org.dromara.app.domain.bo.AppUserManageBo;
import org.dromara.app.domain.vo.AppUserManageVo;
import org.dromara.app.mapper.AppUserMapper;
import org.dromara.app.service.IAppUserManageService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 应用用户管理Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppUserManageServiceImpl implements IAppUserManageService {

    private final AppUserMapper baseMapper;

    /**
     * 查询应用用户
     */
    @Override
    public AppUserManageVo queryById(Long userId) {
        return baseMapper.selectVoById(userId, AppUserManageVo.class);
    }

    /**
     * 查询应用用户列表
     */
    @Override
    public TableDataInfo<AppUserManageVo> queryPageList(AppUserManageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppUser> lqw = buildQueryWrapper(bo);
        Page<AppUserManageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw, AppUserManageVo.class);
        return TableDataInfo.build(result);
    }

    /**
     * 查询应用用户列表
     */
    @Override
    public List<AppUserManageVo> queryList(AppUserManageBo bo) {
        LambdaQueryWrapper<AppUser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw, AppUserManageVo.class);
    }

    /**
     * 导出应用用户列表
     */
    @Override
    public List<AppUserManageVo> exportUserList(AppUserManageBo bo) {
        LambdaQueryWrapper<AppUser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw, AppUserManageVo.class);
    }

    private LambdaQueryWrapper<AppUser> buildQueryWrapper(AppUserManageBo bo) {
        LambdaQueryWrapper<AppUser> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getPhone()), AppUser::getPhone, bo.getPhone());
        lqw.like(StringUtils.isNotBlank(bo.getEmail()), AppUser::getEmail, bo.getEmail());
        lqw.like(StringUtils.isNotBlank(bo.getRealName()), AppUser::getRealName, bo.getRealName());
        lqw.eq(StringUtils.isNotBlank(bo.getGender()), AppUser::getGender, bo.getGender());
        lqw.like(StringUtils.isNotBlank(bo.getStudentId()), AppUser::getStudentId, bo.getStudentId());
        lqw.like(StringUtils.isNotBlank(bo.getMajor()), AppUser::getMajor, bo.getMajor());
        lqw.like(StringUtils.isNotBlank(bo.getGrade()), AppUser::getGrade, bo.getGrade());
        lqw.like(StringUtils.isNotBlank(bo.getSchool()), AppUser::getSchool, bo.getSchool());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppUser::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增应用用户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(AppUserManageBo bo) {
        AppUser add = BeanUtil.toBean(bo, AppUser.class);
        validEntityBeforeSave(add);

        // 校验邮箱唯一性
        if (!checkEmailUnique(bo)) {
            throw new ServiceException("新增用户'" + bo.getRealName() + "'失败，邮箱账号已存在");
        }

        // 校验手机号唯一性
        if (StringUtils.isNotBlank(bo.getPhone()) && !checkPhoneUnique(bo)) {
            throw new ServiceException("新增用户'" + bo.getRealName() + "'失败，手机号码已存在");
        }

        // 校验学号唯一性
        if (StringUtils.isNotBlank(bo.getStudentId()) && !checkStudentIdUnique(bo)) {
            throw new ServiceException("新增用户'" + bo.getRealName() + "'失败，学号已存在");
        }

        // 设置默认值
        if (StringUtils.isNotBlank(add.getPassword())) {
            add.setPassword(BCrypt.hashpw(add.getPassword()));
        }
        if (StringUtils.isBlank(add.getStatus())) {
            add.setStatus("0");
        }
        add.setDelFlag("0");

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setUserId(add.getUserId());
        }
        return flag;
    }

    /**
     * 修改应用用户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(AppUserManageBo bo) {
        AppUser update = BeanUtil.toBean(bo, AppUser.class);
        validEntityBeforeSave(update);

        // 校验邮箱唯一性
        if (!checkEmailUnique(bo)) {
            throw new ServiceException("修改用户'" + bo.getRealName() + "'失败，邮箱账号已存在");
        }

        // 校验手机号唯一性
        if (StringUtils.isNotBlank(bo.getPhone()) && !checkPhoneUnique(bo)) {
            throw new ServiceException("修改用户'" + bo.getRealName() + "'失败，手机号码已存在");
        }

        // 校验学号唯一性
        if (StringUtils.isNotBlank(bo.getStudentId()) && !checkStudentIdUnique(bo)) {
            throw new ServiceException("修改用户'" + bo.getRealName() + "'失败，学号已存在");
        }

        // 密码不为空时进行加密
        if (StringUtils.isNotBlank(update.getPassword())) {
            update.setPassword(BCrypt.hashpw(update.getPassword()));
        }

        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppUser entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除应用用户
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 校验邮箱是否唯一
     */
    @Override
    public boolean checkEmailUnique(AppUserManageBo user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<AppUser>()
            .eq(AppUser::getEmail, user.getEmail())
            .ne(ObjectUtil.isNotNull(user.getUserId()), AppUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验手机号是否唯一
     */
    @Override
    public boolean checkPhoneUnique(AppUserManageBo user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<AppUser>()
            .eq(AppUser::getPhone, user.getPhone())
            .ne(ObjectUtil.isNotNull(user.getUserId()), AppUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验学号是否唯一
     */
    @Override
    public boolean checkStudentIdUnique(AppUserManageBo user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<AppUser>()
            .eq(AppUser::getStudentId, user.getStudentId())
            .ne(ObjectUtil.isNotNull(user.getUserId()), AppUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 重置用户密码
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetUserPassword(Long userId, String newPassword) {
        AppUser user = new AppUser();
        user.setUserId(userId);
        user.setPassword(BCrypt.hashpw(newPassword));
        return baseMapper.updateById(user) > 0;
    }

    /**
     * 修改用户状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeUserStatus(Long userId, String status) {
        AppUser user = new AppUser();
        user.setUserId(userId);
        user.setStatus(status);
        return baseMapper.updateById(user) > 0;
    }

}
