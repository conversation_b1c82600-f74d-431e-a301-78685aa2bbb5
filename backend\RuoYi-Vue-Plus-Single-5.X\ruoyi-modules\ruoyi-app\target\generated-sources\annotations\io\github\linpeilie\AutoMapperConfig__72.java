package io.github.linpeilie;

import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper__1;
import org.dromara.system.domain.bo.SysOperLogBoToOperLogEventMapper__1;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__72.class, OperLogEventToSysOperLogBoMapper__1.class, SysOperLogBoToOperLogEventMapper__1.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__72 {
}
