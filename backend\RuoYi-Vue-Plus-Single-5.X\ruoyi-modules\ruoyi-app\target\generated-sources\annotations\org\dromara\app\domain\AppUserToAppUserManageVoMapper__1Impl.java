package org.dromara.app.domain;

import javax.annotation.processing.Generated;
import org.dromara.app.domain.vo.AppUserManageVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T11:12:39+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AppUserToAppUserManageVoMapper__1Impl implements AppUserToAppUserManageVoMapper__1 {

    @Override
    public AppUserManageVo convert(AppUser arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserManageVo appUserManageVo = new AppUserManageVo();

        appUserManageVo.setAvatar( arg0.getAvatar() );
        appUserManageVo.setCreateTime( arg0.getCreateTime() );
        appUserManageVo.setEmail( arg0.getEmail() );
        appUserManageVo.setGender( arg0.getGender() );
        appUserManageVo.setGrade( arg0.getGrade() );
        appUserManageVo.setIntroduction( arg0.getIntroduction() );
        appUserManageVo.setLoginDate( arg0.getLoginDate() );
        appUserManageVo.setLoginIp( arg0.getLoginIp() );
        appUserManageVo.setMajor( arg0.getMajor() );
        appUserManageVo.setPhone( arg0.getPhone() );
        appUserManageVo.setRealName( arg0.getRealName() );
        appUserManageVo.setRegisteredAt( arg0.getRegisteredAt() );
        appUserManageVo.setRemark( arg0.getRemark() );
        appUserManageVo.setSchool( arg0.getSchool() );
        appUserManageVo.setStatus( arg0.getStatus() );
        appUserManageVo.setStudentId( arg0.getStudentId() );
        appUserManageVo.setUpdateTime( arg0.getUpdateTime() );
        appUserManageVo.setUserId( arg0.getUserId() );

        return appUserManageVo;
    }

    @Override
    public AppUserManageVo convert(AppUser arg0, AppUserManageVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAvatar( arg0.getAvatar() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setGender( arg0.getGender() );
        arg1.setGrade( arg0.getGrade() );
        arg1.setIntroduction( arg0.getIntroduction() );
        arg1.setLoginDate( arg0.getLoginDate() );
        arg1.setLoginIp( arg0.getLoginIp() );
        arg1.setMajor( arg0.getMajor() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setRealName( arg0.getRealName() );
        arg1.setRegisteredAt( arg0.getRegisteredAt() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSchool( arg0.getSchool() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setStudentId( arg0.getStudentId() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}
