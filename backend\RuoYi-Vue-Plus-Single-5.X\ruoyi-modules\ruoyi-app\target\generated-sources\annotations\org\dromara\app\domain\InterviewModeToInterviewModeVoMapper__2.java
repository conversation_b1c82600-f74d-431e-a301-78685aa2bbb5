package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.vo.InterviewModeVo;
import org.dromara.app.domain.vo.InterviewModeVoToInterviewModeMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {InterviewModeVoToInterviewModeMapper__2.class},
    imports = {}
)
public interface InterviewModeToInterviewModeVoMapper__2 extends BaseMapper<InterviewMode, InterviewModeVo> {
}
