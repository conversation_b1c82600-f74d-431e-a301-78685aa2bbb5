package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.InterviewResultBoToInterviewResultMapper__2;
import org.dromara.app.domain.vo.InterviewResultVo;
import org.dromara.app.domain.vo.InterviewResultVoToInterviewResultMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {InterviewResultVoToInterviewResultMapper__2.class,InterviewResultBoToInterviewResultMapper__2.class},
    imports = {}
)
public interface InterviewResultToInterviewResultVoMapper__2 extends BaseMapper<InterviewResult, InterviewResultVo> {
}
