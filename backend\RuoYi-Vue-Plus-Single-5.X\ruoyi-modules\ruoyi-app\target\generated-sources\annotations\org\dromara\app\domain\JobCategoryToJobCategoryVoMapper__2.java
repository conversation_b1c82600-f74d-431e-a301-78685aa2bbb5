package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.vo.JobCategoryVo;
import org.dromara.app.domain.vo.JobCategoryVoToJobCategoryMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {JobCategoryVoToJobCategoryMapper__2.class},
    imports = {}
)
public interface JobCategoryToJobCategoryVoMapper__2 extends BaseMapper<JobCategory, JobCategoryVo> {
}
