package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.UserResumeBoToUserResumeMapper__2;
import org.dromara.app.domain.vo.UserResumeVo;
import org.dromara.app.domain.vo.UserResumeVoToUserResumeMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {UserResumeVoToUserResumeMapper__2.class,UserResumeBoToUserResumeMapper__2.class},
    imports = {}
)
public interface UserResumeToUserResumeVoMapper__2 extends BaseMapper<UserResume, UserResumeVo> {
}
