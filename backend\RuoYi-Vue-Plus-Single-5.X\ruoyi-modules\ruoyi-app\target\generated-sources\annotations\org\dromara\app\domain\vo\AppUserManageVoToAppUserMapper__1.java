package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.AppUser;
import org.dromara.app.domain.AppUserToAppUserManageVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {AppUserToAppUserManageVoMapper__1.class},
    imports = {}
)
public interface AppUserManageVoToAppUserMapper__1 extends BaseMapper<AppUserManageVo, AppUser> {
}
