package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.Feedback;
import org.dromara.app.domain.FeedbackToFeedbackVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {FeedbackToFeedbackVoMapper__2.class},
    imports = {}
)
public interface FeedbackVoToFeedbackMapper__2 extends BaseMapper<FeedbackVo, Feedback> {
}
