package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.InterviewResult;
import org.dromara.app.domain.InterviewResultToInterviewResultVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {InterviewResultToInterviewResultVoMapper__2.class},
    imports = {}
)
public interface InterviewResultVoToInterviewResultMapper__2 extends BaseMapper<InterviewResultVo, InterviewResult> {
}
