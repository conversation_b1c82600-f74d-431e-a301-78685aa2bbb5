package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.KnowledgeBase;
import org.dromara.app.domain.KnowledgeBaseToKnowledgeBaseVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {KnowledgeBaseToKnowledgeBaseVoMapper__2.class},
    imports = {}
)
public interface KnowledgeBaseVoToKnowledgeBaseMapper__2 extends BaseMapper<KnowledgeBaseVo, KnowledgeBase> {
}
