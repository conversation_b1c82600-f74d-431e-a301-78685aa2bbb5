package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.KnowledgeDocument;
import org.dromara.app.domain.KnowledgeDocumentToKnowledgeDocumentVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {KnowledgeDocumentToKnowledgeDocumentVoMapper__2.class},
    imports = {}
)
public interface KnowledgeDocumentVoToKnowledgeDocumentMapper__2 extends BaseMapper<KnowledgeDocumentVo, KnowledgeDocument> {
}
