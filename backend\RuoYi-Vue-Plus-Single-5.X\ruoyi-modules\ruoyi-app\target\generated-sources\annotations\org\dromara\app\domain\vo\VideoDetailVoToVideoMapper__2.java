package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.Video;
import org.dromara.app.domain.VideoToVideoDetailVoMapper__2;
import org.dromara.app.utils.VideoMappingUtils;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {VideoMappingUtils.class,VideoToVideoDetailVoMapper__2.class},
    imports = {}
)
public interface VideoDetailVoToVideoMapper__2 extends BaseMapper<VideoDetailVo, Video> {
}
