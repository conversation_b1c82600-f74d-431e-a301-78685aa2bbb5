package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPaymentOrder;
import org.dromara.system.domain.SysPaymentOrderToPaymentOrderVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {SysPaymentOrderToPaymentOrderVoMapper__1.class},
    imports = {}
)
public interface PaymentOrderVoToSysPaymentOrderMapper__1 extends BaseMapper<PaymentOrderVo, SysPaymentOrder> {
}
