package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysClientBoToSysClientMapper__177;
import org.dromara.system.domain.vo.SysClientVo;
import org.dromara.system.domain.vo.SysClientVoToSysClientMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysClientVoToSysClientMapper__177.class,SysClientBoToSysClientMapper__177.class},
    imports = {}
)
public interface SysClientToSysClientVoMapper__177 extends BaseMapper<SysClient, SysClientVo> {
}
