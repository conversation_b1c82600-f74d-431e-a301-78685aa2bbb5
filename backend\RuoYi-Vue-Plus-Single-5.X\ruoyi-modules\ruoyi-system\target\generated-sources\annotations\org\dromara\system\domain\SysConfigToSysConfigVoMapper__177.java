package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysConfigBoToSysConfigMapper__177;
import org.dromara.system.domain.vo.SysConfigVo;
import org.dromara.system.domain.vo.SysConfigVoToSysConfigMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysConfigVoToSysConfigMapper__177.class,SysConfigBoToSysConfigMapper__177.class},
    imports = {}
)
public interface SysConfigToSysConfigVoMapper__177 extends BaseMapper<SysConfig, SysConfigVo> {
}
