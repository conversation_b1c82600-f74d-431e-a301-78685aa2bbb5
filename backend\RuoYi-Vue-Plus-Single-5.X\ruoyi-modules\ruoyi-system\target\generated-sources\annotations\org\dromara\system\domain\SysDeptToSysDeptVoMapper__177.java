package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__177;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysDeptBoToSysDeptMapper__177.class,SysDeptVoToSysDeptMapper__177.class,SysDeptBoToSysDeptMapper__177.class},
    imports = {}
)
public interface SysDeptToSysDeptVoMapper__177 extends BaseMapper<SysDept, SysDeptVo> {
}
