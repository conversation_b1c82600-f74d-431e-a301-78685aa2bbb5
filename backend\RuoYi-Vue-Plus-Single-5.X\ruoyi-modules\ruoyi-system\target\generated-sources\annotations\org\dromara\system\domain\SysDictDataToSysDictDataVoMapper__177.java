package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictDataBoToSysDictDataMapper__177;
import org.dromara.system.domain.vo.SysDictDataVo;
import org.dromara.system.domain.vo.SysDictDataVoToSysDictDataMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysDictDataBoToSysDictDataMapper__177.class,SysDictDataVoToSysDictDataMapper__177.class},
    imports = {}
)
public interface SysDictDataToSysDictDataVoMapper__177 extends BaseMapper<SysDictData, SysDictDataVo> {
}
