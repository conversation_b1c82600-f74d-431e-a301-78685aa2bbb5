package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictTypeBoToSysDictTypeMapper__177;
import org.dromara.system.domain.vo.SysDictTypeVo;
import org.dromara.system.domain.vo.SysDictTypeVoToSysDictTypeMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysDictTypeBoToSysDictTypeMapper__177.class,SysDictTypeVoToSysDictTypeMapper__177.class},
    imports = {}
)
public interface SysDictTypeToSysDictTypeVoMapper__177 extends BaseMapper<SysDictType, SysDictTypeVo> {
}
