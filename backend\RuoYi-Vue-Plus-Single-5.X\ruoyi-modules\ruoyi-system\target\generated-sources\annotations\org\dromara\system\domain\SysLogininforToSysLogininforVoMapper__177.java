package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysLogininforBoToSysLogininforMapper__177;
import org.dromara.system.domain.vo.SysLogininforVo;
import org.dromara.system.domain.vo.SysLogininforVoToSysLogininforMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysLogininforBoToSysLogininforMapper__177.class,SysLogininforVoToSysLogininforMapper__177.class},
    imports = {}
)
public interface SysLogininforToSysLogininforVoMapper__177 extends BaseMapper<SysLogininfor, SysLogininforVo> {
}
