package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.MajorBoToSysMajorMapper__177;
import org.dromara.system.domain.vo.MajorVo;
import org.dromara.system.domain.vo.MajorVoToSysMajorMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {MajorVoToSysMajorMapper__177.class,MajorBoToSysMajorMapper__177.class},
    imports = {}
)
public interface SysMajorToMajorVoMapper__177 extends BaseMapper<SysMajor, MajorVo> {
}
