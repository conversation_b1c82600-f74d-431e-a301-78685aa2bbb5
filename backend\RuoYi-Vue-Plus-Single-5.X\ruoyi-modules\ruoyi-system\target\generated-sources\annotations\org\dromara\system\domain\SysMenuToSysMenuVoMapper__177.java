package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysMenuBoToSysMenuMapper__177;
import org.dromara.system.domain.vo.SysMenuVo;
import org.dromara.system.domain.vo.SysMenuVoToSysMenuMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysMenuVoToSysMenuMapper__177.class,SysMenuBoToSysMenuMapper__177.class},
    imports = {}
)
public interface SysMenuToSysMenuVoMapper__177 extends BaseMapper<SysMenu, SysMenuVo> {
}
