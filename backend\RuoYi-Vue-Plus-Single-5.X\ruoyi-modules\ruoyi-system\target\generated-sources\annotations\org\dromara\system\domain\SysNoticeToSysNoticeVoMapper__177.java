package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysNoticeBoToSysNoticeMapper__177;
import org.dromara.system.domain.vo.SysNoticeVo;
import org.dromara.system.domain.vo.SysNoticeVoToSysNoticeMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysNoticeVoToSysNoticeMapper__177.class,SysNoticeBoToSysNoticeMapper__177.class},
    imports = {}
)
public interface SysNoticeToSysNoticeVoMapper__177 extends BaseMapper<SysNotice, SysNoticeVo> {
}
