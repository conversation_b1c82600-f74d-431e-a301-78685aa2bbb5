package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__177;
import org.dromara.system.domain.vo.SysOperLogVo;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysOperLogBoToSysOperLogMapper__177.class,SysOperLogVoToSysOperLogMapper__177.class},
    imports = {}
)
public interface SysOperLogToSysOperLogVoMapper__177 extends BaseMapper<SysOperLog, SysOperLogVo> {
}
