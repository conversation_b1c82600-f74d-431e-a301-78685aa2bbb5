package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOssConfigBoToSysOssConfigMapper__177;
import org.dromara.system.domain.vo.SysOssConfigVo;
import org.dromara.system.domain.vo.SysOssConfigVoToSysOssConfigMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysOssConfigVoToSysOssConfigMapper__177.class,SysOssConfigBoToSysOssConfigMapper__177.class},
    imports = {}
)
public interface SysOssConfigToSysOssConfigVoMapper__177 extends BaseMapper<SysOssConfig, SysOssConfigVo> {
}
