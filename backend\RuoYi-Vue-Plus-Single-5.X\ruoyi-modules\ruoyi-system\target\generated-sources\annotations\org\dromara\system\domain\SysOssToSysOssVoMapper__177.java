package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOssBoToSysOssMapper__177;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.domain.vo.SysOssVoToSysOssMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysOssBoToSysOssMapper__177.class,SysOssVoToSysOssMapper__177.class},
    imports = {}
)
public interface SysOssToSysOssVoMapper__177 extends BaseMapper<SysOss, SysOssVo> {
}
