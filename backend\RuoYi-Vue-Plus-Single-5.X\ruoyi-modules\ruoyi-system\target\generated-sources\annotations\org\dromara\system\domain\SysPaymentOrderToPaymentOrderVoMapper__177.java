package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.PaymentOrderBoToSysPaymentOrderMapper__177;
import org.dromara.system.domain.vo.PaymentOrderVo;
import org.dromara.system.domain.vo.PaymentOrderVoToSysPaymentOrderMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {PaymentOrderBoToSysPaymentOrderMapper__177.class,PaymentOrderVoToSysPaymentOrderMapper__177.class},
    imports = {}
)
public interface SysPaymentOrderToPaymentOrderVoMapper__177 extends BaseMapper<SysPaymentOrder, PaymentOrderVo> {
}
