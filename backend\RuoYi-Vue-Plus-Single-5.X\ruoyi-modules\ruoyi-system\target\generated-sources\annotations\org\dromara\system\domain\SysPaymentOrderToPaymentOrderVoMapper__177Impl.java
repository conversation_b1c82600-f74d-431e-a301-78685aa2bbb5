package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.PaymentOrderVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T11:17:22+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysPaymentOrderToPaymentOrderVoMapper__177Impl implements SysPaymentOrderToPaymentOrderVoMapper__177 {

    @Override
    public PaymentOrderVo convert(SysPaymentOrder arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PaymentOrderVo paymentOrderVo = new PaymentOrderVo();

        paymentOrderVo.setAlipayTradeNo( arg0.getAlipayTradeNo() );
        paymentOrderVo.setAmount( arg0.getAmount() );
        paymentOrderVo.setClientIp( arg0.getClientIp() );
        paymentOrderVo.setCreateTime( arg0.getCreateTime() );
        paymentOrderVo.setExpireTime( arg0.getExpireTime() );
        paymentOrderVo.setLastNotifyTime( arg0.getLastNotifyTime() );
        paymentOrderVo.setNotifyCount( arg0.getNotifyCount() );
        paymentOrderVo.setNotifyResult( arg0.getNotifyResult() );
        paymentOrderVo.setOrderId( arg0.getOrderId() );
        paymentOrderVo.setOrderNo( arg0.getOrderNo() );
        paymentOrderVo.setPayTime( arg0.getPayTime() );
        paymentOrderVo.setPayToken( arg0.getPayToken() );
        paymentOrderVo.setPayTokenExpireTime( arg0.getPayTokenExpireTime() );
        paymentOrderVo.setPayTokenUsed( arg0.getPayTokenUsed() );
        paymentOrderVo.setPaymentMethod( arg0.getPaymentMethod() );
        paymentOrderVo.setProductId( arg0.getProductId() );
        paymentOrderVo.setProductTitle( arg0.getProductTitle() );
        paymentOrderVo.setProductType( arg0.getProductType() );
        paymentOrderVo.setRemark( arg0.getRemark() );
        paymentOrderVo.setStatus( arg0.getStatus() );
        paymentOrderVo.setUpdateTime( arg0.getUpdateTime() );
        paymentOrderVo.setUserAgent( arg0.getUserAgent() );
        paymentOrderVo.setUserId( arg0.getUserId() );

        return paymentOrderVo;
    }

    @Override
    public PaymentOrderVo convert(SysPaymentOrder arg0, PaymentOrderVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAlipayTradeNo( arg0.getAlipayTradeNo() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setClientIp( arg0.getClientIp() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setExpireTime( arg0.getExpireTime() );
        arg1.setLastNotifyTime( arg0.getLastNotifyTime() );
        arg1.setNotifyCount( arg0.getNotifyCount() );
        arg1.setNotifyResult( arg0.getNotifyResult() );
        arg1.setOrderId( arg0.getOrderId() );
        arg1.setOrderNo( arg0.getOrderNo() );
        arg1.setPayTime( arg0.getPayTime() );
        arg1.setPayToken( arg0.getPayToken() );
        arg1.setPayTokenExpireTime( arg0.getPayTokenExpireTime() );
        arg1.setPayTokenUsed( arg0.getPayTokenUsed() );
        arg1.setPaymentMethod( arg0.getPaymentMethod() );
        arg1.setProductId( arg0.getProductId() );
        arg1.setProductTitle( arg0.getProductTitle() );
        arg1.setProductType( arg0.getProductType() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setUserAgent( arg0.getUserAgent() );
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}
