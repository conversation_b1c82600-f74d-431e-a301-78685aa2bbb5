package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysPostBoToSysPostMapper__177;
import org.dromara.system.domain.vo.SysPostVo;
import org.dromara.system.domain.vo.SysPostVoToSysPostMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysPostBoToSysPostMapper__177.class,SysPostVoToSysPostMapper__177.class},
    imports = {}
)
public interface SysPostToSysPostVoMapper__177 extends BaseMapper<SysPost, SysPostVo> {
}
