package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysRoleBoToSysRoleMapper__177;
import org.dromara.system.domain.vo.SysRoleVo;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysRoleVoToSysRoleMapper__177.class,SysRoleBoToSysRoleMapper__177.class},
    imports = {}
)
public interface SysRoleToSysRoleVoMapper__177 extends BaseMapper<SysRole, SysRoleVo> {
}
