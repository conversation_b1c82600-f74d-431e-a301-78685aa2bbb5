package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysSocialBoToSysSocialMapper__177;
import org.dromara.system.domain.vo.SysSocialVo;
import org.dromara.system.domain.vo.SysSocialVoToSysSocialMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysSocialBoToSysSocialMapper__177.class,SysSocialVoToSysSocialMapper__177.class},
    imports = {}
)
public interface SysSocialToSysSocialVoMapper__177 extends BaseMapper<SysSocial, SysSocialVo> {
}
