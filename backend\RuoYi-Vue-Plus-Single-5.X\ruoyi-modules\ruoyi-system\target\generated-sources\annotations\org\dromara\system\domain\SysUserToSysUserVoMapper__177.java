package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserBoToSysUserMapper__177;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__177;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.domain.vo.SysUserVoToSysUserMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysRoleVoToSysRoleMapper__177.class,SysRoleToSysRoleVoMapper__177.class,SysUserVoToSysUserMapper__177.class,SysUserBoToSysUserMapper__177.class},
    imports = {}
)
public interface SysUserToSysUserVoMapper__177 extends BaseMapper<SysUser, SysUserVo> {
}
