package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPaymentOrder;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {},
    imports = {}
)
public interface PaymentOrderBoToSysPaymentOrderMapper__177 extends BaseMapper<PaymentOrderBo, SysPaymentOrder> {
}
