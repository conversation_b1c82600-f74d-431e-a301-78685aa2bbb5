package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysMajor;
import org.dromara.system.domain.SysMajorToMajorVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysMajorToMajorVoMapper__177.class},
    imports = {}
)
public interface MajorVoToSysMajorMapper__177 extends BaseMapper<MajorVo, SysMajor> {
}
