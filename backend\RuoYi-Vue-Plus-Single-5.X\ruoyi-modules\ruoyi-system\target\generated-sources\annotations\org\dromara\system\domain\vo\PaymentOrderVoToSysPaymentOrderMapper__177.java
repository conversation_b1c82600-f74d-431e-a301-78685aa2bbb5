package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPaymentOrder;
import org.dromara.system.domain.SysPaymentOrderToPaymentOrderVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysPaymentOrderToPaymentOrderVoMapper__177.class},
    imports = {}
)
public interface PaymentOrderVoToSysPaymentOrderMapper__177 extends BaseMapper<PaymentOrderVo, SysPaymentOrder> {
}
