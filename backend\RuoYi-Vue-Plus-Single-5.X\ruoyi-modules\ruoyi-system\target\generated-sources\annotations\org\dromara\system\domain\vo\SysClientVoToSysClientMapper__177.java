package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysClient;
import org.dromara.system.domain.SysClientToSysClientVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysClientToSysClientVoMapper__177.class},
    imports = {}
)
public interface SysClientVoToSysClientMapper__177 extends BaseMapper<SysClientVo, SysClient> {
}
