package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysConfig;
import org.dromara.system.domain.SysConfigToSysConfigVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysConfigToSysConfigVoMapper__177.class},
    imports = {}
)
public interface SysConfigVoToSysConfigMapper__177 extends BaseMapper<SysConfigVo, SysConfig> {
}
