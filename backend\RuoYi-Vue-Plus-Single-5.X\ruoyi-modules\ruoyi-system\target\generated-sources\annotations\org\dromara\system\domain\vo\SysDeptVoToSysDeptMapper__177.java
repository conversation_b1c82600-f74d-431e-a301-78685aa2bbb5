package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDept;
import org.dromara.system.domain.SysDeptToSysDeptVoMapper__177;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysDeptBoToSysDeptMapper__177.class,SysDeptToSysDeptVoMapper__177.class,SysDeptToSysDeptVoMapper__177.class},
    imports = {}
)
public interface SysDeptVoToSysDeptMapper__177 extends BaseMapper<SysDeptVo, SysDept> {
}
