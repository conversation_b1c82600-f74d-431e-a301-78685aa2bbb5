package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDictData;
import org.dromara.system.domain.SysDictDataToSysDictDataVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysDictDataToSysDictDataVoMapper__177.class},
    imports = {}
)
public interface SysDictDataVoToSysDictDataMapper__177 extends BaseMapper<SysDictDataVo, SysDictData> {
}
