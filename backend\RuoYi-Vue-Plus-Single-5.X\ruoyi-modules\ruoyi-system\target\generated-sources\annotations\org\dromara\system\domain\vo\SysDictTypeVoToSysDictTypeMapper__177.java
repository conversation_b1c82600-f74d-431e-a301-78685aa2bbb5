package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDictType;
import org.dromara.system.domain.SysDictTypeToSysDictTypeVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysDictTypeToSysDictTypeVoMapper__177.class},
    imports = {}
)
public interface SysDictTypeVoToSysDictTypeMapper__177 extends BaseMapper<SysDictTypeVo, SysDictType> {
}
