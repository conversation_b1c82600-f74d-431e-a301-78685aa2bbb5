package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysLogininfor;
import org.dromara.system.domain.SysLogininforToSysLogininforVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysLogininforToSysLogininforVoMapper__177.class},
    imports = {}
)
public interface SysLogininforVoToSysLogininforMapper__177 extends BaseMapper<SysLogininforVo, SysLogininfor> {
}
