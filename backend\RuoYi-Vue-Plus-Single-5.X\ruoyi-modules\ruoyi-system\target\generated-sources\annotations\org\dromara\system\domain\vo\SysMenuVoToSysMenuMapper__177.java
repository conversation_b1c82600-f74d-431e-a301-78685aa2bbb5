package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysMenu;
import org.dromara.system.domain.SysMenuToSysMenuVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysMenuToSysMenuVoMapper__177.class,SysMenuToSysMenuVoMapper__177.class},
    imports = {}
)
public interface SysMenuVoToSysMenuMapper__177 extends BaseMapper<SysMenuVo, SysMenu> {
}
