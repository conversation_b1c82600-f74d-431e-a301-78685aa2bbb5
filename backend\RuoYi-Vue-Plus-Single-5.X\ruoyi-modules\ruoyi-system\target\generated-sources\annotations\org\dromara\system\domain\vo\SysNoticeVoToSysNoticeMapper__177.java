package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysNotice;
import org.dromara.system.domain.SysNoticeToSysNoticeVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysNoticeToSysNoticeVoMapper__177.class},
    imports = {}
)
public interface SysNoticeVoToSysNoticeMapper__177 extends BaseMapper<SysNoticeVo, SysNotice> {
}
