package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOperLog;
import org.dromara.system.domain.SysOperLogToSysOperLogVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysOperLogToSysOperLogVoMapper__177.class},
    imports = {}
)
public interface SysOperLogVoToSysOperLogMapper__177 extends BaseMapper<SysOperLogVo, SysOperLog> {
}
