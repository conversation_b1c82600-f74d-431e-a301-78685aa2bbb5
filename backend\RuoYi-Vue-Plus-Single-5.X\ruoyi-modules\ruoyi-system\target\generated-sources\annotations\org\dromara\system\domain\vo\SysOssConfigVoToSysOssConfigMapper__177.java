package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOssConfig;
import org.dromara.system.domain.SysOssConfigToSysOssConfigVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysOssConfigToSysOssConfigVoMapper__177.class},
    imports = {}
)
public interface SysOssConfigVoToSysOssConfigMapper__177 extends BaseMapper<SysOssConfigVo, SysOssConfig> {
}
