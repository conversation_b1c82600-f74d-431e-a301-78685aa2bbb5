package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOss;
import org.dromara.system.domain.SysOssToSysOssVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysOssToSysOssVoMapper__177.class},
    imports = {}
)
public interface SysOssVoToSysOssMapper__177 extends BaseMapper<SysOssVo, SysOss> {
}
