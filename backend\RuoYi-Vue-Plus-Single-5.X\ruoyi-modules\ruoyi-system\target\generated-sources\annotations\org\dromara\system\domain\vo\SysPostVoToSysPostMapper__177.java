package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPost;
import org.dromara.system.domain.SysPostToSysPostVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysPostToSysPostVoMapper__177.class},
    imports = {}
)
public interface SysPostVoToSysPostMapper__177 extends BaseMapper<SysPostVo, SysPost> {
}
