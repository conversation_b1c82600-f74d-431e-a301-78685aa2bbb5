package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysRole;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysRoleToSysRoleVoMapper__177.class},
    imports = {}
)
public interface SysRoleVoToSysRoleMapper__177 extends BaseMapper<SysRoleVo, SysRole> {
}
