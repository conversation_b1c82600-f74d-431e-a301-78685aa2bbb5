package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysSocial;
import org.dromara.system.domain.SysSocialToSysSocialVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysSocialToSysSocialVoMapper__177.class},
    imports = {}
)
public interface SysSocialVoToSysSocialMapper__177 extends BaseMapper<SysSocialVo, SysSocial> {
}
