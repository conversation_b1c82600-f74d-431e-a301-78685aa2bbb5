package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__71;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__177;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.SysUserToSysUserVoMapper__177;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__71.class,
    uses = {SysRoleVoToSysRoleMapper__177.class,SysRoleToSysRoleVoMapper__177.class,SysUserToSysUserVoMapper__177.class},
    imports = {}
)
public interface SysUserVoToSysUserMapper__177 extends BaseMapper<SysUserVo, SysUser> {
}
