import {
  __privateAdd,
  __privateMethod,
  __publicField
} from "./chunk-LTFQ5KL6.js";

// node_modules/tailwindcss/dist/chunk-HTB5LLOP.mjs
var l = { inherit: "inherit", current: "currentcolor", transparent: "transparent", black: "#000", white: "#fff", slate: { 50: "oklch(98.4% 0.003 247.858)", 100: "oklch(96.8% 0.007 247.896)", 200: "oklch(92.9% 0.013 255.508)", 300: "oklch(86.9% 0.022 252.894)", 400: "oklch(70.4% 0.04 256.788)", 500: "oklch(55.4% 0.046 257.417)", 600: "oklch(44.6% 0.043 257.281)", 700: "oklch(37.2% 0.044 257.287)", 800: "oklch(27.9% 0.041 260.031)", 900: "oklch(20.8% 0.042 265.755)", 950: "oklch(12.9% 0.042 264.695)" }, gray: { 50: "oklch(98.5% 0.002 247.839)", 100: "oklch(96.7% 0.003 264.542)", 200: "oklch(92.8% 0.006 264.531)", 300: "oklch(87.2% 0.01 258.338)", 400: "oklch(70.7% 0.022 261.325)", 500: "oklch(55.1% 0.027 264.364)", 600: "oklch(44.6% 0.03 256.802)", 700: "oklch(37.3% 0.034 259.733)", 800: "oklch(27.8% 0.033 256.848)", 900: "oklch(21% 0.034 264.665)", 950: "oklch(13% 0.028 261.692)" }, zinc: { 50: "oklch(98.5% 0 0)", 100: "oklch(96.7% 0.001 286.375)", 200: "oklch(92% 0.004 286.32)", 300: "oklch(87.1% 0.006 286.286)", 400: "oklch(70.5% 0.015 286.067)", 500: "oklch(55.2% 0.016 285.938)", 600: "oklch(44.2% 0.017 285.786)", 700: "oklch(37% 0.013 285.805)", 800: "oklch(27.4% 0.006 286.033)", 900: "oklch(21% 0.006 285.885)", 950: "oklch(14.1% 0.005 285.823)" }, neutral: { 50: "oklch(98.5% 0 0)", 100: "oklch(97% 0 0)", 200: "oklch(92.2% 0 0)", 300: "oklch(87% 0 0)", 400: "oklch(70.8% 0 0)", 500: "oklch(55.6% 0 0)", 600: "oklch(43.9% 0 0)", 700: "oklch(37.1% 0 0)", 800: "oklch(26.9% 0 0)", 900: "oklch(20.5% 0 0)", 950: "oklch(14.5% 0 0)" }, stone: { 50: "oklch(98.5% 0.001 106.423)", 100: "oklch(97% 0.001 106.424)", 200: "oklch(92.3% 0.003 48.717)", 300: "oklch(86.9% 0.005 56.366)", 400: "oklch(70.9% 0.01 56.259)", 500: "oklch(55.3% 0.013 58.071)", 600: "oklch(44.4% 0.011 73.639)", 700: "oklch(37.4% 0.01 67.558)", 800: "oklch(26.8% 0.007 34.298)", 900: "oklch(21.6% 0.006 56.043)", 950: "oklch(14.7% 0.004 49.25)" }, red: { 50: "oklch(97.1% 0.013 17.38)", 100: "oklch(93.6% 0.032 17.717)", 200: "oklch(88.5% 0.062 18.334)", 300: "oklch(80.8% 0.114 19.571)", 400: "oklch(70.4% 0.191 22.216)", 500: "oklch(63.7% 0.237 25.331)", 600: "oklch(57.7% 0.245 27.325)", 700: "oklch(50.5% 0.213 27.518)", 800: "oklch(44.4% 0.177 26.899)", 900: "oklch(39.6% 0.141 25.723)", 950: "oklch(25.8% 0.092 26.042)" }, orange: { 50: "oklch(98% 0.016 73.684)", 100: "oklch(95.4% 0.038 75.164)", 200: "oklch(90.1% 0.076 70.697)", 300: "oklch(83.7% 0.128 66.29)", 400: "oklch(75% 0.183 55.934)", 500: "oklch(70.5% 0.213 47.604)", 600: "oklch(64.6% 0.222 41.116)", 700: "oklch(55.3% 0.195 38.402)", 800: "oklch(47% 0.157 37.304)", 900: "oklch(40.8% 0.123 38.172)", 950: "oklch(26.6% 0.079 36.259)" }, amber: { 50: "oklch(98.7% 0.022 95.277)", 100: "oklch(96.2% 0.059 95.617)", 200: "oklch(92.4% 0.12 95.746)", 300: "oklch(87.9% 0.169 91.605)", 400: "oklch(82.8% 0.189 84.429)", 500: "oklch(76.9% 0.188 70.08)", 600: "oklch(66.6% 0.179 58.318)", 700: "oklch(55.5% 0.163 48.998)", 800: "oklch(47.3% 0.137 46.201)", 900: "oklch(41.4% 0.112 45.904)", 950: "oklch(27.9% 0.077 45.635)" }, yellow: { 50: "oklch(98.7% 0.026 102.212)", 100: "oklch(97.3% 0.071 103.193)", 200: "oklch(94.5% 0.129 101.54)", 300: "oklch(90.5% 0.182 98.111)", 400: "oklch(85.2% 0.199 91.936)", 500: "oklch(79.5% 0.184 86.047)", 600: "oklch(68.1% 0.162 75.834)", 700: "oklch(55.4% 0.135 66.442)", 800: "oklch(47.6% 0.114 61.907)", 900: "oklch(42.1% 0.095 57.708)", 950: "oklch(28.6% 0.066 53.813)" }, lime: { 50: "oklch(98.6% 0.031 120.757)", 100: "oklch(96.7% 0.067 122.328)", 200: "oklch(93.8% 0.127 124.321)", 300: "oklch(89.7% 0.196 126.665)", 400: "oklch(84.1% 0.238 128.85)", 500: "oklch(76.8% 0.233 130.85)", 600: "oklch(64.8% 0.2 131.684)", 700: "oklch(53.2% 0.157 131.589)", 800: "oklch(45.3% 0.124 130.933)", 900: "oklch(40.5% 0.101 131.063)", 950: "oklch(27.4% 0.072 132.109)" }, green: { 50: "oklch(98.2% 0.018 155.826)", 100: "oklch(96.2% 0.044 156.743)", 200: "oklch(92.5% 0.084 155.995)", 300: "oklch(87.1% 0.15 154.449)", 400: "oklch(79.2% 0.209 151.711)", 500: "oklch(72.3% 0.219 149.579)", 600: "oklch(62.7% 0.194 149.214)", 700: "oklch(52.7% 0.154 150.069)", 800: "oklch(44.8% 0.119 151.328)", 900: "oklch(39.3% 0.095 152.535)", 950: "oklch(26.6% 0.065 152.934)" }, emerald: { 50: "oklch(97.9% 0.021 166.113)", 100: "oklch(95% 0.052 163.051)", 200: "oklch(90.5% 0.093 164.15)", 300: "oklch(84.5% 0.143 164.978)", 400: "oklch(76.5% 0.177 163.223)", 500: "oklch(69.6% 0.17 162.48)", 600: "oklch(59.6% 0.145 163.225)", 700: "oklch(50.8% 0.118 165.612)", 800: "oklch(43.2% 0.095 166.913)", 900: "oklch(37.8% 0.077 168.94)", 950: "oklch(26.2% 0.051 172.552)" }, teal: { 50: "oklch(98.4% 0.014 180.72)", 100: "oklch(95.3% 0.051 180.801)", 200: "oklch(91% 0.096 180.426)", 300: "oklch(85.5% 0.138 181.071)", 400: "oklch(77.7% 0.152 181.912)", 500: "oklch(70.4% 0.14 182.503)", 600: "oklch(60% 0.118 184.704)", 700: "oklch(51.1% 0.096 186.391)", 800: "oklch(43.7% 0.078 188.216)", 900: "oklch(38.6% 0.063 188.416)", 950: "oklch(27.7% 0.046 192.524)" }, cyan: { 50: "oklch(98.4% 0.019 200.873)", 100: "oklch(95.6% 0.045 203.388)", 200: "oklch(91.7% 0.08 205.041)", 300: "oklch(86.5% 0.127 207.078)", 400: "oklch(78.9% 0.154 211.53)", 500: "oklch(71.5% 0.143 215.221)", 600: "oklch(60.9% 0.126 221.723)", 700: "oklch(52% 0.105 223.128)", 800: "oklch(45% 0.085 224.283)", 900: "oklch(39.8% 0.07 227.392)", 950: "oklch(30.2% 0.056 229.695)" }, sky: { 50: "oklch(97.7% 0.013 236.62)", 100: "oklch(95.1% 0.026 236.824)", 200: "oklch(90.1% 0.058 230.902)", 300: "oklch(82.8% 0.111 230.318)", 400: "oklch(74.6% 0.16 232.661)", 500: "oklch(68.5% 0.169 237.323)", 600: "oklch(58.8% 0.158 241.966)", 700: "oklch(50% 0.134 242.749)", 800: "oklch(44.3% 0.11 240.79)", 900: "oklch(39.1% 0.09 240.876)", 950: "oklch(29.3% 0.066 243.157)" }, blue: { 50: "oklch(97% 0.014 254.604)", 100: "oklch(93.2% 0.032 255.585)", 200: "oklch(88.2% 0.059 254.128)", 300: "oklch(80.9% 0.105 251.813)", 400: "oklch(70.7% 0.165 254.624)", 500: "oklch(62.3% 0.214 259.815)", 600: "oklch(54.6% 0.245 262.881)", 700: "oklch(48.8% 0.243 264.376)", 800: "oklch(42.4% 0.199 265.638)", 900: "oklch(37.9% 0.146 265.522)", 950: "oklch(28.2% 0.091 267.935)" }, indigo: { 50: "oklch(96.2% 0.018 272.314)", 100: "oklch(93% 0.034 272.788)", 200: "oklch(87% 0.065 274.039)", 300: "oklch(78.5% 0.115 274.713)", 400: "oklch(67.3% 0.182 276.935)", 500: "oklch(58.5% 0.233 277.117)", 600: "oklch(51.1% 0.262 276.966)", 700: "oklch(45.7% 0.24 277.023)", 800: "oklch(39.8% 0.195 277.366)", 900: "oklch(35.9% 0.144 278.697)", 950: "oklch(25.7% 0.09 281.288)" }, violet: { 50: "oklch(96.9% 0.016 293.756)", 100: "oklch(94.3% 0.029 294.588)", 200: "oklch(89.4% 0.057 293.283)", 300: "oklch(81.1% 0.111 293.571)", 400: "oklch(70.2% 0.183 293.541)", 500: "oklch(60.6% 0.25 292.717)", 600: "oklch(54.1% 0.281 293.009)", 700: "oklch(49.1% 0.27 292.581)", 800: "oklch(43.2% 0.232 292.759)", 900: "oklch(38% 0.189 293.745)", 950: "oklch(28.3% 0.141 291.089)" }, purple: { 50: "oklch(97.7% 0.014 308.299)", 100: "oklch(94.6% 0.033 307.174)", 200: "oklch(90.2% 0.063 306.703)", 300: "oklch(82.7% 0.119 306.383)", 400: "oklch(71.4% 0.203 305.504)", 500: "oklch(62.7% 0.265 303.9)", 600: "oklch(55.8% 0.288 302.321)", 700: "oklch(49.6% 0.265 301.924)", 800: "oklch(43.8% 0.218 303.724)", 900: "oklch(38.1% 0.176 304.987)", 950: "oklch(29.1% 0.149 302.717)" }, fuchsia: { 50: "oklch(97.7% 0.017 320.058)", 100: "oklch(95.2% 0.037 318.852)", 200: "oklch(90.3% 0.076 319.62)", 300: "oklch(83.3% 0.145 321.434)", 400: "oklch(74% 0.238 322.16)", 500: "oklch(66.7% 0.295 322.15)", 600: "oklch(59.1% 0.293 322.896)", 700: "oklch(51.8% 0.253 323.949)", 800: "oklch(45.2% 0.211 324.591)", 900: "oklch(40.1% 0.17 325.612)", 950: "oklch(29.3% 0.136 325.661)" }, pink: { 50: "oklch(97.1% 0.014 343.198)", 100: "oklch(94.8% 0.028 342.258)", 200: "oklch(89.9% 0.061 343.231)", 300: "oklch(82.3% 0.12 346.018)", 400: "oklch(71.8% 0.202 349.761)", 500: "oklch(65.6% 0.241 354.308)", 600: "oklch(59.2% 0.249 0.584)", 700: "oklch(52.5% 0.223 3.958)", 800: "oklch(45.9% 0.187 3.815)", 900: "oklch(40.8% 0.153 2.432)", 950: "oklch(28.4% 0.109 3.907)" }, rose: { 50: "oklch(96.9% 0.015 12.422)", 100: "oklch(94.1% 0.03 12.58)", 200: "oklch(89.2% 0.058 10.001)", 300: "oklch(81% 0.117 11.638)", 400: "oklch(71.2% 0.194 13.428)", 500: "oklch(64.5% 0.246 16.439)", 600: "oklch(58.6% 0.253 17.585)", 700: "oklch(51.4% 0.222 16.935)", 800: "oklch(45.5% 0.188 13.697)", 900: "oklch(41% 0.159 10.272)", 950: "oklch(27.1% 0.105 12.094)" } };

// node_modules/tailwindcss/dist/chunk-P5FH2LZE.mjs
var O = /* @__PURE__ */ new Set(["black", "silver", "gray", "white", "maroon", "red", "purple", "fuchsia", "green", "lime", "olive", "yellow", "navy", "blue", "teal", "aqua", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanchedalmond", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "darkcyan", "darkgoldenrod", "darkgray", "darkgreen", "darkgrey", "darkkhaki", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "darksalmon", "darkseagreen", "darkslateblue", "darkslategray", "darkslategrey", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "dimgrey", "dodgerblue", "firebrick", "floralwhite", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemonchiffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "lightgrey", "lightpink", "lightsalmon", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "olivedrab", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "slategrey", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "transparent", "currentcolor", "canvas", "canvastext", "linktext", "visitedtext", "activetext", "buttonface", "buttontext", "buttonborder", "field", "fieldtext", "highlight", "highlighttext", "selecteditem", "selecteditemtext", "mark", "marktext", "graytext", "accentcolor", "accentcolortext"]);
var R = /^(rgba?|hsla?|hwb|color|(ok)?(lab|lch)|light-dark|color-mix)\(/i;
function v(e) {
  return e.charCodeAt(0) === 35 || R.test(e) || O.has(e.toLowerCase());
}
var E = ["calc", "min", "max", "clamp", "mod", "rem", "sin", "cos", "tan", "asin", "acos", "atan", "atan2", "pow", "sqrt", "hypot", "log", "exp", "round"];
var h = ["anchor-size"];
var A = new RegExp(`(${h.join("|")})\\(`, "g");
function b(e) {
  return e.indexOf("(") !== -1 && E.some((r) => e.includes(`${r}(`));
}
function ie(e) {
  if (!E.some((n) => e.includes(n)))
    return e;
  let r = false;
  h.some((n) => e.includes(n)) && (A.lastIndex = 0, e = e.replace(A, (n, o) => (r = true, `$${h.indexOf(o)}$(`)));
  let t = "", i = [];
  for (let n = 0; n < e.length; n++) {
    let o = e[n];
    if (o === "(") {
      t += o;
      let m = n;
      for (let c = n - 1; c >= 0; c--) {
        let x = e.charCodeAt(c);
        if (x >= 48 && x <= 57)
          m = c;
        else if (x >= 97 && x <= 122)
          m = c;
        else
          break;
      }
      let a = e.slice(m, n);
      if (E.includes(a)) {
        i.unshift(true);
        continue;
      } else if (i[0] && a === "") {
        i.unshift(true);
        continue;
      }
      i.unshift(false);
      continue;
    } else if (o === ")")
      t += o, i.shift();
    else if (o === "," && i[0]) {
      t += ", ";
      continue;
    } else {
      if (o === " " && i[0] && t[t.length - 1] === " ")
        continue;
      if ((o === "+" || o === "*" || o === "/" || o === "-") && i[0]) {
        let m = t.trimEnd(), a = m[m.length - 1];
        if (a === "+" || a === "*" || a === "/" || a === "-") {
          t += o;
          continue;
        } else if (a === "(" || a === ",") {
          t += o;
          continue;
        } else
          e[n - 1] === " " ? t += `${o} ` : t += ` ${o} `;
      } else if (i[0] && e.startsWith("to-zero", n)) {
        let m = n;
        n += 7, t += e.slice(m, n + 1);
      } else
        t += o;
    }
  }
  return r ? t.replace(/\$(\d+)\$/g, (n, o) => h[o] ?? n) : t;
}
var y = new Uint8Array(256);
function g(e, r) {
  let t = 0, i = [], n = 0, o = e.length, m = r.charCodeAt(0);
  for (let a = 0; a < o; a++) {
    let c = e.charCodeAt(a);
    if (t === 0 && c === m) {
      i.push(e.slice(n, a)), n = a + 1;
      continue;
    }
    switch (c) {
      case 92:
        a += 1;
        break;
      case 39:
      case 34:
        for (; ++a < o; ) {
          let x = e.charCodeAt(a);
          if (x === 92) {
            a += 1;
            continue;
          }
          if (x === c)
            break;
        }
        break;
      case 40:
        y[t] = 41, t++;
        break;
      case 91:
        y[t] = 93, t++;
        break;
      case 123:
        y[t] = 125, t++;
        break;
      case 93:
      case 125:
      case 41:
        t > 0 && c === y[t - 1] && t--;
        break;
    }
  }
  return i.push(e.slice(n)), i;
}
var _ = { color: v, length: w, percentage: C, ratio: j, number: N, integer: p, url: z, position: Q, "bg-size": X, "line-width": I, image: H, "family-name": q, "generic-name": P, "absolute-size": B, "relative-size": W, angle: ee, vector: re };
function pe(e, r) {
  var _a2;
  if (e.startsWith("var("))
    return null;
  for (let t of r)
    if ((_a2 = _[t]) == null ? void 0 : _a2.call(_, e))
      return t;
  return null;
}
var D = /^url\(.*\)$/;
function z(e) {
  return D.test(e);
}
function I(e) {
  return g(e, " ").every((r) => w(r) || N(r) || r === "thin" || r === "medium" || r === "thick");
}
var F = /^(?:element|image|cross-fade|image-set)\(/;
var $ = /^(repeating-)?(conic|linear|radial)-gradient\(/;
function H(e) {
  let r = 0;
  for (let t of g(e, ","))
    if (!t.startsWith("var(")) {
      if (z(t)) {
        r += 1;
        continue;
      }
      if ($.test(t)) {
        r += 1;
        continue;
      }
      if (F.test(t)) {
        r += 1;
        continue;
      }
      return false;
    }
  return r > 0;
}
function P(e) {
  return e === "serif" || e === "sans-serif" || e === "monospace" || e === "cursive" || e === "fantasy" || e === "system-ui" || e === "ui-serif" || e === "ui-sans-serif" || e === "ui-monospace" || e === "ui-rounded" || e === "math" || e === "emoji" || e === "fangsong";
}
function q(e) {
  let r = 0;
  for (let t of g(e, ",")) {
    let i = t.charCodeAt(0);
    if (i >= 48 && i <= 57)
      return false;
    t.startsWith("var(") || (r += 1);
  }
  return r > 0;
}
function B(e) {
  return e === "xx-small" || e === "x-small" || e === "small" || e === "medium" || e === "large" || e === "x-large" || e === "xx-large" || e === "xxx-large";
}
function W(e) {
  return e === "larger" || e === "smaller";
}
var u = /[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/;
var M = new RegExp(`^${u.source}$`);
function N(e) {
  return M.test(e) || b(e);
}
var G = new RegExp(`^${u.source}%$`);
function C(e) {
  return G.test(e) || b(e);
}
var V = new RegExp(`^${u.source}s*/s*${u.source}$`);
function j(e) {
  return V.test(e) || b(e);
}
var K = ["cm", "mm", "Q", "in", "pc", "pt", "px", "em", "ex", "ch", "rem", "lh", "rlh", "vw", "vh", "vmin", "vmax", "vb", "vi", "svw", "svh", "lvw", "lvh", "dvw", "dvh", "cqw", "cqh", "cqi", "cqb", "cqmin", "cqmax"];
var Y = new RegExp(`^${u.source}(${K.join("|")})$`);
function w(e) {
  return Y.test(e) || b(e);
}
function Q(e) {
  let r = 0;
  for (let t of g(e, " ")) {
    if (t === "center" || t === "top" || t === "right" || t === "bottom" || t === "left") {
      r += 1;
      continue;
    }
    if (!t.startsWith("var(")) {
      if (w(t) || C(t)) {
        r += 1;
        continue;
      }
      return false;
    }
  }
  return r > 0;
}
function X(e) {
  let r = 0;
  for (let t of g(e, ",")) {
    if (t === "cover" || t === "contain") {
      r += 1;
      continue;
    }
    let i = g(t, " ");
    if (i.length !== 1 && i.length !== 2)
      return false;
    if (i.every((n) => n === "auto" || w(n) || C(n))) {
      r += 1;
      continue;
    }
  }
  return r > 0;
}
var J = ["deg", "rad", "grad", "turn"];
var Z = new RegExp(`^${u.source}(${J.join("|")})$`);
function ee(e) {
  return Z.test(e);
}
var te = new RegExp(`^${u.source} +${u.source} +${u.source}$`);
function re(e) {
  return te.test(e);
}
function p(e) {
  let r = Number(e);
  return Number.isInteger(r) && r >= 0 && String(r) === String(e);
}
function ge(e) {
  let r = Number(e);
  return Number.isInteger(r) && r > 0 && String(r) === String(e);
}
function ue(e) {
  return T(e, 0.25);
}
function de(e) {
  return T(e, 0.25);
}
function T(e, r) {
  let t = Number(e);
  return t >= 0 && t % r === 0 && String(t) === String(e);
}
function f(e) {
  return { __BARE_VALUE__: e };
}
var l2 = f((e) => {
  if (p(e.value))
    return e.value;
});
var s = f((e) => {
  if (p(e.value))
    return `${e.value}%`;
});
var d = f((e) => {
  if (p(e.value))
    return `${e.value}px`;
});
var L = f((e) => {
  if (p(e.value))
    return `${e.value}ms`;
});
var k = f((e) => {
  if (p(e.value))
    return `${e.value}deg`;
});
var ne = f((e) => {
  if (e.fraction === null)
    return;
  let [r, t] = g(e.fraction, "/");
  if (!(!p(r) || !p(t)))
    return e.fraction;
});
var U = f((e) => {
  if (p(Number(e.value)))
    return `repeat(${e.value}, minmax(0, 1fr))`;
});
var ye = { accentColor: ({ theme: e }) => e("colors"), animation: { none: "none", spin: "spin 1s linear infinite", ping: "ping 1s cubic-bezier(0, 0, 0.2, 1) infinite", pulse: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite", bounce: "bounce 1s infinite" }, aria: { busy: 'busy="true"', checked: 'checked="true"', disabled: 'disabled="true"', expanded: 'expanded="true"', hidden: 'hidden="true"', pressed: 'pressed="true"', readonly: 'readonly="true"', required: 'required="true"', selected: 'selected="true"' }, aspectRatio: { auto: "auto", square: "1 / 1", video: "16 / 9", ...ne }, backdropBlur: ({ theme: e }) => e("blur"), backdropBrightness: ({ theme: e }) => ({ ...e("brightness"), ...s }), backdropContrast: ({ theme: e }) => ({ ...e("contrast"), ...s }), backdropGrayscale: ({ theme: e }) => ({ ...e("grayscale"), ...s }), backdropHueRotate: ({ theme: e }) => ({ ...e("hueRotate"), ...k }), backdropInvert: ({ theme: e }) => ({ ...e("invert"), ...s }), backdropOpacity: ({ theme: e }) => ({ ...e("opacity"), ...s }), backdropSaturate: ({ theme: e }) => ({ ...e("saturate"), ...s }), backdropSepia: ({ theme: e }) => ({ ...e("sepia"), ...s }), backgroundColor: ({ theme: e }) => e("colors"), backgroundImage: { none: "none", "gradient-to-t": "linear-gradient(to top, var(--tw-gradient-stops))", "gradient-to-tr": "linear-gradient(to top right, var(--tw-gradient-stops))", "gradient-to-r": "linear-gradient(to right, var(--tw-gradient-stops))", "gradient-to-br": "linear-gradient(to bottom right, var(--tw-gradient-stops))", "gradient-to-b": "linear-gradient(to bottom, var(--tw-gradient-stops))", "gradient-to-bl": "linear-gradient(to bottom left, var(--tw-gradient-stops))", "gradient-to-l": "linear-gradient(to left, var(--tw-gradient-stops))", "gradient-to-tl": "linear-gradient(to top left, var(--tw-gradient-stops))" }, backgroundOpacity: ({ theme: e }) => e("opacity"), backgroundPosition: { bottom: "bottom", center: "center", left: "left", "left-bottom": "left bottom", "left-top": "left top", right: "right", "right-bottom": "right bottom", "right-top": "right top", top: "top" }, backgroundSize: { auto: "auto", cover: "cover", contain: "contain" }, blur: { 0: "0", none: "", sm: "4px", DEFAULT: "8px", md: "12px", lg: "16px", xl: "24px", "2xl": "40px", "3xl": "64px" }, borderColor: ({ theme: e }) => ({ DEFAULT: "currentcolor", ...e("colors") }), borderOpacity: ({ theme: e }) => e("opacity"), borderRadius: { none: "0px", sm: "0.125rem", DEFAULT: "0.25rem", md: "0.375rem", lg: "0.5rem", xl: "0.75rem", "2xl": "1rem", "3xl": "1.5rem", full: "9999px" }, borderSpacing: ({ theme: e }) => e("spacing"), borderWidth: { DEFAULT: "1px", 0: "0px", 2: "2px", 4: "4px", 8: "8px", ...d }, boxShadow: { sm: "0 1px 2px 0 rgb(0 0 0 / 0.05)", DEFAULT: "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)", md: "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)", lg: "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)", xl: "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)", "2xl": "0 25px 50px -12px rgb(0 0 0 / 0.25)", inner: "inset 0 2px 4px 0 rgb(0 0 0 / 0.05)", none: "none" }, boxShadowColor: ({ theme: e }) => e("colors"), brightness: { 0: "0", 50: ".5", 75: ".75", 90: ".9", 95: ".95", 100: "1", 105: "1.05", 110: "1.1", 125: "1.25", 150: "1.5", 200: "2", ...s }, caretColor: ({ theme: e }) => e("colors"), colors: () => ({ ...l }), columns: { auto: "auto", 1: "1", 2: "2", 3: "3", 4: "4", 5: "5", 6: "6", 7: "7", 8: "8", 9: "9", 10: "10", 11: "11", 12: "12", "3xs": "16rem", "2xs": "18rem", xs: "20rem", sm: "24rem", md: "28rem", lg: "32rem", xl: "36rem", "2xl": "42rem", "3xl": "48rem", "4xl": "56rem", "5xl": "64rem", "6xl": "72rem", "7xl": "80rem", ...l2 }, container: {}, content: { none: "none" }, contrast: { 0: "0", 50: ".5", 75: ".75", 100: "1", 125: "1.25", 150: "1.5", 200: "2", ...s }, cursor: { auto: "auto", default: "default", pointer: "pointer", wait: "wait", text: "text", move: "move", help: "help", "not-allowed": "not-allowed", none: "none", "context-menu": "context-menu", progress: "progress", cell: "cell", crosshair: "crosshair", "vertical-text": "vertical-text", alias: "alias", copy: "copy", "no-drop": "no-drop", grab: "grab", grabbing: "grabbing", "all-scroll": "all-scroll", "col-resize": "col-resize", "row-resize": "row-resize", "n-resize": "n-resize", "e-resize": "e-resize", "s-resize": "s-resize", "w-resize": "w-resize", "ne-resize": "ne-resize", "nw-resize": "nw-resize", "se-resize": "se-resize", "sw-resize": "sw-resize", "ew-resize": "ew-resize", "ns-resize": "ns-resize", "nesw-resize": "nesw-resize", "nwse-resize": "nwse-resize", "zoom-in": "zoom-in", "zoom-out": "zoom-out" }, divideColor: ({ theme: e }) => e("borderColor"), divideOpacity: ({ theme: e }) => e("borderOpacity"), divideWidth: ({ theme: e }) => ({ ...e("borderWidth"), ...d }), dropShadow: { sm: "0 1px 1px rgb(0 0 0 / 0.05)", DEFAULT: ["0 1px 2px rgb(0 0 0 / 0.1)", "0 1px 1px rgb(0 0 0 / 0.06)"], md: ["0 4px 3px rgb(0 0 0 / 0.07)", "0 2px 2px rgb(0 0 0 / 0.06)"], lg: ["0 10px 8px rgb(0 0 0 / 0.04)", "0 4px 3px rgb(0 0 0 / 0.1)"], xl: ["0 20px 13px rgb(0 0 0 / 0.03)", "0 8px 5px rgb(0 0 0 / 0.08)"], "2xl": "0 25px 25px rgb(0 0 0 / 0.15)", none: "0 0 #0000" }, fill: ({ theme: e }) => e("colors"), flex: { 1: "1 1 0%", auto: "1 1 auto", initial: "0 1 auto", none: "none" }, flexBasis: ({ theme: e }) => ({ auto: "auto", "1/2": "50%", "1/3": "33.333333%", "2/3": "66.666667%", "1/4": "25%", "2/4": "50%", "3/4": "75%", "1/5": "20%", "2/5": "40%", "3/5": "60%", "4/5": "80%", "1/6": "16.666667%", "2/6": "33.333333%", "3/6": "50%", "4/6": "66.666667%", "5/6": "83.333333%", "1/12": "8.333333%", "2/12": "16.666667%", "3/12": "25%", "4/12": "33.333333%", "5/12": "41.666667%", "6/12": "50%", "7/12": "58.333333%", "8/12": "66.666667%", "9/12": "75%", "10/12": "83.333333%", "11/12": "91.666667%", full: "100%", ...e("spacing") }), flexGrow: { 0: "0", DEFAULT: "1", ...l2 }, flexShrink: { 0: "0", DEFAULT: "1", ...l2 }, fontFamily: { sans: ["ui-sans-serif", "system-ui", "sans-serif", '"Apple Color Emoji"', '"Segoe UI Emoji"', '"Segoe UI Symbol"', '"Noto Color Emoji"'], serif: ["ui-serif", "Georgia", "Cambria", '"Times New Roman"', "Times", "serif"], mono: ["ui-monospace", "SFMono-Regular", "Menlo", "Monaco", "Consolas", '"Liberation Mono"', '"Courier New"', "monospace"] }, fontSize: { xs: ["0.75rem", { lineHeight: "1rem" }], sm: ["0.875rem", { lineHeight: "1.25rem" }], base: ["1rem", { lineHeight: "1.5rem" }], lg: ["1.125rem", { lineHeight: "1.75rem" }], xl: ["1.25rem", { lineHeight: "1.75rem" }], "2xl": ["1.5rem", { lineHeight: "2rem" }], "3xl": ["1.875rem", { lineHeight: "2.25rem" }], "4xl": ["2.25rem", { lineHeight: "2.5rem" }], "5xl": ["3rem", { lineHeight: "1" }], "6xl": ["3.75rem", { lineHeight: "1" }], "7xl": ["4.5rem", { lineHeight: "1" }], "8xl": ["6rem", { lineHeight: "1" }], "9xl": ["8rem", { lineHeight: "1" }] }, fontWeight: { thin: "100", extralight: "200", light: "300", normal: "400", medium: "500", semibold: "600", bold: "700", extrabold: "800", black: "900" }, gap: ({ theme: e }) => e("spacing"), gradientColorStops: ({ theme: e }) => e("colors"), gradientColorStopPositions: { "0%": "0%", "5%": "5%", "10%": "10%", "15%": "15%", "20%": "20%", "25%": "25%", "30%": "30%", "35%": "35%", "40%": "40%", "45%": "45%", "50%": "50%", "55%": "55%", "60%": "60%", "65%": "65%", "70%": "70%", "75%": "75%", "80%": "80%", "85%": "85%", "90%": "90%", "95%": "95%", "100%": "100%", ...s }, grayscale: { 0: "0", DEFAULT: "100%", ...s }, gridAutoColumns: { auto: "auto", min: "min-content", max: "max-content", fr: "minmax(0, 1fr)" }, gridAutoRows: { auto: "auto", min: "min-content", max: "max-content", fr: "minmax(0, 1fr)" }, gridColumn: { auto: "auto", "span-1": "span 1 / span 1", "span-2": "span 2 / span 2", "span-3": "span 3 / span 3", "span-4": "span 4 / span 4", "span-5": "span 5 / span 5", "span-6": "span 6 / span 6", "span-7": "span 7 / span 7", "span-8": "span 8 / span 8", "span-9": "span 9 / span 9", "span-10": "span 10 / span 10", "span-11": "span 11 / span 11", "span-12": "span 12 / span 12", "span-full": "1 / -1" }, gridColumnEnd: { auto: "auto", 1: "1", 2: "2", 3: "3", 4: "4", 5: "5", 6: "6", 7: "7", 8: "8", 9: "9", 10: "10", 11: "11", 12: "12", 13: "13", ...l2 }, gridColumnStart: { auto: "auto", 1: "1", 2: "2", 3: "3", 4: "4", 5: "5", 6: "6", 7: "7", 8: "8", 9: "9", 10: "10", 11: "11", 12: "12", 13: "13", ...l2 }, gridRow: { auto: "auto", "span-1": "span 1 / span 1", "span-2": "span 2 / span 2", "span-3": "span 3 / span 3", "span-4": "span 4 / span 4", "span-5": "span 5 / span 5", "span-6": "span 6 / span 6", "span-7": "span 7 / span 7", "span-8": "span 8 / span 8", "span-9": "span 9 / span 9", "span-10": "span 10 / span 10", "span-11": "span 11 / span 11", "span-12": "span 12 / span 12", "span-full": "1 / -1" }, gridRowEnd: { auto: "auto", 1: "1", 2: "2", 3: "3", 4: "4", 5: "5", 6: "6", 7: "7", 8: "8", 9: "9", 10: "10", 11: "11", 12: "12", 13: "13", ...l2 }, gridRowStart: { auto: "auto", 1: "1", 2: "2", 3: "3", 4: "4", 5: "5", 6: "6", 7: "7", 8: "8", 9: "9", 10: "10", 11: "11", 12: "12", 13: "13", ...l2 }, gridTemplateColumns: { none: "none", subgrid: "subgrid", 1: "repeat(1, minmax(0, 1fr))", 2: "repeat(2, minmax(0, 1fr))", 3: "repeat(3, minmax(0, 1fr))", 4: "repeat(4, minmax(0, 1fr))", 5: "repeat(5, minmax(0, 1fr))", 6: "repeat(6, minmax(0, 1fr))", 7: "repeat(7, minmax(0, 1fr))", 8: "repeat(8, minmax(0, 1fr))", 9: "repeat(9, minmax(0, 1fr))", 10: "repeat(10, minmax(0, 1fr))", 11: "repeat(11, minmax(0, 1fr))", 12: "repeat(12, minmax(0, 1fr))", ...U }, gridTemplateRows: { none: "none", subgrid: "subgrid", 1: "repeat(1, minmax(0, 1fr))", 2: "repeat(2, minmax(0, 1fr))", 3: "repeat(3, minmax(0, 1fr))", 4: "repeat(4, minmax(0, 1fr))", 5: "repeat(5, minmax(0, 1fr))", 6: "repeat(6, minmax(0, 1fr))", 7: "repeat(7, minmax(0, 1fr))", 8: "repeat(8, minmax(0, 1fr))", 9: "repeat(9, minmax(0, 1fr))", 10: "repeat(10, minmax(0, 1fr))", 11: "repeat(11, minmax(0, 1fr))", 12: "repeat(12, minmax(0, 1fr))", ...U }, height: ({ theme: e }) => ({ auto: "auto", "1/2": "50%", "1/3": "33.333333%", "2/3": "66.666667%", "1/4": "25%", "2/4": "50%", "3/4": "75%", "1/5": "20%", "2/5": "40%", "3/5": "60%", "4/5": "80%", "1/6": "16.666667%", "2/6": "33.333333%", "3/6": "50%", "4/6": "66.666667%", "5/6": "83.333333%", full: "100%", screen: "100vh", svh: "100svh", lvh: "100lvh", dvh: "100dvh", min: "min-content", max: "max-content", fit: "fit-content", ...e("spacing") }), hueRotate: { 0: "0deg", 15: "15deg", 30: "30deg", 60: "60deg", 90: "90deg", 180: "180deg", ...k }, inset: ({ theme: e }) => ({ auto: "auto", "1/2": "50%", "1/3": "33.333333%", "2/3": "66.666667%", "1/4": "25%", "2/4": "50%", "3/4": "75%", full: "100%", ...e("spacing") }), invert: { 0: "0", DEFAULT: "100%", ...s }, keyframes: { spin: { to: { transform: "rotate(360deg)" } }, ping: { "75%, 100%": { transform: "scale(2)", opacity: "0" } }, pulse: { "50%": { opacity: ".5" } }, bounce: { "0%, 100%": { transform: "translateY(-25%)", animationTimingFunction: "cubic-bezier(0.8,0,1,1)" }, "50%": { transform: "none", animationTimingFunction: "cubic-bezier(0,0,0.2,1)" } } }, letterSpacing: { tighter: "-0.05em", tight: "-0.025em", normal: "0em", wide: "0.025em", wider: "0.05em", widest: "0.1em" }, lineHeight: { none: "1", tight: "1.25", snug: "1.375", normal: "1.5", relaxed: "1.625", loose: "2", 3: ".75rem", 4: "1rem", 5: "1.25rem", 6: "1.5rem", 7: "1.75rem", 8: "2rem", 9: "2.25rem", 10: "2.5rem" }, listStyleType: { none: "none", disc: "disc", decimal: "decimal" }, listStyleImage: { none: "none" }, margin: ({ theme: e }) => ({ auto: "auto", ...e("spacing") }), lineClamp: { 1: "1", 2: "2", 3: "3", 4: "4", 5: "5", 6: "6", ...l2 }, maxHeight: ({ theme: e }) => ({ none: "none", full: "100%", screen: "100vh", svh: "100svh", lvh: "100lvh", dvh: "100dvh", min: "min-content", max: "max-content", fit: "fit-content", ...e("spacing") }), maxWidth: ({ theme: e }) => ({ none: "none", xs: "20rem", sm: "24rem", md: "28rem", lg: "32rem", xl: "36rem", "2xl": "42rem", "3xl": "48rem", "4xl": "56rem", "5xl": "64rem", "6xl": "72rem", "7xl": "80rem", full: "100%", min: "min-content", max: "max-content", fit: "fit-content", prose: "65ch", ...e("spacing") }), minHeight: ({ theme: e }) => ({ full: "100%", screen: "100vh", svh: "100svh", lvh: "100lvh", dvh: "100dvh", min: "min-content", max: "max-content", fit: "fit-content", ...e("spacing") }), minWidth: ({ theme: e }) => ({ full: "100%", min: "min-content", max: "max-content", fit: "fit-content", ...e("spacing") }), objectPosition: { bottom: "bottom", center: "center", left: "left", "left-bottom": "left bottom", "left-top": "left top", right: "right", "right-bottom": "right bottom", "right-top": "right top", top: "top" }, opacity: { 0: "0", 5: "0.05", 10: "0.1", 15: "0.15", 20: "0.2", 25: "0.25", 30: "0.3", 35: "0.35", 40: "0.4", 45: "0.45", 50: "0.5", 55: "0.55", 60: "0.6", 65: "0.65", 70: "0.7", 75: "0.75", 80: "0.8", 85: "0.85", 90: "0.9", 95: "0.95", 100: "1", ...s }, order: { first: "-9999", last: "9999", none: "0", 1: "1", 2: "2", 3: "3", 4: "4", 5: "5", 6: "6", 7: "7", 8: "8", 9: "9", 10: "10", 11: "11", 12: "12", ...l2 }, outlineColor: ({ theme: e }) => e("colors"), outlineOffset: { 0: "0px", 1: "1px", 2: "2px", 4: "4px", 8: "8px", ...d }, outlineWidth: { 0: "0px", 1: "1px", 2: "2px", 4: "4px", 8: "8px", ...d }, padding: ({ theme: e }) => e("spacing"), placeholderColor: ({ theme: e }) => e("colors"), placeholderOpacity: ({ theme: e }) => e("opacity"), ringColor: ({ theme: e }) => ({ DEFAULT: "currentcolor", ...e("colors") }), ringOffsetColor: ({ theme: e }) => e("colors"), ringOffsetWidth: { 0: "0px", 1: "1px", 2: "2px", 4: "4px", 8: "8px", ...d }, ringOpacity: ({ theme: e }) => ({ DEFAULT: "0.5", ...e("opacity") }), ringWidth: { DEFAULT: "3px", 0: "0px", 1: "1px", 2: "2px", 4: "4px", 8: "8px", ...d }, rotate: { 0: "0deg", 1: "1deg", 2: "2deg", 3: "3deg", 6: "6deg", 12: "12deg", 45: "45deg", 90: "90deg", 180: "180deg", ...k }, saturate: { 0: "0", 50: ".5", 100: "1", 150: "1.5", 200: "2", ...s }, scale: { 0: "0", 50: ".5", 75: ".75", 90: ".9", 95: ".95", 100: "1", 105: "1.05", 110: "1.1", 125: "1.25", 150: "1.5", ...s }, screens: { sm: "40rem", md: "48rem", lg: "64rem", xl: "80rem", "2xl": "96rem" }, scrollMargin: ({ theme: e }) => e("spacing"), scrollPadding: ({ theme: e }) => e("spacing"), sepia: { 0: "0", DEFAULT: "100%", ...s }, skew: { 0: "0deg", 1: "1deg", 2: "2deg", 3: "3deg", 6: "6deg", 12: "12deg", ...k }, space: ({ theme: e }) => e("spacing"), spacing: { px: "1px", 0: "0px", 0.5: "0.125rem", 1: "0.25rem", 1.5: "0.375rem", 2: "0.5rem", 2.5: "0.625rem", 3: "0.75rem", 3.5: "0.875rem", 4: "1rem", 5: "1.25rem", 6: "1.5rem", 7: "1.75rem", 8: "2rem", 9: "2.25rem", 10: "2.5rem", 11: "2.75rem", 12: "3rem", 14: "3.5rem", 16: "4rem", 20: "5rem", 24: "6rem", 28: "7rem", 32: "8rem", 36: "9rem", 40: "10rem", 44: "11rem", 48: "12rem", 52: "13rem", 56: "14rem", 60: "15rem", 64: "16rem", 72: "18rem", 80: "20rem", 96: "24rem" }, stroke: ({ theme: e }) => ({ none: "none", ...e("colors") }), strokeWidth: { 0: "0", 1: "1", 2: "2", ...l2 }, supports: {}, data: {}, textColor: ({ theme: e }) => e("colors"), textDecorationColor: ({ theme: e }) => e("colors"), textDecorationThickness: { auto: "auto", "from-font": "from-font", 0: "0px", 1: "1px", 2: "2px", 4: "4px", 8: "8px", ...d }, textIndent: ({ theme: e }) => e("spacing"), textOpacity: ({ theme: e }) => e("opacity"), textUnderlineOffset: { auto: "auto", 0: "0px", 1: "1px", 2: "2px", 4: "4px", 8: "8px", ...d }, transformOrigin: { center: "center", top: "top", "top-right": "top right", right: "right", "bottom-right": "bottom right", bottom: "bottom", "bottom-left": "bottom left", left: "left", "top-left": "top left" }, transitionDelay: { 0: "0s", 75: "75ms", 100: "100ms", 150: "150ms", 200: "200ms", 300: "300ms", 500: "500ms", 700: "700ms", 1e3: "1000ms", ...L }, transitionDuration: { DEFAULT: "150ms", 0: "0s", 75: "75ms", 100: "100ms", 150: "150ms", 200: "200ms", 300: "300ms", 500: "500ms", 700: "700ms", 1e3: "1000ms", ...L }, transitionProperty: { none: "none", all: "all", DEFAULT: "color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter", colors: "color, background-color, border-color, outline-color, text-decoration-color, fill, stroke", opacity: "opacity", shadow: "box-shadow", transform: "transform" }, transitionTimingFunction: { DEFAULT: "cubic-bezier(0.4, 0, 0.2, 1)", linear: "linear", in: "cubic-bezier(0.4, 0, 1, 1)", out: "cubic-bezier(0, 0, 0.2, 1)", "in-out": "cubic-bezier(0.4, 0, 0.2, 1)" }, translate: ({ theme: e }) => ({ "1/2": "50%", "1/3": "33.333333%", "2/3": "66.666667%", "1/4": "25%", "2/4": "50%", "3/4": "75%", full: "100%", ...e("spacing") }), size: ({ theme: e }) => ({ auto: "auto", "1/2": "50%", "1/3": "33.333333%", "2/3": "66.666667%", "1/4": "25%", "2/4": "50%", "3/4": "75%", "1/5": "20%", "2/5": "40%", "3/5": "60%", "4/5": "80%", "1/6": "16.666667%", "2/6": "33.333333%", "3/6": "50%", "4/6": "66.666667%", "5/6": "83.333333%", "1/12": "8.333333%", "2/12": "16.666667%", "3/12": "25%", "4/12": "33.333333%", "5/12": "41.666667%", "6/12": "50%", "7/12": "58.333333%", "8/12": "66.666667%", "9/12": "75%", "10/12": "83.333333%", "11/12": "91.666667%", full: "100%", min: "min-content", max: "max-content", fit: "fit-content", ...e("spacing") }), width: ({ theme: e }) => ({ auto: "auto", "1/2": "50%", "1/3": "33.333333%", "2/3": "66.666667%", "1/4": "25%", "2/4": "50%", "3/4": "75%", "1/5": "20%", "2/5": "40%", "3/5": "60%", "4/5": "80%", "1/6": "16.666667%", "2/6": "33.333333%", "3/6": "50%", "4/6": "66.666667%", "5/6": "83.333333%", "1/12": "8.333333%", "2/12": "16.666667%", "3/12": "25%", "4/12": "33.333333%", "5/12": "41.666667%", "6/12": "50%", "7/12": "58.333333%", "8/12": "66.666667%", "9/12": "75%", "10/12": "83.333333%", "11/12": "91.666667%", full: "100%", screen: "100vw", svw: "100svw", lvw: "100lvw", dvw: "100dvw", min: "min-content", max: "max-content", fit: "fit-content", ...e("spacing") }), willChange: { auto: "auto", scroll: "scroll-position", contents: "contents", transform: "transform" }, zIndex: { auto: "auto", 0: "0", 10: "10", 20: "20", 30: "30", 40: "40", 50: "50", ...l2 } };

// node_modules/tailwindcss/dist/chunk-U7LQKMN6.mjs
var Rt = "4.1.7";
var Ve = 92;
var Ie = 47;
var Fe = 42;
var ei = 34;
var ti = 39;
var ri = 58;
var Le = 59;
var ie2 = 10;
var ze = 13;
var Ne = 32;
var Me = 9;
var Ot = 123;
var at = 125;
var ft = 40;
var Kt = 41;
var ii = 91;
var ni = 93;
var _t = 45;
var st = 64;
var oi = 33;
function me(r, t) {
  let i = (t == null ? void 0 : t.from) ? { file: t.from, code: r } : null;
  r[0] === "\uFEFF" && (r = " " + r.slice(1));
  let e = [], n = [], s2 = [], a = null, c = null, u2 = "", f2 = "", g2 = 0, p2;
  for (let m = 0; m < r.length; m++) {
    let w2 = r.charCodeAt(m);
    if (!(w2 === ze && (p2 = r.charCodeAt(m + 1), p2 === ie2)))
      if (w2 === Ve)
        u2 === "" && (g2 = m), u2 += r.slice(m, m + 2), m += 1;
      else if (w2 === Ie && r.charCodeAt(m + 1) === Fe) {
        let v2 = m;
        for (let k2 = m + 2; k2 < r.length; k2++)
          if (p2 = r.charCodeAt(k2), p2 === Ve)
            k2 += 1;
          else if (p2 === Fe && r.charCodeAt(k2 + 1) === Ie) {
            m = k2 + 1;
            break;
          }
        let x = r.slice(v2, m + 1);
        if (x.charCodeAt(2) === oi) {
          let k2 = We(x.slice(2, -2));
          n.push(k2), i && (k2.src = [i, v2, m + 1], k2.dst = [i, v2, m + 1]);
        }
      } else if (w2 === ti || w2 === ei) {
        let v2 = m;
        for (let x = m + 1; x < r.length; x++)
          if (p2 = r.charCodeAt(x), p2 === Ve)
            x += 1;
          else if (p2 === w2) {
            m = x;
            break;
          } else {
            if (p2 === Le && (r.charCodeAt(x + 1) === ie2 || r.charCodeAt(x + 1) === ze && r.charCodeAt(x + 2) === ie2))
              throw new Error(`Unterminated string: ${r.slice(v2, x + 1) + String.fromCharCode(w2)}`);
            if (p2 === ie2 || p2 === ze && r.charCodeAt(x + 1) === ie2)
              throw new Error(`Unterminated string: ${r.slice(v2, x) + String.fromCharCode(w2)}`);
          }
        u2 += r.slice(v2, m + 1);
      } else {
        if ((w2 === Ne || w2 === ie2 || w2 === Me) && (p2 = r.charCodeAt(m + 1)) && (p2 === Ne || p2 === ie2 || p2 === Me || p2 === ze && (p2 = r.charCodeAt(m + 2)) && p2 == ie2))
          continue;
        if (w2 === ie2) {
          if (u2.length === 0)
            continue;
          p2 = u2.charCodeAt(u2.length - 1), p2 !== Ne && p2 !== ie2 && p2 !== Me && (u2 += " ");
        } else if (w2 === _t && r.charCodeAt(m + 1) === _t && u2.length === 0) {
          let v2 = "", x = m, k2 = -1;
          for (let b2 = m + 2; b2 < r.length; b2++)
            if (p2 = r.charCodeAt(b2), p2 === Ve)
              b2 += 1;
            else if (p2 === Ie && r.charCodeAt(b2 + 1) === Fe) {
              for (let S = b2 + 2; S < r.length; S++)
                if (p2 = r.charCodeAt(S), p2 === Ve)
                  S += 1;
                else if (p2 === Fe && r.charCodeAt(S + 1) === Ie) {
                  b2 = S + 1;
                  break;
                }
            } else if (k2 === -1 && p2 === ri)
              k2 = u2.length + b2 - x;
            else if (p2 === Le && v2.length === 0) {
              u2 += r.slice(x, b2), m = b2;
              break;
            } else if (p2 === ft)
              v2 += ")";
            else if (p2 === ii)
              v2 += "]";
            else if (p2 === Ot)
              v2 += "}";
            else if ((p2 === at || r.length - 1 === b2) && v2.length === 0) {
              m = b2 - 1, u2 += r.slice(x, b2);
              break;
            } else
              (p2 === Kt || p2 === ni || p2 === at) && v2.length > 0 && r[b2] === v2[v2.length - 1] && (v2 = v2.slice(0, -1));
          let N2 = ut(u2, k2);
          if (!N2)
            throw new Error("Invalid custom property, expected a value");
          i && (N2.src = [i, x, m], N2.dst = [i, x, m]), a ? a.nodes.push(N2) : e.push(N2), u2 = "";
        } else if (w2 === Le && u2.charCodeAt(0) === st)
          c = Se(u2), i && (c.src = [i, g2, m], c.dst = [i, g2, m]), a ? a.nodes.push(c) : e.push(c), u2 = "", c = null;
        else if (w2 === Le && f2[f2.length - 1] !== ")") {
          let v2 = ut(u2);
          if (!v2)
            throw u2.length === 0 ? new Error("Unexpected semicolon") : new Error(`Invalid declaration: \`${u2.trim()}\``);
          i && (v2.src = [i, g2, m], v2.dst = [i, g2, m]), a ? a.nodes.push(v2) : e.push(v2), u2 = "";
        } else if (w2 === Ot && f2[f2.length - 1] !== ")")
          f2 += "}", c = H2(u2.trim()), i && (c.src = [i, g2, m], c.dst = [i, g2, m]), a && a.nodes.push(c), s2.push(a), a = c, u2 = "", c = null;
        else if (w2 === at && f2[f2.length - 1] !== ")") {
          if (f2 === "")
            throw new Error("Missing opening {");
          if (f2 = f2.slice(0, -1), u2.length > 0)
            if (u2.charCodeAt(0) === st)
              c = Se(u2), i && (c.src = [i, g2, m], c.dst = [i, g2, m]), a ? a.nodes.push(c) : e.push(c), u2 = "", c = null;
            else {
              let x = u2.indexOf(":");
              if (a) {
                let k2 = ut(u2, x);
                if (!k2)
                  throw new Error(`Invalid declaration: \`${u2.trim()}\``);
                i && (k2.src = [i, g2, m], k2.dst = [i, g2, m]), a.nodes.push(k2);
              }
            }
          let v2 = s2.pop() ?? null;
          v2 === null && a && e.push(a), a = v2, u2 = "", c = null;
        } else if (w2 === ft)
          f2 += ")", u2 += "(";
        else if (w2 === Kt) {
          if (f2[f2.length - 1] !== ")")
            throw new Error("Missing opening (");
          f2 = f2.slice(0, -1), u2 += ")";
        } else {
          if (u2.length === 0 && (w2 === Ne || w2 === ie2 || w2 === Me))
            continue;
          u2 === "" && (g2 = m), u2 += String.fromCharCode(w2);
        }
      }
  }
  if (u2.charCodeAt(0) === st) {
    let m = Se(u2);
    i && (m.src = [i, g2, r.length], m.dst = [i, g2, r.length]), e.push(m);
  }
  if (f2.length > 0 && a) {
    if (a.kind === "rule")
      throw new Error(`Missing closing } at ${a.selector}`);
    if (a.kind === "at-rule")
      throw new Error(`Missing closing } at ${a.name} ${a.params}`);
  }
  return n.length > 0 ? n.concat(e) : e;
}
function Se(r, t = []) {
  let i = r, e = "";
  for (let n = 5; n < r.length; n++) {
    let s2 = r.charCodeAt(n);
    if (s2 === Ne || s2 === ft) {
      i = r.slice(0, n), e = r.slice(n);
      break;
    }
  }
  return z2(i.trim(), e.trim(), t);
}
function ut(r, t = r.indexOf(":")) {
  if (t === -1)
    return null;
  let i = r.indexOf("!important", t + 1);
  return l3(r.slice(0, t).trim(), r.slice(t + 1, i === -1 ? r.length : i).trim(), i !== -1);
}
function fe(r) {
  if (arguments.length === 0)
    throw new TypeError("`CSS.escape` requires an argument.");
  let t = String(r), i = t.length, e = -1, n, s2 = "", a = t.charCodeAt(0);
  if (i === 1 && a === 45)
    return "\\" + t;
  for (; ++e < i; ) {
    if (n = t.charCodeAt(e), n === 0) {
      s2 += "�";
      continue;
    }
    if (n >= 1 && n <= 31 || n === 127 || e === 0 && n >= 48 && n <= 57 || e === 1 && n >= 48 && n <= 57 && a === 45) {
      s2 += "\\" + n.toString(16) + " ";
      continue;
    }
    if (n >= 128 || n === 45 || n === 95 || n >= 48 && n <= 57 || n >= 65 && n <= 90 || n >= 97 && n <= 122) {
      s2 += t.charAt(e);
      continue;
    }
    s2 += "\\" + t.charAt(e);
  }
  return s2;
}
function ge2(r) {
  return r.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g, (t) => t.length > 2 ? String.fromCodePoint(Number.parseInt(t.slice(1).trim(), 16)) : t[1]);
}
var Dt = /* @__PURE__ */ new Map([["--font", ["--font-weight", "--font-size"]], ["--inset", ["--inset-shadow", "--inset-ring"]], ["--text", ["--text-color", "--text-decoration-color", "--text-decoration-thickness", "--text-indent", "--text-shadow", "--text-underline-offset"]]]);
function jt(r, t) {
  return (Dt.get(t) ?? []).some((i) => r === i || r.startsWith(`${i}-`));
}
var _r, r_fn, _e, e_fn, _t2, t_fn, _a;
var Be = (_a = class {
  constructor(t = /* @__PURE__ */ new Map(), i = /* @__PURE__ */ new Set([])) {
    __privateAdd(this, _r);
    __privateAdd(this, _e);
    __privateAdd(this, _t2);
    __publicField(this, "prefix", null);
    this.values = t;
    this.keyframes = i;
  }
  add(t, i, e = 0, n) {
    if (t.endsWith("-*")) {
      if (i !== "initial")
        throw new Error(`Invalid theme value \`${i}\` for namespace \`${t}\``);
      t === "--*" ? this.values.clear() : this.clearNamespace(t.slice(0, -2), 0);
    }
    if (e & 4) {
      let s2 = this.values.get(t);
      if (s2 && !(s2.options & 4))
        return;
    }
    i === "initial" ? this.values.delete(t) : this.values.set(t, { value: i, options: e, src: n });
  }
  keysInNamespaces(t) {
    let i = [];
    for (let e of t) {
      let n = `${e}-`;
      for (let s2 of this.values.keys())
        s2.startsWith(n) && s2.indexOf("--", 2) === -1 && (jt(s2, e) || i.push(s2.slice(n.length)));
    }
    return i;
  }
  get(t) {
    for (let i of t) {
      let e = this.values.get(i);
      if (e)
        return e.value;
    }
    return null;
  }
  hasDefault(t) {
    return (this.getOptions(t) & 4) === 4;
  }
  getOptions(t) {
    var _a2;
    return t = ge2(__privateMethod(this, _r, r_fn).call(this, t)), ((_a2 = this.values.get(t)) == null ? void 0 : _a2.options) ?? 0;
  }
  entries() {
    return this.prefix ? Array.from(this.values, (t) => (t[0] = this.prefixKey(t[0]), t)) : this.values.entries();
  }
  prefixKey(t) {
    return this.prefix ? `--${this.prefix}-${t.slice(2)}` : t;
  }
  clearNamespace(t, i) {
    let e = Dt.get(t) ?? [];
    e:
      for (let n of this.values.keys())
        if (n.startsWith(t)) {
          if (i !== 0 && (this.getOptions(n) & i) !== i)
            continue;
          for (let s2 of e)
            if (n.startsWith(s2))
              continue e;
          this.values.delete(n);
        }
  }
  markUsedVariable(t) {
    let i = ge2(__privateMethod(this, _r, r_fn).call(this, t)), e = this.values.get(i);
    if (!e)
      return false;
    let n = e.options & 16;
    return e.options |= 16, !n;
  }
  resolve(t, i, e = 0) {
    let n = __privateMethod(this, _e, e_fn).call(this, t, i);
    if (!n)
      return null;
    let s2 = this.values.get(n);
    return (e | s2.options) & 1 ? s2.value : __privateMethod(this, _t2, t_fn).call(this, n);
  }
  resolveValue(t, i) {
    let e = __privateMethod(this, _e, e_fn).call(this, t, i);
    return e ? this.values.get(e).value : null;
  }
  resolveWith(t, i, e = []) {
    let n = __privateMethod(this, _e, e_fn).call(this, t, i);
    if (!n)
      return null;
    let s2 = {};
    for (let c of e) {
      let u2 = `${n}${c}`, f2 = this.values.get(u2);
      f2 && (f2.options & 1 ? s2[c] = f2.value : s2[c] = __privateMethod(this, _t2, t_fn).call(this, u2));
    }
    let a = this.values.get(n);
    return a.options & 1 ? [a.value, s2] : [__privateMethod(this, _t2, t_fn).call(this, n), s2];
  }
  namespace(t) {
    let i = /* @__PURE__ */ new Map(), e = `${t}-`;
    for (let [n, s2] of this.values)
      n === t ? i.set(null, s2.value) : n.startsWith(`${e}-`) ? i.set(n.slice(t.length), s2.value) : n.startsWith(e) && i.set(n.slice(e.length), s2.value);
    return i;
  }
  addKeyframes(t) {
    this.keyframes.add(t);
  }
  getKeyframes() {
    return Array.from(this.keyframes);
  }
}, _r = new WeakSet(), r_fn = function(t) {
  return this.prefix ? `--${t.slice(3 + this.prefix.length)}` : t;
}, _e = new WeakSet(), e_fn = function(t, i) {
  for (let e of i) {
    let n = t !== null ? `${e}-${t}` : e;
    if (!this.values.has(n))
      if (t !== null && t.includes(".")) {
        if (n = `${e}-${t.replaceAll(".", "_")}`, !this.values.has(n))
          continue;
      } else
        continue;
    if (!jt(n, e))
      return n;
  }
  return null;
}, _t2 = new WeakSet(), t_fn = function(t) {
  let i = this.values.get(t);
  if (!i)
    return null;
  let e = null;
  return i.options & 2 && (e = i.value), `var(${fe(this.prefixKey(t))}${e ? `, ${e}` : ""})`;
}, _a);
var W2 = class extends Map {
  constructor(i) {
    super();
    this.factory = i;
  }
  get(i) {
    let e = super.get(i);
    return e === void 0 && (e = this.factory(i, this), this.set(i, e)), e;
  }
};
function dt(r) {
  return { kind: "word", value: r };
}
function li(r, t) {
  return { kind: "function", value: r, nodes: t };
}
function ai(r) {
  return { kind: "separator", value: r };
}
function ee2(r, t, i = null) {
  for (let e = 0; e < r.length; e++) {
    let n = r[e], s2 = false, a = 0, c = t(n, { parent: i, replaceWith(u2) {
      s2 || (s2 = true, Array.isArray(u2) ? u2.length === 0 ? (r.splice(e, 1), a = 0) : u2.length === 1 ? (r[e] = u2[0], a = 1) : (r.splice(e, 1, ...u2), a = u2.length) : r[e] = u2);
    } }) ?? 0;
    if (s2) {
      c === 0 ? e-- : e += a - 1;
      continue;
    }
    if (c === 2)
      return 2;
    if (c !== 1 && n.kind === "function" && ee2(n.nodes, t, n) === 2)
      return 2;
  }
}
function Y2(r) {
  let t = "";
  for (let i of r)
    switch (i.kind) {
      case "word":
      case "separator": {
        t += i.value;
        break;
      }
      case "function":
        t += i.value + "(" + Y2(i.nodes) + ")";
    }
  return t;
}
var Ut = 92;
var si = 41;
var It = 58;
var Ft = 44;
var ui = 34;
var Lt = 61;
var zt = 62;
var Mt = 60;
var Wt = 10;
var fi = 40;
var ci = 39;
var Bt = 47;
var qt = 32;
var Gt = 9;
function B2(r) {
  r = r.replaceAll(`\r
`, `
`);
  let t = [], i = [], e = null, n = "", s2;
  for (let a = 0; a < r.length; a++) {
    let c = r.charCodeAt(a);
    switch (c) {
      case Ut: {
        n += r[a] + r[a + 1], a++;
        break;
      }
      case It:
      case Ft:
      case Lt:
      case zt:
      case Mt:
      case Wt:
      case Bt:
      case qt:
      case Gt: {
        if (n.length > 0) {
          let p2 = dt(n);
          e ? e.nodes.push(p2) : t.push(p2), n = "";
        }
        let u2 = a, f2 = a + 1;
        for (; f2 < r.length && (s2 = r.charCodeAt(f2), !(s2 !== It && s2 !== Ft && s2 !== Lt && s2 !== zt && s2 !== Mt && s2 !== Wt && s2 !== Bt && s2 !== qt && s2 !== Gt)); f2++)
          ;
        a = f2 - 1;
        let g2 = ai(r.slice(u2, f2));
        e ? e.nodes.push(g2) : t.push(g2);
        break;
      }
      case ci:
      case ui: {
        let u2 = a;
        for (let f2 = a + 1; f2 < r.length; f2++)
          if (s2 = r.charCodeAt(f2), s2 === Ut)
            f2 += 1;
          else if (s2 === c) {
            a = f2;
            break;
          }
        n += r.slice(u2, a + 1);
        break;
      }
      case fi: {
        let u2 = li(n, []);
        n = "", e ? e.nodes.push(u2) : t.push(u2), i.push(u2), e = u2;
        break;
      }
      case si: {
        let u2 = i.pop();
        if (n.length > 0) {
          let f2 = dt(n);
          u2.nodes.push(f2), n = "";
        }
        i.length > 0 ? e = i[i.length - 1] : e = null;
        break;
      }
      default:
        n += String.fromCharCode(c);
    }
  }
  return n.length > 0 && t.push(dt(n)), t;
}
function qe(r) {
  let t = [];
  return ee2(B2(r), (i) => {
    if (!(i.kind !== "function" || i.value !== "var"))
      return ee2(i.nodes, (e) => {
        e.kind !== "word" || e.value[0] !== "-" || e.value[1] !== "-" || t.push(e.value);
      }), 1;
  }), t;
}
var pi = 64;
function M2(r, t = []) {
  return { kind: "rule", selector: r, nodes: t };
}
function z2(r, t = "", i = []) {
  return { kind: "at-rule", name: r, params: t, nodes: i };
}
function H2(r, t = []) {
  return r.charCodeAt(0) === pi ? Se(r, t) : M2(r, t);
}
function l3(r, t, i = false) {
  return { kind: "declaration", property: r, value: t, important: i };
}
function We(r) {
  return { kind: "comment", value: r };
}
function le(r, t) {
  return { kind: "context", context: r, nodes: t };
}
function I2(r) {
  return { kind: "at-root", nodes: r };
}
function D2(r, t, i = [], e = {}) {
  for (let n = 0; n < r.length; n++) {
    let s2 = r[n], a = i[i.length - 1] ?? null;
    if (s2.kind === "context") {
      if (D2(s2.nodes, t, i, { ...e, ...s2.context }) === 2)
        return 2;
      continue;
    }
    i.push(s2);
    let c = false, u2 = 0, f2 = t(s2, { parent: a, context: e, path: i, replaceWith(g2) {
      c || (c = true, Array.isArray(g2) ? g2.length === 0 ? (r.splice(n, 1), u2 = 0) : g2.length === 1 ? (r[n] = g2[0], u2 = 1) : (r.splice(n, 1, ...g2), u2 = g2.length) : (r[n] = g2, u2 = 1));
    } }) ?? 0;
    if (i.pop(), c) {
      f2 === 0 ? n-- : n += u2 - 1;
      continue;
    }
    if (f2 === 2)
      return 2;
    if (f2 !== 1 && "nodes" in s2) {
      i.push(s2);
      let g2 = D2(s2.nodes, t, i, e);
      if (i.pop(), g2 === 2)
        return 2;
    }
  }
}
function Ge(r, t, i = [], e = {}) {
  for (let n = 0; n < r.length; n++) {
    let s2 = r[n], a = i[i.length - 1] ?? null;
    if (s2.kind === "rule" || s2.kind === "at-rule")
      i.push(s2), Ge(s2.nodes, t, i, e), i.pop();
    else if (s2.kind === "context") {
      Ge(s2.nodes, t, i, { ...e, ...s2.context });
      continue;
    }
    i.push(s2), t(s2, { parent: a, context: e, path: i, replaceWith(c) {
      Array.isArray(c) ? c.length === 0 ? r.splice(n, 1) : c.length === 1 ? r[n] = c[0] : r.splice(n, 1, ...c) : r[n] = c, n += c.length - 1;
    } }), i.pop();
  }
}
function ve(r, t, i = 3) {
  let e = [], n = /* @__PURE__ */ new Set(), s2 = new W2(() => /* @__PURE__ */ new Set()), a = new W2(() => /* @__PURE__ */ new Set()), c = /* @__PURE__ */ new Set(), u2 = /* @__PURE__ */ new Set(), f2 = [], g2 = [], p2 = new W2(() => /* @__PURE__ */ new Set());
  function m(v2, x, k2 = {}, N2 = 0) {
    if (v2.kind === "declaration") {
      if (v2.property === "--tw-sort" || v2.value === void 0 || v2.value === null)
        return;
      if (k2.theme && v2.property[0] === "-" && v2.property[1] === "-") {
        if (v2.value === "initial") {
          v2.value = void 0;
          return;
        }
        k2.keyframes || s2.get(x).add(v2);
      }
      if (v2.value.includes("var("))
        if (k2.theme && v2.property[0] === "-" && v2.property[1] === "-")
          for (let b2 of qe(v2.value))
            p2.get(b2).add(v2.property);
        else
          t.trackUsedVariables(v2.value);
      if (v2.property === "animation")
        for (let b2 of Jt(v2.value))
          u2.add(b2);
      i & 2 && v2.value.includes("color-mix(") && a.get(x).add(v2), x.push(v2);
    } else if (v2.kind === "rule")
      if (v2.selector === "&")
        for (let b2 of v2.nodes) {
          let S = [];
          m(b2, S, k2, N2 + 1), S.length > 0 && x.push(...S);
        }
      else {
        let b2 = { ...v2, nodes: [] };
        for (let S of v2.nodes)
          m(S, b2.nodes, k2, N2 + 1);
        b2.nodes.length > 0 && x.push(b2);
      }
    else if (v2.kind === "at-rule" && v2.name === "@property" && N2 === 0) {
      if (n.has(v2.params))
        return;
      if (i & 1) {
        let S = v2.params, P2 = null, j2 = false;
        for (let F2 of v2.nodes)
          F2.kind === "declaration" && (F2.property === "initial-value" ? P2 = F2.value : F2.property === "inherits" && (j2 = F2.value === "true"));
        let K2 = l3(S, P2 ?? "initial");
        K2.src = v2.src, j2 ? f2.push(K2) : g2.push(K2);
      }
      n.add(v2.params);
      let b2 = { ...v2, nodes: [] };
      for (let S of v2.nodes)
        m(S, b2.nodes, k2, N2 + 1);
      x.push(b2);
    } else if (v2.kind === "at-rule") {
      v2.name === "@keyframes" && (k2 = { ...k2, keyframes: true });
      let b2 = { ...v2, nodes: [] };
      for (let S of v2.nodes)
        m(S, b2.nodes, k2, N2 + 1);
      v2.name === "@keyframes" && k2.theme && c.add(b2), (b2.nodes.length > 0 || b2.name === "@layer" || b2.name === "@charset" || b2.name === "@custom-media" || b2.name === "@namespace" || b2.name === "@import") && x.push(b2);
    } else if (v2.kind === "at-root")
      for (let b2 of v2.nodes) {
        let S = [];
        m(b2, S, k2, 0);
        for (let P2 of S)
          e.push(P2);
      }
    else if (v2.kind === "context") {
      if (v2.context.reference)
        return;
      for (let b2 of v2.nodes)
        m(b2, x, { ...k2, ...v2.context }, N2);
    } else
      v2.kind === "comment" && x.push(v2);
  }
  let w2 = [];
  for (let v2 of r)
    m(v2, w2, {}, 0);
  e:
    for (let [v2, x] of s2)
      for (let k2 of x) {
        if (Ht(k2.property, t.theme, p2)) {
          if (k2.property.startsWith(t.theme.prefixKey("--animate-")))
            for (let S of Jt(k2.value))
              u2.add(S);
          continue;
        }
        let b2 = v2.indexOf(k2);
        if (v2.splice(b2, 1), v2.length === 0) {
          let S = mi(w2, (P2) => P2.kind === "rule" && P2.nodes === v2);
          if (!S || S.length === 0)
            continue e;
          S.unshift({ kind: "at-root", nodes: w2 });
          do {
            let P2 = S.pop();
            if (!P2)
              break;
            let j2 = S[S.length - 1];
            if (!j2 || j2.kind !== "at-root" && j2.kind !== "at-rule")
              break;
            let K2 = j2.nodes.indexOf(P2);
            if (K2 === -1)
              break;
            j2.nodes.splice(K2, 1);
          } while (true);
          continue e;
        }
      }
  for (let v2 of c)
    if (!u2.has(v2.params)) {
      let x = e.indexOf(v2);
      e.splice(x, 1);
    }
  if (w2 = w2.concat(e), i & 2)
    for (let [v2, x] of a)
      for (let k2 of x) {
        let N2 = v2.indexOf(k2);
        if (N2 === -1 || k2.value == null)
          continue;
        let b2 = B2(k2.value), S = false;
        if (ee2(b2, (K2, { replaceWith: F2 }) => {
          if (K2.kind !== "function" || K2.value !== "color-mix")
            return;
          let O2 = false, G2 = false;
          if (ee2(K2.nodes, (L2, { replaceWith: q2 }) => {
            if (L2.kind == "word" && L2.value.toLowerCase() === "currentcolor") {
              G2 = true, S = true;
              return;
            }
            let X2 = L2, re2 = null, o = /* @__PURE__ */ new Set();
            do {
              if (X2.kind !== "function" || X2.value !== "var")
                return;
              let d2 = X2.nodes[0];
              if (!d2 || d2.kind !== "word")
                return;
              let h2 = d2.value;
              if (o.has(h2)) {
                O2 = true;
                return;
              }
              if (o.add(h2), S = true, re2 = t.theme.resolveValue(null, [d2.value]), !re2) {
                O2 = true;
                return;
              }
              if (re2.toLowerCase() === "currentcolor") {
                G2 = true;
                return;
              }
              re2.startsWith("var(") ? X2 = B2(re2)[0] : X2 = null;
            } while (X2);
            q2({ kind: "word", value: re2 });
          }), O2 || G2) {
            let L2 = K2.nodes.findIndex((X2) => X2.kind === "separator" && X2.value.trim().includes(","));
            if (L2 === -1)
              return;
            let q2 = K2.nodes.length > L2 ? K2.nodes[L2 + 1] : null;
            if (!q2)
              return;
            F2(q2);
          } else if (S) {
            let L2 = K2.nodes[2];
            L2.kind === "word" && (L2.value === "oklab" || L2.value === "oklch" || L2.value === "lab" || L2.value === "lch") && (L2.value = "srgb");
          }
        }), !S)
          continue;
        let P2 = { ...k2, value: Y2(b2) }, j2 = H2("@supports (color: color-mix(in lab, red, red))", [k2]);
        j2.src = k2.src, v2.splice(N2, 1, P2, j2);
      }
  if (i & 1) {
    let v2 = [];
    if (f2.length > 0) {
      let x = H2(":root, :host", f2);
      x.src = f2[0].src, v2.push(x);
    }
    if (g2.length > 0) {
      let x = H2("*, ::before, ::after, ::backdrop", g2);
      x.src = g2[0].src, v2.push(x);
    }
    if (v2.length > 0) {
      let x = w2.findIndex((b2) => !(b2.kind === "comment" || b2.kind === "at-rule" && (b2.name === "@charset" || b2.name === "@import"))), k2 = z2("@layer", "properties", []);
      k2.src = v2[0].src, w2.splice(x < 0 ? w2.length : x, 0, k2);
      let N2 = H2("@layer properties", [z2("@supports", "((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b))))", v2)]);
      N2.src = v2[0].src, N2.nodes[0].src = v2[0].src, w2.push(N2);
    }
  }
  return w2;
}
function ne2(r, t) {
  let i = 0, e = { file: null, code: "" };
  function n(a, c = 0) {
    var _a2;
    let u2 = "", f2 = "  ".repeat(c);
    if (a.kind === "declaration") {
      if (u2 += `${f2}${a.property}: ${a.value}${a.important ? " !important" : ""};
`, t) {
        i += f2.length;
        let g2 = i;
        i += a.property.length, i += 2, i += ((_a2 = a.value) == null ? void 0 : _a2.length) ?? 0, a.important && (i += 11);
        let p2 = i;
        i += 2, a.dst = [e, g2, p2];
      }
    } else if (a.kind === "rule") {
      if (u2 += `${f2}${a.selector} {
`, t) {
        i += f2.length;
        let g2 = i;
        i += a.selector.length, i += 1;
        let p2 = i;
        a.dst = [e, g2, p2], i += 2;
      }
      for (let g2 of a.nodes)
        u2 += n(g2, c + 1);
      u2 += `${f2}}
`, t && (i += f2.length, i += 2);
    } else if (a.kind === "at-rule") {
      if (a.nodes.length === 0) {
        let g2 = `${f2}${a.name} ${a.params};
`;
        if (t) {
          i += f2.length;
          let p2 = i;
          i += a.name.length, i += 1, i += a.params.length;
          let m = i;
          i += 2, a.dst = [e, p2, m];
        }
        return g2;
      }
      if (u2 += `${f2}${a.name}${a.params ? ` ${a.params} ` : " "}{
`, t) {
        i += f2.length;
        let g2 = i;
        i += a.name.length, a.params && (i += 1, i += a.params.length), i += 1;
        let p2 = i;
        a.dst = [e, g2, p2], i += 2;
      }
      for (let g2 of a.nodes)
        u2 += n(g2, c + 1);
      u2 += `${f2}}
`, t && (i += f2.length, i += 2);
    } else if (a.kind === "comment") {
      if (u2 += `${f2}/*${a.value}*/
`, t) {
        i += f2.length;
        let g2 = i;
        i += 2 + a.value.length + 2;
        let p2 = i;
        a.dst = [e, g2, p2], i += 1;
      }
    } else if (a.kind === "context" || a.kind === "at-root")
      return "";
    return u2;
  }
  let s2 = "";
  for (let a of r)
    s2 += n(a, 0);
  return e.code = s2, s2;
}
function mi(r, t) {
  let i = [];
  return D2(r, (e, { path: n }) => {
    if (t(e))
      return i = [...n], 2;
  }), i;
}
function Ht(r, t, i, e = /* @__PURE__ */ new Set()) {
  if (e.has(r) || (e.add(r), t.getOptions(r) & 24))
    return true;
  {
    let s2 = i.get(r) ?? [];
    for (let a of s2)
      if (Ht(a, t, i, e))
        return true;
  }
  return false;
}
function Jt(r) {
  return r.split(/[\s,]+/);
}
function ce(r) {
  if (r.indexOf("(") === -1)
    return be(r);
  let t = B2(r);
  return mt(t), r = Y2(t), r = ie(r), r;
}
function be(r, t = false) {
  let i = "";
  for (let e = 0; e < r.length; e++) {
    let n = r[e];
    n === "\\" && r[e + 1] === "_" ? (i += "_", e += 1) : n === "_" && !t ? i += " " : i += n;
  }
  return i;
}
function mt(r) {
  for (let t of r)
    switch (t.kind) {
      case "function": {
        if (t.value === "url" || t.value.endsWith("_url")) {
          t.value = be(t.value);
          break;
        }
        if (t.value === "var" || t.value.endsWith("_var") || t.value === "theme" || t.value.endsWith("_theme")) {
          t.value = be(t.value);
          for (let i = 0; i < t.nodes.length; i++) {
            if (i == 0 && t.nodes[i].kind === "word") {
              t.nodes[i].value = be(t.nodes[i].value, true);
              continue;
            }
            mt([t.nodes[i]]);
          }
          break;
        }
        t.value = be(t.value), mt(t.nodes);
        break;
      }
      case "separator":
      case "word": {
        t.value = be(t.value);
        break;
      }
      default:
        gi(t);
    }
}
function gi(r) {
  throw new Error(`Unexpected value: ${r}`);
}
var gt = new Uint8Array(256);
function se(r) {
  let t = 0, i = r.length;
  for (let e = 0; e < i; e++) {
    let n = r.charCodeAt(e);
    switch (n) {
      case 92:
        e += 1;
        break;
      case 39:
      case 34:
        for (; ++e < i; ) {
          let s2 = r.charCodeAt(e);
          if (s2 === 92) {
            e += 1;
            continue;
          }
          if (s2 === n)
            break;
        }
        break;
      case 40:
        gt[t] = 41, t++;
        break;
      case 91:
        gt[t] = 93, t++;
        break;
      case 123:
        break;
      case 93:
      case 125:
      case 41:
        if (t === 0)
          return false;
        t > 0 && n === gt[t - 1] && t--;
        break;
      case 59:
        if (t === 0)
          return false;
        break;
    }
  }
  return true;
}
var hi = 58;
var Yt = 45;
var Zt = 97;
var Qt = 122;
function* Xt(r, t) {
  var _a2;
  let i = g(r, ":");
  if (t.theme.prefix) {
    if (i.length === 1 || i[0] !== t.theme.prefix)
      return null;
    i.shift();
  }
  let e = i.pop(), n = [];
  for (let p2 = i.length - 1; p2 >= 0; --p2) {
    let m = t.parseVariant(i[p2]);
    if (m === null)
      return;
    n.push(m);
  }
  let s2 = false;
  e[e.length - 1] === "!" ? (s2 = true, e = e.slice(0, -1)) : e[0] === "!" && (s2 = true, e = e.slice(1)), t.utilities.has(e, "static") && !e.includes("[") && (yield { kind: "static", root: e, variants: n, important: s2, raw: r });
  let [a, c = null, u2] = g(e, "/");
  if (u2)
    return;
  let f2 = c === null ? null : ht(c);
  if (c !== null && f2 === null)
    return;
  if (a[0] === "[") {
    if (a[a.length - 1] !== "]")
      return;
    let p2 = a.charCodeAt(1);
    if (p2 !== Yt && !(p2 >= Zt && p2 <= Qt))
      return;
    a = a.slice(1, -1);
    let m = a.indexOf(":");
    if (m === -1 || m === 0 || m === a.length - 1)
      return;
    let w2 = a.slice(0, m), v2 = ce(a.slice(m + 1));
    if (!se(v2))
      return;
    yield { kind: "arbitrary", property: w2, value: v2, modifier: f2, variants: n, important: s2, raw: r };
    return;
  }
  let g2;
  if (a[a.length - 1] === "]") {
    let p2 = a.indexOf("-[");
    if (p2 === -1)
      return;
    let m = a.slice(0, p2);
    if (!t.utilities.has(m, "functional"))
      return;
    let w2 = a.slice(p2 + 1);
    g2 = [[m, w2]];
  } else if (a[a.length - 1] === ")") {
    let p2 = a.indexOf("-(");
    if (p2 === -1)
      return;
    let m = a.slice(0, p2);
    if (!t.utilities.has(m, "functional"))
      return;
    let w2 = a.slice(p2 + 2, -1), v2 = g(w2, ":"), x = null;
    if (v2.length === 2 && (x = v2[0], w2 = v2[1]), w2[0] !== "-" || w2[1] !== "-" || !se(w2))
      return;
    g2 = [[m, x === null ? `[var(${w2})]` : `[${x}:var(${w2})]`]];
  } else
    g2 = tr(a, (p2) => t.utilities.has(p2, "functional"));
  for (let [p2, m] of g2) {
    let w2 = { kind: "functional", root: p2, modifier: f2, value: null, variants: n, important: s2, raw: r };
    if (m === null) {
      yield w2;
      continue;
    }
    {
      let v2 = m.indexOf("[");
      if (v2 !== -1) {
        if (m[m.length - 1] !== "]")
          return;
        let k2 = ce(m.slice(v2 + 1, -1));
        if (!se(k2))
          continue;
        let N2 = "";
        for (let b2 = 0; b2 < k2.length; b2++) {
          let S = k2.charCodeAt(b2);
          if (S === hi) {
            N2 = k2.slice(0, b2), k2 = k2.slice(b2 + 1);
            break;
          }
          if (!(S === Yt || S >= Zt && S <= Qt))
            break;
        }
        if (k2.length === 0 || k2.trim().length === 0)
          continue;
        w2.value = { kind: "arbitrary", dataType: N2 || null, value: k2 };
      } else {
        let k2 = c === null || ((_a2 = w2.modifier) == null ? void 0 : _a2.kind) === "arbitrary" ? null : `${m}/${c}`;
        w2.value = { kind: "named", value: m, fraction: k2 };
      }
    }
    yield w2;
  }
}
function ht(r) {
  if (r[0] === "[" && r[r.length - 1] === "]") {
    let t = ce(r.slice(1, -1));
    return !se(t) || t.length === 0 || t.trim().length === 0 ? null : { kind: "arbitrary", value: t };
  }
  return r[0] === "(" && r[r.length - 1] === ")" ? (r = r.slice(1, -1), r[0] !== "-" || r[1] !== "-" || !se(r) ? null : (r = `var(${r})`, { kind: "arbitrary", value: ce(r) })) : { kind: "named", value: r };
}
function er(r, t) {
  if (r[0] === "[" && r[r.length - 1] === "]") {
    if (r[1] === "@" && r.includes("&"))
      return null;
    let i = ce(r.slice(1, -1));
    if (!se(i) || i.length === 0 || i.trim().length === 0)
      return null;
    let e = i[0] === ">" || i[0] === "+" || i[0] === "~";
    return !e && i[0] !== "@" && !i.includes("&") && (i = `&:is(${i})`), { kind: "arbitrary", selector: i, relative: e };
  }
  {
    let [i, e = null, n] = g(r, "/");
    if (n)
      return null;
    let s2 = tr(i, (a) => t.variants.has(a));
    for (let [a, c] of s2)
      switch (t.variants.kind(a)) {
        case "static":
          return c !== null || e !== null ? null : { kind: "static", root: a };
        case "functional": {
          let u2 = e === null ? null : ht(e);
          if (e !== null && u2 === null)
            return null;
          if (c === null)
            return { kind: "functional", root: a, modifier: u2, value: null };
          if (c[c.length - 1] === "]") {
            if (c[0] !== "[")
              continue;
            let f2 = ce(c.slice(1, -1));
            return !se(f2) || f2.length === 0 || f2.trim().length === 0 ? null : { kind: "functional", root: a, modifier: u2, value: { kind: "arbitrary", value: f2 } };
          }
          if (c[c.length - 1] === ")") {
            if (c[0] !== "(")
              continue;
            let f2 = ce(c.slice(1, -1));
            return !se(f2) || f2.length === 0 || f2.trim().length === 0 || f2[0] !== "-" || f2[1] !== "-" ? null : { kind: "functional", root: a, modifier: u2, value: { kind: "arbitrary", value: `var(${f2})` } };
          }
          return { kind: "functional", root: a, modifier: u2, value: { kind: "named", value: c } };
        }
        case "compound": {
          if (c === null)
            return null;
          let u2 = t.parseVariant(c);
          if (u2 === null || !t.variants.compoundsWith(a, u2))
            return null;
          let f2 = e === null ? null : ht(e);
          return e !== null && f2 === null ? null : { kind: "compound", root: a, modifier: f2, variant: u2 };
        }
      }
  }
  return null;
}
function* tr(r, t) {
  t(r) && (yield [r, null]);
  let i = r.lastIndexOf("-");
  for (; i > 0; ) {
    let e = r.slice(0, i);
    if (t(e)) {
      let n = [e, r.slice(i + 1)];
      if (n[1] === "")
        break;
      yield n;
    }
    i = r.lastIndexOf("-", i - 1);
  }
  r[0] === "@" && t("@") && (yield ["@", r.slice(1)]);
}
function rr(r, t) {
  let i = [];
  for (let n of t.variants)
    i.unshift(He(n));
  r.theme.prefix && i.unshift(r.theme.prefix);
  let e = "";
  if (t.kind === "static" && (e += t.root), t.kind === "functional" && (e += t.root, t.value))
    if (t.value.kind === "arbitrary") {
      if (t.value !== null) {
        let n = wt(t.value.value), s2 = n ? t.value.value.slice(4, -1) : t.value.value, [a, c] = n ? ["(", ")"] : ["[", "]"];
        t.value.dataType ? e += `-${a}${t.value.dataType}:${ke(s2)}${c}` : e += `-${a}${ke(s2)}${c}`;
      }
    } else
      t.value.kind === "named" && (e += `-${t.value.value}`);
  return t.kind === "arbitrary" && (e += `[${t.property}:${ke(t.value)}]`), (t.kind === "arbitrary" || t.kind === "functional") && (e += ir(t.modifier)), t.important && (e += "!"), i.push(e), i.join(":");
}
function ir(r) {
  if (r === null)
    return "";
  let t = wt(r.value), i = t ? r.value.slice(4, -1) : r.value, [e, n] = t ? ["(", ")"] : ["[", "]"];
  return r.kind === "arbitrary" ? `/${e}${ke(i)}${n}` : r.kind === "named" ? `/${r.value}` : "";
}
function He(r) {
  if (r.kind === "static")
    return r.root;
  if (r.kind === "arbitrary")
    return `[${ke(yi(r.selector))}]`;
  let t = "";
  if (r.kind === "functional") {
    t += r.root;
    let i = r.root !== "@";
    if (r.value)
      if (r.value.kind === "arbitrary") {
        let e = wt(r.value.value), n = e ? r.value.value.slice(4, -1) : r.value.value, [s2, a] = e ? ["(", ")"] : ["[", "]"];
        t += `${i ? "-" : ""}${s2}${ke(n)}${a}`;
      } else
        r.value.kind === "named" && (t += `${i ? "-" : ""}${r.value.value}`);
  }
  return r.kind === "compound" && (t += r.root, t += "-", t += He(r.variant)), (r.kind === "functional" || r.kind === "compound") && (t += ir(r.modifier)), t;
}
var vi = new W2((r) => {
  let t = B2(r), i = /* @__PURE__ */ new Set();
  return ee2(t, (e, { parent: n }) => {
    let s2 = n === null ? t : n.nodes ?? [];
    if (e.kind === "word" && (e.value === "+" || e.value === "-" || e.value === "*" || e.value === "/")) {
      let a = s2.indexOf(e) ?? -1;
      if (a === -1)
        return;
      let c = s2[a - 1];
      if ((c == null ? void 0 : c.kind) !== "separator" || c.value !== " ")
        return;
      let u2 = s2[a + 1];
      if ((u2 == null ? void 0 : u2.kind) !== "separator" || u2.value !== " ")
        return;
      i.add(c), i.add(u2);
    } else
      e.kind === "separator" && e.value.trim() === "/" ? e.value = "/" : e.kind === "separator" && e.value.length > 0 && e.value.trim() === "" ? (s2[0] === e || s2[s2.length - 1] === e) && i.add(e) : e.kind === "separator" && e.value.trim() === "," && (e.value = ",");
  }), i.size > 0 && ee2(t, (e, { replaceWith: n }) => {
    i.has(e) && (i.delete(e), n([]));
  }), vt(t), Y2(t);
});
function ke(r) {
  return vi.get(r);
}
var wi = new W2((r) => {
  let t = B2(r);
  return t.length === 3 && t[0].kind === "word" && t[0].value === "&" && t[1].kind === "separator" && t[1].value === ":" && t[2].kind === "function" && t[2].value === "is" ? Y2(t[2].nodes) : r;
});
function yi(r) {
  return wi.get(r);
}
function vt(r) {
  for (let t of r)
    switch (t.kind) {
      case "function": {
        if (t.value === "url" || t.value.endsWith("_url")) {
          t.value = Te(t.value);
          break;
        }
        if (t.value === "var" || t.value.endsWith("_var") || t.value === "theme" || t.value.endsWith("_theme")) {
          t.value = Te(t.value);
          for (let i = 0; i < t.nodes.length; i++)
            vt([t.nodes[i]]);
          break;
        }
        t.value = Te(t.value), vt(t.nodes);
        break;
      }
      case "separator":
        t.value = Te(t.value);
        break;
      case "word": {
        (t.value[0] !== "-" || t.value[1] !== "-") && (t.value = Te(t.value));
        break;
      }
      default:
        ki(t);
    }
}
var bi = new W2((r) => {
  let t = B2(r);
  return t.length === 1 && t[0].kind === "function" && t[0].value === "var";
});
function wt(r) {
  return bi.get(r);
}
function ki(r) {
  throw new Error(`Unexpected value: ${r}`);
}
function Te(r) {
  return r.replaceAll("_", String.raw`\_`).replaceAll(" ", "_");
}
function we(r, t, i) {
  if (r === t)
    return 0;
  let e = r.indexOf("("), n = t.indexOf("("), s2 = e === -1 ? r.replace(/[\d.]+/g, "") : r.slice(0, e), a = n === -1 ? t.replace(/[\d.]+/g, "") : t.slice(0, n), c = (s2 === a ? 0 : s2 < a ? -1 : 1) || (i === "asc" ? parseInt(r) - parseInt(t) : parseInt(t) - parseInt(r));
  return Number.isNaN(c) ? r < t ? -1 : 1 : c;
}
var xi = /* @__PURE__ */ new Set(["inset", "inherit", "initial", "revert", "unset"]);
var nr = /^-?(\d+|\.\d+)(.*?)$/g;
function Ee(r, t) {
  return g(r, ",").map((e) => {
    e = e.trim();
    let n = g(e, " ").filter((f2) => f2.trim() !== ""), s2 = null, a = null, c = null;
    for (let f2 of n)
      xi.has(f2) || (nr.test(f2) ? (a === null ? a = f2 : c === null && (c = f2), nr.lastIndex = 0) : s2 === null && (s2 = f2));
    if (a === null || c === null)
      return e;
    let u2 = t(s2 ?? "currentcolor");
    return s2 !== null ? e.replace(s2, u2) : `${e} ${u2}`;
  }).join(", ");
}
var Ai = /^-?[a-z][a-zA-Z0-9/%._-]*$/;
var Ci = /^-?[a-z][a-zA-Z0-9/%._-]*-\*$/;
var Ze = ["0", "0.5", "1", "1.5", "2", "2.5", "3", "3.5", "4", "5", "6", "7", "8", "9", "10", "11", "12", "14", "16", "20", "24", "28", "32", "36", "40", "44", "48", "52", "56", "60", "64", "72", "80", "96"];
var yt = class {
  constructor() {
    __publicField(this, "utilities", new W2(() => []));
    __publicField(this, "completions", /* @__PURE__ */ new Map());
  }
  static(t, i) {
    this.utilities.get(t).push({ kind: "static", compileFn: i });
  }
  functional(t, i, e) {
    this.utilities.get(t).push({ kind: "functional", compileFn: i, options: e });
  }
  has(t, i) {
    return this.utilities.has(t) && this.utilities.get(t).some((e) => e.kind === i);
  }
  get(t) {
    return this.utilities.has(t) ? this.utilities.get(t) : [];
  }
  getCompletions(t) {
    var _a2;
    return ((_a2 = this.completions.get(t)) == null ? void 0 : _a2()) ?? [];
  }
  suggest(t, i) {
    this.completions.set(t, i);
  }
  keys(t) {
    let i = [];
    for (let [e, n] of this.utilities.entries())
      for (let s2 of n)
        if (s2.kind === t) {
          i.push(e);
          break;
        }
    return i;
  }
};
function $2(r, t, i) {
  return z2("@property", r, [l3("syntax", i ? `"${i}"` : '"*"'), l3("inherits", "false"), ...t ? [l3("initial-value", t)] : []]);
}
function Z2(r, t) {
  if (t === null)
    return r;
  let i = Number(t);
  return Number.isNaN(i) || (t = `${i * 100}%`), t === "100%" ? r : `color-mix(in oklab, ${r} ${t}, transparent)`;
}
function lr(r, t) {
  let i = Number(t);
  return Number.isNaN(i) || (t = `${i * 100}%`), `oklab(from ${r} l a b / ${t})`;
}
function Q2(r, t, i) {
  if (!t)
    return r;
  if (t.kind === "arbitrary")
    return Z2(r, t.value);
  let e = i.resolve(t.value, ["--opacity"]);
  return e ? Z2(r, e) : de(t.value) ? Z2(r, `${t.value}%`) : null;
}
function te2(r, t, i) {
  let e = null;
  switch (r.value.value) {
    case "inherit": {
      e = "inherit";
      break;
    }
    case "transparent": {
      e = "transparent";
      break;
    }
    case "current": {
      e = "currentcolor";
      break;
    }
    default: {
      e = t.resolve(r.value.value, i);
      break;
    }
  }
  return e ? Q2(e, r.modifier, t) : null;
}
var ar = /(\d+)_(\d+)/g;
function sr(r) {
  let t = new yt();
  function i(o, d2) {
    function* h2(y2) {
      for (let C2 of r.keysInNamespaces(y2))
        yield C2.replace(ar, (R2, V2, T2) => `${V2}.${T2}`);
    }
    let A2 = ["1/2", "1/3", "2/3", "1/4", "2/4", "3/4", "1/5", "2/5", "3/5", "4/5", "1/6", "2/6", "3/6", "4/6", "5/6", "1/12", "2/12", "3/12", "4/12", "5/12", "6/12", "7/12", "8/12", "9/12", "10/12", "11/12"];
    t.suggest(o, () => {
      let y2 = [];
      for (let C2 of d2()) {
        if (typeof C2 == "string") {
          y2.push({ values: [C2], modifiers: [] });
          continue;
        }
        let R2 = [...C2.values ?? [], ...h2(C2.valueThemeKeys ?? [])], V2 = [...C2.modifiers ?? [], ...h2(C2.modifierThemeKeys ?? [])];
        C2.supportsFractions && R2.push(...A2), C2.hasDefaultValue && R2.unshift(null), y2.push({ supportsNegative: C2.supportsNegative, values: R2, modifiers: V2 });
      }
      return y2;
    });
  }
  function e(o, d2) {
    t.static(o, () => d2.map((h2) => typeof h2 == "function" ? h2() : l3(h2[0], h2[1])));
  }
  function n(o, d2) {
    function h2({ negative: A2 }) {
      return (y2) => {
        let C2 = null, R2 = null;
        if (y2.value)
          if (y2.value.kind === "arbitrary") {
            if (y2.modifier)
              return;
            C2 = y2.value.value, R2 = y2.value.dataType;
          } else {
            if (C2 = r.resolve(y2.value.fraction ?? y2.value.value, d2.themeKeys ?? []), C2 === null && d2.supportsFractions && y2.value.fraction) {
              let [V2, T2] = g(y2.value.fraction, "/");
              if (!p(V2) || !p(T2))
                return;
              C2 = `calc(${y2.value.fraction} * 100%)`;
            }
            if (C2 === null && A2 && d2.handleNegativeBareValue) {
              if (C2 = d2.handleNegativeBareValue(y2.value), !(C2 == null ? void 0 : C2.includes("/")) && y2.modifier)
                return;
              if (C2 !== null)
                return d2.handle(C2, null);
            }
            if (C2 === null && d2.handleBareValue && (C2 = d2.handleBareValue(y2.value), !(C2 == null ? void 0 : C2.includes("/")) && y2.modifier))
              return;
          }
        else {
          if (y2.modifier)
            return;
          C2 = d2.defaultValue !== void 0 ? d2.defaultValue : r.resolve(null, d2.themeKeys ?? []);
        }
        if (C2 !== null)
          return d2.handle(A2 ? `calc(${C2} * -1)` : C2, R2);
      };
    }
    d2.supportsNegative && t.functional(`-${o}`, h2({ negative: true })), t.functional(o, h2({ negative: false })), i(o, () => [{ supportsNegative: d2.supportsNegative, valueThemeKeys: d2.themeKeys ?? [], hasDefaultValue: d2.defaultValue !== void 0 && d2.defaultValue !== null, supportsFractions: d2.supportsFractions }]);
  }
  function s2(o, d2) {
    t.functional(o, (h2) => {
      if (!h2.value)
        return;
      let A2 = null;
      if (h2.value.kind === "arbitrary" ? (A2 = h2.value.value, A2 = Q2(A2, h2.modifier, r)) : A2 = te2(h2, r, d2.themeKeys), A2 !== null)
        return d2.handle(A2);
    }), i(o, () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: d2.themeKeys, modifiers: Array.from({ length: 21 }, (h2, A2) => `${A2 * 5}`) }]);
  }
  function a(o, d2, h2, { supportsNegative: A2 = false, supportsFractions: y2 = false } = {}) {
    A2 && t.static(`-${o}-px`, () => h2("-1px")), t.static(`${o}-px`, () => h2("1px")), n(o, { themeKeys: d2, supportsFractions: y2, supportsNegative: A2, defaultValue: null, handleBareValue: ({ value: C2 }) => {
      let R2 = r.resolve(null, ["--spacing"]);
      return !R2 || !ue(C2) ? null : `calc(${R2} * ${C2})`;
    }, handleNegativeBareValue: ({ value: C2 }) => {
      let R2 = r.resolve(null, ["--spacing"]);
      return !R2 || !ue(C2) ? null : `calc(${R2} * -${C2})`;
    }, handle: h2 }), i(o, () => [{ values: r.get(["--spacing"]) ? Ze : [], supportsNegative: A2, supportsFractions: y2, valueThemeKeys: d2 }]);
  }
  e("sr-only", [["position", "absolute"], ["width", "1px"], ["height", "1px"], ["padding", "0"], ["margin", "-1px"], ["overflow", "hidden"], ["clip", "rect(0, 0, 0, 0)"], ["white-space", "nowrap"], ["border-width", "0"]]), e("not-sr-only", [["position", "static"], ["width", "auto"], ["height", "auto"], ["padding", "0"], ["margin", "0"], ["overflow", "visible"], ["clip", "auto"], ["white-space", "normal"]]), e("pointer-events-none", [["pointer-events", "none"]]), e("pointer-events-auto", [["pointer-events", "auto"]]), e("visible", [["visibility", "visible"]]), e("invisible", [["visibility", "hidden"]]), e("collapse", [["visibility", "collapse"]]), e("static", [["position", "static"]]), e("fixed", [["position", "fixed"]]), e("absolute", [["position", "absolute"]]), e("relative", [["position", "relative"]]), e("sticky", [["position", "sticky"]]);
  for (let [o, d2] of [["inset", "inset"], ["inset-x", "inset-inline"], ["inset-y", "inset-block"], ["start", "inset-inline-start"], ["end", "inset-inline-end"], ["top", "top"], ["right", "right"], ["bottom", "bottom"], ["left", "left"]])
    e(`${o}-auto`, [[d2, "auto"]]), e(`${o}-full`, [[d2, "100%"]]), e(`-${o}-full`, [[d2, "-100%"]]), a(o, ["--inset", "--spacing"], (h2) => [l3(d2, h2)], { supportsNegative: true, supportsFractions: true });
  e("isolate", [["isolation", "isolate"]]), e("isolation-auto", [["isolation", "auto"]]), e("z-auto", [["z-index", "auto"]]), n("z", { supportsNegative: true, handleBareValue: ({ value: o }) => p(o) ? o : null, themeKeys: ["--z-index"], handle: (o) => [l3("z-index", o)] }), i("z", () => [{ supportsNegative: true, values: ["0", "10", "20", "30", "40", "50"], valueThemeKeys: ["--z-index"] }]), e("order-first", [["order", "-9999"]]), e("order-last", [["order", "9999"]]), e("order-none", [["order", "0"]]), n("order", { supportsNegative: true, handleBareValue: ({ value: o }) => p(o) ? o : null, themeKeys: ["--order"], handle: (o) => [l3("order", o)] }), i("order", () => [{ supportsNegative: true, values: Array.from({ length: 12 }, (o, d2) => `${d2 + 1}`), valueThemeKeys: ["--order"] }]), e("col-auto", [["grid-column", "auto"]]), n("col", { supportsNegative: true, handleBareValue: ({ value: o }) => p(o) ? o : null, themeKeys: ["--grid-column"], handle: (o) => [l3("grid-column", o)] }), e("col-span-full", [["grid-column", "1 / -1"]]), n("col-span", { handleBareValue: ({ value: o }) => p(o) ? o : null, handle: (o) => [l3("grid-column", `span ${o} / span ${o}`)] }), e("col-start-auto", [["grid-column-start", "auto"]]), n("col-start", { supportsNegative: true, handleBareValue: ({ value: o }) => p(o) ? o : null, themeKeys: ["--grid-column-start"], handle: (o) => [l3("grid-column-start", o)] }), e("col-end-auto", [["grid-column-end", "auto"]]), n("col-end", { supportsNegative: true, handleBareValue: ({ value: o }) => p(o) ? o : null, themeKeys: ["--grid-column-end"], handle: (o) => [l3("grid-column-end", o)] }), i("col-span", () => [{ values: Array.from({ length: 12 }, (o, d2) => `${d2 + 1}`), valueThemeKeys: [] }]), i("col-start", () => [{ supportsNegative: true, values: Array.from({ length: 13 }, (o, d2) => `${d2 + 1}`), valueThemeKeys: ["--grid-column-start"] }]), i("col-end", () => [{ supportsNegative: true, values: Array.from({ length: 13 }, (o, d2) => `${d2 + 1}`), valueThemeKeys: ["--grid-column-end"] }]), e("row-auto", [["grid-row", "auto"]]), n("row", { supportsNegative: true, handleBareValue: ({ value: o }) => p(o) ? o : null, themeKeys: ["--grid-row"], handle: (o) => [l3("grid-row", o)] }), e("row-span-full", [["grid-row", "1 / -1"]]), n("row-span", { themeKeys: [], handleBareValue: ({ value: o }) => p(o) ? o : null, handle: (o) => [l3("grid-row", `span ${o} / span ${o}`)] }), e("row-start-auto", [["grid-row-start", "auto"]]), n("row-start", { supportsNegative: true, handleBareValue: ({ value: o }) => p(o) ? o : null, themeKeys: ["--grid-row-start"], handle: (o) => [l3("grid-row-start", o)] }), e("row-end-auto", [["grid-row-end", "auto"]]), n("row-end", { supportsNegative: true, handleBareValue: ({ value: o }) => p(o) ? o : null, themeKeys: ["--grid-row-end"], handle: (o) => [l3("grid-row-end", o)] }), i("row-span", () => [{ values: Array.from({ length: 12 }, (o, d2) => `${d2 + 1}`), valueThemeKeys: [] }]), i("row-start", () => [{ supportsNegative: true, values: Array.from({ length: 13 }, (o, d2) => `${d2 + 1}`), valueThemeKeys: ["--grid-row-start"] }]), i("row-end", () => [{ supportsNegative: true, values: Array.from({ length: 13 }, (o, d2) => `${d2 + 1}`), valueThemeKeys: ["--grid-row-end"] }]), e("float-start", [["float", "inline-start"]]), e("float-end", [["float", "inline-end"]]), e("float-right", [["float", "right"]]), e("float-left", [["float", "left"]]), e("float-none", [["float", "none"]]), e("clear-start", [["clear", "inline-start"]]), e("clear-end", [["clear", "inline-end"]]), e("clear-right", [["clear", "right"]]), e("clear-left", [["clear", "left"]]), e("clear-both", [["clear", "both"]]), e("clear-none", [["clear", "none"]]);
  for (let [o, d2] of [["m", "margin"], ["mx", "margin-inline"], ["my", "margin-block"], ["ms", "margin-inline-start"], ["me", "margin-inline-end"], ["mt", "margin-top"], ["mr", "margin-right"], ["mb", "margin-bottom"], ["ml", "margin-left"]])
    e(`${o}-auto`, [[d2, "auto"]]), a(o, ["--margin", "--spacing"], (h2) => [l3(d2, h2)], { supportsNegative: true });
  e("box-border", [["box-sizing", "border-box"]]), e("box-content", [["box-sizing", "content-box"]]), e("line-clamp-none", [["overflow", "visible"], ["display", "block"], ["-webkit-box-orient", "horizontal"], ["-webkit-line-clamp", "unset"]]), n("line-clamp", { themeKeys: ["--line-clamp"], handleBareValue: ({ value: o }) => p(o) ? o : null, handle: (o) => [l3("overflow", "hidden"), l3("display", "-webkit-box"), l3("-webkit-box-orient", "vertical"), l3("-webkit-line-clamp", o)] }), i("line-clamp", () => [{ values: ["1", "2", "3", "4", "5", "6"], valueThemeKeys: ["--line-clamp"] }]), e("block", [["display", "block"]]), e("inline-block", [["display", "inline-block"]]), e("inline", [["display", "inline"]]), e("hidden", [["display", "none"]]), e("inline-flex", [["display", "inline-flex"]]), e("table", [["display", "table"]]), e("inline-table", [["display", "inline-table"]]), e("table-caption", [["display", "table-caption"]]), e("table-cell", [["display", "table-cell"]]), e("table-column", [["display", "table-column"]]), e("table-column-group", [["display", "table-column-group"]]), e("table-footer-group", [["display", "table-footer-group"]]), e("table-header-group", [["display", "table-header-group"]]), e("table-row-group", [["display", "table-row-group"]]), e("table-row", [["display", "table-row"]]), e("flow-root", [["display", "flow-root"]]), e("flex", [["display", "flex"]]), e("grid", [["display", "grid"]]), e("inline-grid", [["display", "inline-grid"]]), e("contents", [["display", "contents"]]), e("list-item", [["display", "list-item"]]), e("field-sizing-content", [["field-sizing", "content"]]), e("field-sizing-fixed", [["field-sizing", "fixed"]]), e("aspect-auto", [["aspect-ratio", "auto"]]), e("aspect-square", [["aspect-ratio", "1 / 1"]]), n("aspect", { themeKeys: ["--aspect"], handleBareValue: ({ fraction: o }) => {
    if (o === null)
      return null;
    let [d2, h2] = g(o, "/");
    return !p(d2) || !p(h2) ? null : o;
  }, handle: (o) => [l3("aspect-ratio", o)] });
  for (let [o, d2] of [["full", "100%"], ["svw", "100svw"], ["lvw", "100lvw"], ["dvw", "100dvw"], ["svh", "100svh"], ["lvh", "100lvh"], ["dvh", "100dvh"], ["min", "min-content"], ["max", "max-content"], ["fit", "fit-content"]])
    e(`size-${o}`, [["--tw-sort", "size"], ["width", d2], ["height", d2]]), e(`w-${o}`, [["width", d2]]), e(`h-${o}`, [["height", d2]]), e(`min-w-${o}`, [["min-width", d2]]), e(`min-h-${o}`, [["min-height", d2]]), e(`max-w-${o}`, [["max-width", d2]]), e(`max-h-${o}`, [["max-height", d2]]);
  e("size-auto", [["--tw-sort", "size"], ["width", "auto"], ["height", "auto"]]), e("w-auto", [["width", "auto"]]), e("h-auto", [["height", "auto"]]), e("min-w-auto", [["min-width", "auto"]]), e("min-h-auto", [["min-height", "auto"]]), e("h-lh", [["height", "1lh"]]), e("min-h-lh", [["min-height", "1lh"]]), e("max-h-lh", [["max-height", "1lh"]]), e("w-screen", [["width", "100vw"]]), e("min-w-screen", [["min-width", "100vw"]]), e("max-w-screen", [["max-width", "100vw"]]), e("h-screen", [["height", "100vh"]]), e("min-h-screen", [["min-height", "100vh"]]), e("max-h-screen", [["max-height", "100vh"]]), e("max-w-none", [["max-width", "none"]]), e("max-h-none", [["max-height", "none"]]), a("size", ["--size", "--spacing"], (o) => [l3("--tw-sort", "size"), l3("width", o), l3("height", o)], { supportsFractions: true });
  for (let [o, d2, h2] of [["w", ["--width", "--spacing", "--container"], "width"], ["min-w", ["--min-width", "--spacing", "--container"], "min-width"], ["max-w", ["--max-width", "--spacing", "--container"], "max-width"], ["h", ["--height", "--spacing"], "height"], ["min-h", ["--min-height", "--height", "--spacing"], "min-height"], ["max-h", ["--max-height", "--height", "--spacing"], "max-height"]])
    a(o, d2, (A2) => [l3(h2, A2)], { supportsFractions: true });
  t.static("container", () => {
    let o = [...r.namespace("--breakpoint").values()];
    o.sort((h2, A2) => we(h2, A2, "asc"));
    let d2 = [l3("--tw-sort", "--tw-container-component"), l3("width", "100%")];
    for (let h2 of o)
      d2.push(z2("@media", `(width >= ${h2})`, [l3("max-width", h2)]));
    return d2;
  }), e("flex-auto", [["flex", "auto"]]), e("flex-initial", [["flex", "0 auto"]]), e("flex-none", [["flex", "none"]]), t.functional("flex", (o) => {
    if (o.value) {
      if (o.value.kind === "arbitrary")
        return o.modifier ? void 0 : [l3("flex", o.value.value)];
      if (o.value.fraction) {
        let [d2, h2] = g(o.value.fraction, "/");
        return !p(d2) || !p(h2) ? void 0 : [l3("flex", `calc(${o.value.fraction} * 100%)`)];
      }
      if (p(o.value.value))
        return o.modifier ? void 0 : [l3("flex", o.value.value)];
    }
  }), i("flex", () => [{ supportsFractions: true }]), n("shrink", { defaultValue: "1", handleBareValue: ({ value: o }) => p(o) ? o : null, handle: (o) => [l3("flex-shrink", o)] }), n("grow", { defaultValue: "1", handleBareValue: ({ value: o }) => p(o) ? o : null, handle: (o) => [l3("flex-grow", o)] }), i("shrink", () => [{ values: ["0"], valueThemeKeys: [], hasDefaultValue: true }]), i("grow", () => [{ values: ["0"], valueThemeKeys: [], hasDefaultValue: true }]), e("basis-auto", [["flex-basis", "auto"]]), e("basis-full", [["flex-basis", "100%"]]), a("basis", ["--flex-basis", "--spacing", "--container"], (o) => [l3("flex-basis", o)], { supportsFractions: true }), e("table-auto", [["table-layout", "auto"]]), e("table-fixed", [["table-layout", "fixed"]]), e("caption-top", [["caption-side", "top"]]), e("caption-bottom", [["caption-side", "bottom"]]), e("border-collapse", [["border-collapse", "collapse"]]), e("border-separate", [["border-collapse", "separate"]]);
  let c = () => I2([$2("--tw-border-spacing-x", "0", "<length>"), $2("--tw-border-spacing-y", "0", "<length>")]);
  a("border-spacing", ["--border-spacing", "--spacing"], (o) => [c(), l3("--tw-border-spacing-x", o), l3("--tw-border-spacing-y", o), l3("border-spacing", "var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]), a("border-spacing-x", ["--border-spacing", "--spacing"], (o) => [c(), l3("--tw-border-spacing-x", o), l3("border-spacing", "var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]), a("border-spacing-y", ["--border-spacing", "--spacing"], (o) => [c(), l3("--tw-border-spacing-y", o), l3("border-spacing", "var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]), e("origin-center", [["transform-origin", "center"]]), e("origin-top", [["transform-origin", "top"]]), e("origin-top-right", [["transform-origin", "top right"]]), e("origin-right", [["transform-origin", "right"]]), e("origin-bottom-right", [["transform-origin", "bottom right"]]), e("origin-bottom", [["transform-origin", "bottom"]]), e("origin-bottom-left", [["transform-origin", "bottom left"]]), e("origin-left", [["transform-origin", "left"]]), e("origin-top-left", [["transform-origin", "top left"]]), n("origin", { themeKeys: ["--transform-origin"], handle: (o) => [l3("transform-origin", o)] }), e("perspective-origin-center", [["perspective-origin", "center"]]), e("perspective-origin-top", [["perspective-origin", "top"]]), e("perspective-origin-top-right", [["perspective-origin", "top right"]]), e("perspective-origin-right", [["perspective-origin", "right"]]), e("perspective-origin-bottom-right", [["perspective-origin", "bottom right"]]), e("perspective-origin-bottom", [["perspective-origin", "bottom"]]), e("perspective-origin-bottom-left", [["perspective-origin", "bottom left"]]), e("perspective-origin-left", [["perspective-origin", "left"]]), e("perspective-origin-top-left", [["perspective-origin", "top left"]]), n("perspective-origin", { themeKeys: ["--perspective-origin"], handle: (o) => [l3("perspective-origin", o)] }), e("perspective-none", [["perspective", "none"]]), n("perspective", { themeKeys: ["--perspective"], handle: (o) => [l3("perspective", o)] });
  let u2 = () => I2([$2("--tw-translate-x", "0"), $2("--tw-translate-y", "0"), $2("--tw-translate-z", "0")]);
  e("translate-none", [["translate", "none"]]), e("-translate-full", [u2, ["--tw-translate-x", "-100%"], ["--tw-translate-y", "-100%"], ["translate", "var(--tw-translate-x) var(--tw-translate-y)"]]), e("translate-full", [u2, ["--tw-translate-x", "100%"], ["--tw-translate-y", "100%"], ["translate", "var(--tw-translate-x) var(--tw-translate-y)"]]), a("translate", ["--translate", "--spacing"], (o) => [u2(), l3("--tw-translate-x", o), l3("--tw-translate-y", o), l3("translate", "var(--tw-translate-x) var(--tw-translate-y)")], { supportsNegative: true, supportsFractions: true });
  for (let o of ["x", "y"])
    e(`-translate-${o}-full`, [u2, [`--tw-translate-${o}`, "-100%"], ["translate", "var(--tw-translate-x) var(--tw-translate-y)"]]), e(`translate-${o}-full`, [u2, [`--tw-translate-${o}`, "100%"], ["translate", "var(--tw-translate-x) var(--tw-translate-y)"]]), a(`translate-${o}`, ["--translate", "--spacing"], (d2) => [u2(), l3(`--tw-translate-${o}`, d2), l3("translate", "var(--tw-translate-x) var(--tw-translate-y)")], { supportsNegative: true, supportsFractions: true });
  a("translate-z", ["--translate", "--spacing"], (o) => [u2(), l3("--tw-translate-z", o), l3("translate", "var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")], { supportsNegative: true }), e("translate-3d", [u2, ["translate", "var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);
  let f2 = () => I2([$2("--tw-scale-x", "1"), $2("--tw-scale-y", "1"), $2("--tw-scale-z", "1")]);
  e("scale-none", [["scale", "none"]]);
  function g2({ negative: o }) {
    return (d2) => {
      if (!d2.value || d2.modifier)
        return;
      let h2;
      return d2.value.kind === "arbitrary" ? (h2 = d2.value.value, h2 = o ? `calc(${h2} * -1)` : h2, [l3("scale", h2)]) : (h2 = r.resolve(d2.value.value, ["--scale"]), !h2 && p(d2.value.value) && (h2 = `${d2.value.value}%`), h2 ? (h2 = o ? `calc(${h2} * -1)` : h2, [f2(), l3("--tw-scale-x", h2), l3("--tw-scale-y", h2), l3("--tw-scale-z", h2), l3("scale", "var(--tw-scale-x) var(--tw-scale-y)")]) : void 0);
    };
  }
  t.functional("-scale", g2({ negative: true })), t.functional("scale", g2({ negative: false })), i("scale", () => [{ supportsNegative: true, values: ["0", "50", "75", "90", "95", "100", "105", "110", "125", "150", "200"], valueThemeKeys: ["--scale"] }]);
  for (let o of ["x", "y", "z"])
    n(`scale-${o}`, { supportsNegative: true, themeKeys: ["--scale"], handleBareValue: ({ value: d2 }) => p(d2) ? `${d2}%` : null, handle: (d2) => [f2(), l3(`--tw-scale-${o}`, d2), l3("scale", `var(--tw-scale-x) var(--tw-scale-y)${o === "z" ? " var(--tw-scale-z)" : ""}`)] }), i(`scale-${o}`, () => [{ supportsNegative: true, values: ["0", "50", "75", "90", "95", "100", "105", "110", "125", "150", "200"], valueThemeKeys: ["--scale"] }]);
  e("scale-3d", [f2, ["scale", "var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]), e("rotate-none", [["rotate", "none"]]);
  function p2({ negative: o }) {
    return (d2) => {
      if (!d2.value || d2.modifier)
        return;
      let h2;
      if (d2.value.kind === "arbitrary") {
        h2 = d2.value.value;
        let A2 = d2.value.dataType ?? pe(h2, ["angle", "vector"]);
        if (A2 === "vector")
          return [l3("rotate", `${h2} var(--tw-rotate)`)];
        if (A2 !== "angle")
          return [l3("rotate", o ? `calc(${h2} * -1)` : h2)];
      } else if (h2 = r.resolve(d2.value.value, ["--rotate"]), !h2 && p(d2.value.value) && (h2 = `${d2.value.value}deg`), !h2)
        return;
      return [l3("rotate", o ? `calc(${h2} * -1)` : h2)];
    };
  }
  t.functional("-rotate", p2({ negative: true })), t.functional("rotate", p2({ negative: false })), i("rotate", () => [{ supportsNegative: true, values: ["0", "1", "2", "3", "6", "12", "45", "90", "180"], valueThemeKeys: ["--rotate"] }]);
  {
    let o = ["var(--tw-rotate-x,)", "var(--tw-rotate-y,)", "var(--tw-rotate-z,)", "var(--tw-skew-x,)", "var(--tw-skew-y,)"].join(" "), d2 = () => I2([$2("--tw-rotate-x"), $2("--tw-rotate-y"), $2("--tw-rotate-z"), $2("--tw-skew-x"), $2("--tw-skew-y")]);
    for (let h2 of ["x", "y", "z"])
      n(`rotate-${h2}`, { supportsNegative: true, themeKeys: ["--rotate"], handleBareValue: ({ value: A2 }) => p(A2) ? `${A2}deg` : null, handle: (A2) => [d2(), l3(`--tw-rotate-${h2}`, `rotate${h2.toUpperCase()}(${A2})`), l3("transform", o)] }), i(`rotate-${h2}`, () => [{ supportsNegative: true, values: ["0", "1", "2", "3", "6", "12", "45", "90", "180"], valueThemeKeys: ["--rotate"] }]);
    n("skew", { supportsNegative: true, themeKeys: ["--skew"], handleBareValue: ({ value: h2 }) => p(h2) ? `${h2}deg` : null, handle: (h2) => [d2(), l3("--tw-skew-x", `skewX(${h2})`), l3("--tw-skew-y", `skewY(${h2})`), l3("transform", o)] }), n("skew-x", { supportsNegative: true, themeKeys: ["--skew"], handleBareValue: ({ value: h2 }) => p(h2) ? `${h2}deg` : null, handle: (h2) => [d2(), l3("--tw-skew-x", `skewX(${h2})`), l3("transform", o)] }), n("skew-y", { supportsNegative: true, themeKeys: ["--skew"], handleBareValue: ({ value: h2 }) => p(h2) ? `${h2}deg` : null, handle: (h2) => [d2(), l3("--tw-skew-y", `skewY(${h2})`), l3("transform", o)] }), i("skew", () => [{ supportsNegative: true, values: ["0", "1", "2", "3", "6", "12"], valueThemeKeys: ["--skew"] }]), i("skew-x", () => [{ supportsNegative: true, values: ["0", "1", "2", "3", "6", "12"], valueThemeKeys: ["--skew"] }]), i("skew-y", () => [{ supportsNegative: true, values: ["0", "1", "2", "3", "6", "12"], valueThemeKeys: ["--skew"] }]), t.functional("transform", (h2) => {
      if (h2.modifier)
        return;
      let A2 = null;
      if (h2.value ? h2.value.kind === "arbitrary" && (A2 = h2.value.value) : A2 = o, A2 !== null)
        return [d2(), l3("transform", A2)];
    }), i("transform", () => [{ hasDefaultValue: true }]), e("transform-cpu", [["transform", o]]), e("transform-gpu", [["transform", `translateZ(0) ${o}`]]), e("transform-none", [["transform", "none"]]);
  }
  e("transform-flat", [["transform-style", "flat"]]), e("transform-3d", [["transform-style", "preserve-3d"]]), e("transform-content", [["transform-box", "content-box"]]), e("transform-border", [["transform-box", "border-box"]]), e("transform-fill", [["transform-box", "fill-box"]]), e("transform-stroke", [["transform-box", "stroke-box"]]), e("transform-view", [["transform-box", "view-box"]]), e("backface-visible", [["backface-visibility", "visible"]]), e("backface-hidden", [["backface-visibility", "hidden"]]);
  for (let o of ["auto", "default", "pointer", "wait", "text", "move", "help", "not-allowed", "none", "context-menu", "progress", "cell", "crosshair", "vertical-text", "alias", "copy", "no-drop", "grab", "grabbing", "all-scroll", "col-resize", "row-resize", "n-resize", "e-resize", "s-resize", "w-resize", "ne-resize", "nw-resize", "se-resize", "sw-resize", "ew-resize", "ns-resize", "nesw-resize", "nwse-resize", "zoom-in", "zoom-out"])
    e(`cursor-${o}`, [["cursor", o]]);
  n("cursor", { themeKeys: ["--cursor"], handle: (o) => [l3("cursor", o)] });
  for (let o of ["auto", "none", "manipulation"])
    e(`touch-${o}`, [["touch-action", o]]);
  let m = () => I2([$2("--tw-pan-x"), $2("--tw-pan-y"), $2("--tw-pinch-zoom")]);
  for (let o of ["x", "left", "right"])
    e(`touch-pan-${o}`, [m, ["--tw-pan-x", `pan-${o}`], ["touch-action", "var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);
  for (let o of ["y", "up", "down"])
    e(`touch-pan-${o}`, [m, ["--tw-pan-y", `pan-${o}`], ["touch-action", "var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);
  e("touch-pinch-zoom", [m, ["--tw-pinch-zoom", "pinch-zoom"], ["touch-action", "var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);
  for (let o of ["none", "text", "all", "auto"])
    e(`select-${o}`, [["-webkit-user-select", o], ["user-select", o]]);
  e("resize-none", [["resize", "none"]]), e("resize-x", [["resize", "horizontal"]]), e("resize-y", [["resize", "vertical"]]), e("resize", [["resize", "both"]]), e("snap-none", [["scroll-snap-type", "none"]]);
  let w2 = () => I2([$2("--tw-scroll-snap-strictness", "proximity", "*")]);
  for (let o of ["x", "y", "both"])
    e(`snap-${o}`, [w2, ["scroll-snap-type", `${o} var(--tw-scroll-snap-strictness)`]]);
  e("snap-mandatory", [w2, ["--tw-scroll-snap-strictness", "mandatory"]]), e("snap-proximity", [w2, ["--tw-scroll-snap-strictness", "proximity"]]), e("snap-align-none", [["scroll-snap-align", "none"]]), e("snap-start", [["scroll-snap-align", "start"]]), e("snap-end", [["scroll-snap-align", "end"]]), e("snap-center", [["scroll-snap-align", "center"]]), e("snap-normal", [["scroll-snap-stop", "normal"]]), e("snap-always", [["scroll-snap-stop", "always"]]);
  for (let [o, d2] of [["scroll-m", "scroll-margin"], ["scroll-mx", "scroll-margin-inline"], ["scroll-my", "scroll-margin-block"], ["scroll-ms", "scroll-margin-inline-start"], ["scroll-me", "scroll-margin-inline-end"], ["scroll-mt", "scroll-margin-top"], ["scroll-mr", "scroll-margin-right"], ["scroll-mb", "scroll-margin-bottom"], ["scroll-ml", "scroll-margin-left"]])
    a(o, ["--scroll-margin", "--spacing"], (h2) => [l3(d2, h2)], { supportsNegative: true });
  for (let [o, d2] of [["scroll-p", "scroll-padding"], ["scroll-px", "scroll-padding-inline"], ["scroll-py", "scroll-padding-block"], ["scroll-ps", "scroll-padding-inline-start"], ["scroll-pe", "scroll-padding-inline-end"], ["scroll-pt", "scroll-padding-top"], ["scroll-pr", "scroll-padding-right"], ["scroll-pb", "scroll-padding-bottom"], ["scroll-pl", "scroll-padding-left"]])
    a(o, ["--scroll-padding", "--spacing"], (h2) => [l3(d2, h2)]);
  e("list-inside", [["list-style-position", "inside"]]), e("list-outside", [["list-style-position", "outside"]]), e("list-none", [["list-style-type", "none"]]), e("list-disc", [["list-style-type", "disc"]]), e("list-decimal", [["list-style-type", "decimal"]]), n("list", { themeKeys: ["--list-style-type"], handle: (o) => [l3("list-style-type", o)] }), e("list-image-none", [["list-style-image", "none"]]), n("list-image", { themeKeys: ["--list-style-image"], handle: (o) => [l3("list-style-image", o)] }), e("appearance-none", [["appearance", "none"]]), e("appearance-auto", [["appearance", "auto"]]), e("scheme-normal", [["color-scheme", "normal"]]), e("scheme-dark", [["color-scheme", "dark"]]), e("scheme-light", [["color-scheme", "light"]]), e("scheme-light-dark", [["color-scheme", "light dark"]]), e("scheme-only-dark", [["color-scheme", "only dark"]]), e("scheme-only-light", [["color-scheme", "only light"]]), e("columns-auto", [["columns", "auto"]]), n("columns", { themeKeys: ["--columns", "--container"], handleBareValue: ({ value: o }) => p(o) ? o : null, handle: (o) => [l3("columns", o)] }), i("columns", () => [{ values: Array.from({ length: 12 }, (o, d2) => `${d2 + 1}`), valueThemeKeys: ["--columns", "--container"] }]);
  for (let o of ["auto", "avoid", "all", "avoid-page", "page", "left", "right", "column"])
    e(`break-before-${o}`, [["break-before", o]]);
  for (let o of ["auto", "avoid", "avoid-page", "avoid-column"])
    e(`break-inside-${o}`, [["break-inside", o]]);
  for (let o of ["auto", "avoid", "all", "avoid-page", "page", "left", "right", "column"])
    e(`break-after-${o}`, [["break-after", o]]);
  e("grid-flow-row", [["grid-auto-flow", "row"]]), e("grid-flow-col", [["grid-auto-flow", "column"]]), e("grid-flow-dense", [["grid-auto-flow", "dense"]]), e("grid-flow-row-dense", [["grid-auto-flow", "row dense"]]), e("grid-flow-col-dense", [["grid-auto-flow", "column dense"]]), e("auto-cols-auto", [["grid-auto-columns", "auto"]]), e("auto-cols-min", [["grid-auto-columns", "min-content"]]), e("auto-cols-max", [["grid-auto-columns", "max-content"]]), e("auto-cols-fr", [["grid-auto-columns", "minmax(0, 1fr)"]]), n("auto-cols", { themeKeys: ["--grid-auto-columns"], handle: (o) => [l3("grid-auto-columns", o)] }), e("auto-rows-auto", [["grid-auto-rows", "auto"]]), e("auto-rows-min", [["grid-auto-rows", "min-content"]]), e("auto-rows-max", [["grid-auto-rows", "max-content"]]), e("auto-rows-fr", [["grid-auto-rows", "minmax(0, 1fr)"]]), n("auto-rows", { themeKeys: ["--grid-auto-rows"], handle: (o) => [l3("grid-auto-rows", o)] }), e("grid-cols-none", [["grid-template-columns", "none"]]), e("grid-cols-subgrid", [["grid-template-columns", "subgrid"]]), n("grid-cols", { themeKeys: ["--grid-template-columns"], handleBareValue: ({ value: o }) => ge(o) ? `repeat(${o}, minmax(0, 1fr))` : null, handle: (o) => [l3("grid-template-columns", o)] }), e("grid-rows-none", [["grid-template-rows", "none"]]), e("grid-rows-subgrid", [["grid-template-rows", "subgrid"]]), n("grid-rows", { themeKeys: ["--grid-template-rows"], handleBareValue: ({ value: o }) => ge(o) ? `repeat(${o}, minmax(0, 1fr))` : null, handle: (o) => [l3("grid-template-rows", o)] }), i("grid-cols", () => [{ values: Array.from({ length: 12 }, (o, d2) => `${d2 + 1}`), valueThemeKeys: ["--grid-template-columns"] }]), i("grid-rows", () => [{ values: Array.from({ length: 12 }, (o, d2) => `${d2 + 1}`), valueThemeKeys: ["--grid-template-rows"] }]), e("flex-row", [["flex-direction", "row"]]), e("flex-row-reverse", [["flex-direction", "row-reverse"]]), e("flex-col", [["flex-direction", "column"]]), e("flex-col-reverse", [["flex-direction", "column-reverse"]]), e("flex-wrap", [["flex-wrap", "wrap"]]), e("flex-nowrap", [["flex-wrap", "nowrap"]]), e("flex-wrap-reverse", [["flex-wrap", "wrap-reverse"]]), e("place-content-center", [["place-content", "center"]]), e("place-content-start", [["place-content", "start"]]), e("place-content-end", [["place-content", "end"]]), e("place-content-center-safe", [["place-content", "safe center"]]), e("place-content-end-safe", [["place-content", "safe end"]]), e("place-content-between", [["place-content", "space-between"]]), e("place-content-around", [["place-content", "space-around"]]), e("place-content-evenly", [["place-content", "space-evenly"]]), e("place-content-baseline", [["place-content", "baseline"]]), e("place-content-stretch", [["place-content", "stretch"]]), e("place-items-center", [["place-items", "center"]]), e("place-items-start", [["place-items", "start"]]), e("place-items-end", [["place-items", "end"]]), e("place-items-center-safe", [["place-items", "safe center"]]), e("place-items-end-safe", [["place-items", "safe end"]]), e("place-items-baseline", [["place-items", "baseline"]]), e("place-items-stretch", [["place-items", "stretch"]]), e("content-normal", [["align-content", "normal"]]), e("content-center", [["align-content", "center"]]), e("content-start", [["align-content", "flex-start"]]), e("content-end", [["align-content", "flex-end"]]), e("content-center-safe", [["align-content", "safe center"]]), e("content-end-safe", [["align-content", "safe flex-end"]]), e("content-between", [["align-content", "space-between"]]), e("content-around", [["align-content", "space-around"]]), e("content-evenly", [["align-content", "space-evenly"]]), e("content-baseline", [["align-content", "baseline"]]), e("content-stretch", [["align-content", "stretch"]]), e("items-center", [["align-items", "center"]]), e("items-start", [["align-items", "flex-start"]]), e("items-end", [["align-items", "flex-end"]]), e("items-center-safe", [["align-items", "safe center"]]), e("items-end-safe", [["align-items", "safe flex-end"]]), e("items-baseline", [["align-items", "baseline"]]), e("items-baseline-last", [["align-items", "last baseline"]]), e("items-stretch", [["align-items", "stretch"]]), e("justify-normal", [["justify-content", "normal"]]), e("justify-center", [["justify-content", "center"]]), e("justify-start", [["justify-content", "flex-start"]]), e("justify-end", [["justify-content", "flex-end"]]), e("justify-center-safe", [["justify-content", "safe center"]]), e("justify-end-safe", [["justify-content", "safe flex-end"]]), e("justify-between", [["justify-content", "space-between"]]), e("justify-around", [["justify-content", "space-around"]]), e("justify-evenly", [["justify-content", "space-evenly"]]), e("justify-baseline", [["justify-content", "baseline"]]), e("justify-stretch", [["justify-content", "stretch"]]), e("justify-items-normal", [["justify-items", "normal"]]), e("justify-items-center", [["justify-items", "center"]]), e("justify-items-start", [["justify-items", "start"]]), e("justify-items-end", [["justify-items", "end"]]), e("justify-items-center-safe", [["justify-items", "safe center"]]), e("justify-items-end-safe", [["justify-items", "safe end"]]), e("justify-items-stretch", [["justify-items", "stretch"]]), a("gap", ["--gap", "--spacing"], (o) => [l3("gap", o)]), a("gap-x", ["--gap", "--spacing"], (o) => [l3("column-gap", o)]), a("gap-y", ["--gap", "--spacing"], (o) => [l3("row-gap", o)]), a("space-x", ["--space", "--spacing"], (o) => [I2([$2("--tw-space-x-reverse", "0")]), M2(":where(& > :not(:last-child))", [l3("--tw-sort", "row-gap"), l3("--tw-space-x-reverse", "0"), l3("margin-inline-start", `calc(${o} * var(--tw-space-x-reverse))`), l3("margin-inline-end", `calc(${o} * calc(1 - var(--tw-space-x-reverse)))`)])], { supportsNegative: true }), a("space-y", ["--space", "--spacing"], (o) => [I2([$2("--tw-space-y-reverse", "0")]), M2(":where(& > :not(:last-child))", [l3("--tw-sort", "column-gap"), l3("--tw-space-y-reverse", "0"), l3("margin-block-start", `calc(${o} * var(--tw-space-y-reverse))`), l3("margin-block-end", `calc(${o} * calc(1 - var(--tw-space-y-reverse)))`)])], { supportsNegative: true }), e("space-x-reverse", [() => I2([$2("--tw-space-x-reverse", "0")]), () => M2(":where(& > :not(:last-child))", [l3("--tw-sort", "row-gap"), l3("--tw-space-x-reverse", "1")])]), e("space-y-reverse", [() => I2([$2("--tw-space-y-reverse", "0")]), () => M2(":where(& > :not(:last-child))", [l3("--tw-sort", "column-gap"), l3("--tw-space-y-reverse", "1")])]), e("accent-auto", [["accent-color", "auto"]]), s2("accent", { themeKeys: ["--accent-color", "--color"], handle: (o) => [l3("accent-color", o)] }), s2("caret", { themeKeys: ["--caret-color", "--color"], handle: (o) => [l3("caret-color", o)] }), s2("divide", { themeKeys: ["--divide-color", "--color"], handle: (o) => [M2(":where(& > :not(:last-child))", [l3("--tw-sort", "divide-color"), l3("border-color", o)])] }), e("place-self-auto", [["place-self", "auto"]]), e("place-self-start", [["place-self", "start"]]), e("place-self-end", [["place-self", "end"]]), e("place-self-center", [["place-self", "center"]]), e("place-self-end-safe", [["place-self", "safe end"]]), e("place-self-center-safe", [["place-self", "safe center"]]), e("place-self-stretch", [["place-self", "stretch"]]), e("self-auto", [["align-self", "auto"]]), e("self-start", [["align-self", "flex-start"]]), e("self-end", [["align-self", "flex-end"]]), e("self-center", [["align-self", "center"]]), e("self-end-safe", [["align-self", "safe flex-end"]]), e("self-center-safe", [["align-self", "safe center"]]), e("self-stretch", [["align-self", "stretch"]]), e("self-baseline", [["align-self", "baseline"]]), e("self-baseline-last", [["align-self", "last baseline"]]), e("justify-self-auto", [["justify-self", "auto"]]), e("justify-self-start", [["justify-self", "flex-start"]]), e("justify-self-end", [["justify-self", "flex-end"]]), e("justify-self-center", [["justify-self", "center"]]), e("justify-self-end-safe", [["justify-self", "safe flex-end"]]), e("justify-self-center-safe", [["justify-self", "safe center"]]), e("justify-self-stretch", [["justify-self", "stretch"]]);
  for (let o of ["auto", "hidden", "clip", "visible", "scroll"])
    e(`overflow-${o}`, [["overflow", o]]), e(`overflow-x-${o}`, [["overflow-x", o]]), e(`overflow-y-${o}`, [["overflow-y", o]]);
  for (let o of ["auto", "contain", "none"])
    e(`overscroll-${o}`, [["overscroll-behavior", o]]), e(`overscroll-x-${o}`, [["overscroll-behavior-x", o]]), e(`overscroll-y-${o}`, [["overscroll-behavior-y", o]]);
  e("scroll-auto", [["scroll-behavior", "auto"]]), e("scroll-smooth", [["scroll-behavior", "smooth"]]), e("truncate", [["overflow", "hidden"], ["text-overflow", "ellipsis"], ["white-space", "nowrap"]]), e("text-ellipsis", [["text-overflow", "ellipsis"]]), e("text-clip", [["text-overflow", "clip"]]), e("hyphens-none", [["-webkit-hyphens", "none"], ["hyphens", "none"]]), e("hyphens-manual", [["-webkit-hyphens", "manual"], ["hyphens", "manual"]]), e("hyphens-auto", [["-webkit-hyphens", "auto"], ["hyphens", "auto"]]), e("whitespace-normal", [["white-space", "normal"]]), e("whitespace-nowrap", [["white-space", "nowrap"]]), e("whitespace-pre", [["white-space", "pre"]]), e("whitespace-pre-line", [["white-space", "pre-line"]]), e("whitespace-pre-wrap", [["white-space", "pre-wrap"]]), e("whitespace-break-spaces", [["white-space", "break-spaces"]]), e("text-wrap", [["text-wrap", "wrap"]]), e("text-nowrap", [["text-wrap", "nowrap"]]), e("text-balance", [["text-wrap", "balance"]]), e("text-pretty", [["text-wrap", "pretty"]]), e("break-normal", [["overflow-wrap", "normal"], ["word-break", "normal"]]), e("break-words", [["overflow-wrap", "break-word"]]), e("break-all", [["word-break", "break-all"]]), e("break-keep", [["word-break", "keep-all"]]), e("wrap-anywhere", [["overflow-wrap", "anywhere"]]), e("wrap-break-word", [["overflow-wrap", "break-word"]]), e("wrap-normal", [["overflow-wrap", "normal"]]);
  for (let [o, d2] of [["rounded", ["border-radius"]], ["rounded-s", ["border-start-start-radius", "border-end-start-radius"]], ["rounded-e", ["border-start-end-radius", "border-end-end-radius"]], ["rounded-t", ["border-top-left-radius", "border-top-right-radius"]], ["rounded-r", ["border-top-right-radius", "border-bottom-right-radius"]], ["rounded-b", ["border-bottom-right-radius", "border-bottom-left-radius"]], ["rounded-l", ["border-top-left-radius", "border-bottom-left-radius"]], ["rounded-ss", ["border-start-start-radius"]], ["rounded-se", ["border-start-end-radius"]], ["rounded-ee", ["border-end-end-radius"]], ["rounded-es", ["border-end-start-radius"]], ["rounded-tl", ["border-top-left-radius"]], ["rounded-tr", ["border-top-right-radius"]], ["rounded-br", ["border-bottom-right-radius"]], ["rounded-bl", ["border-bottom-left-radius"]]])
    e(`${o}-none`, d2.map((h2) => [h2, "0"])), e(`${o}-full`, d2.map((h2) => [h2, "calc(infinity * 1px)"])), n(o, { themeKeys: ["--radius"], handle: (h2) => d2.map((A2) => l3(A2, h2)) });
  e("border-solid", [["--tw-border-style", "solid"], ["border-style", "solid"]]), e("border-dashed", [["--tw-border-style", "dashed"], ["border-style", "dashed"]]), e("border-dotted", [["--tw-border-style", "dotted"], ["border-style", "dotted"]]), e("border-double", [["--tw-border-style", "double"], ["border-style", "double"]]), e("border-hidden", [["--tw-border-style", "hidden"], ["border-style", "hidden"]]), e("border-none", [["--tw-border-style", "none"], ["border-style", "none"]]);
  {
    let d2 = function(h2, A2) {
      t.functional(h2, (y2) => {
        if (!y2.value) {
          if (y2.modifier)
            return;
          let C2 = r.get(["--default-border-width"]) ?? "1px", R2 = A2.width(C2);
          return R2 ? [o(), ...R2] : void 0;
        }
        if (y2.value.kind === "arbitrary") {
          let C2 = y2.value.value;
          switch (y2.value.dataType ?? pe(C2, ["color", "line-width", "length"])) {
            case "line-width":
            case "length": {
              if (y2.modifier)
                return;
              let V2 = A2.width(C2);
              return V2 ? [o(), ...V2] : void 0;
            }
            default:
              return C2 = Q2(C2, y2.modifier, r), C2 === null ? void 0 : A2.color(C2);
          }
        }
        {
          let C2 = te2(y2, r, ["--border-color", "--color"]);
          if (C2)
            return A2.color(C2);
        }
        {
          if (y2.modifier)
            return;
          let C2 = r.resolve(y2.value.value, ["--border-width"]);
          if (C2) {
            let R2 = A2.width(C2);
            return R2 ? [o(), ...R2] : void 0;
          }
          if (p(y2.value.value)) {
            let R2 = A2.width(`${y2.value.value}px`);
            return R2 ? [o(), ...R2] : void 0;
          }
        }
      }), i(h2, () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--border-color", "--color"], modifiers: Array.from({ length: 21 }, (y2, C2) => `${C2 * 5}`), hasDefaultValue: true }, { values: ["0", "2", "4", "8"], valueThemeKeys: ["--border-width"] }]);
    };
    var O2 = d2;
    let o = () => I2([$2("--tw-border-style", "solid")]);
    d2("border", { width: (h2) => [l3("border-style", "var(--tw-border-style)"), l3("border-width", h2)], color: (h2) => [l3("border-color", h2)] }), d2("border-x", { width: (h2) => [l3("border-inline-style", "var(--tw-border-style)"), l3("border-inline-width", h2)], color: (h2) => [l3("border-inline-color", h2)] }), d2("border-y", { width: (h2) => [l3("border-block-style", "var(--tw-border-style)"), l3("border-block-width", h2)], color: (h2) => [l3("border-block-color", h2)] }), d2("border-s", { width: (h2) => [l3("border-inline-start-style", "var(--tw-border-style)"), l3("border-inline-start-width", h2)], color: (h2) => [l3("border-inline-start-color", h2)] }), d2("border-e", { width: (h2) => [l3("border-inline-end-style", "var(--tw-border-style)"), l3("border-inline-end-width", h2)], color: (h2) => [l3("border-inline-end-color", h2)] }), d2("border-t", { width: (h2) => [l3("border-top-style", "var(--tw-border-style)"), l3("border-top-width", h2)], color: (h2) => [l3("border-top-color", h2)] }), d2("border-r", { width: (h2) => [l3("border-right-style", "var(--tw-border-style)"), l3("border-right-width", h2)], color: (h2) => [l3("border-right-color", h2)] }), d2("border-b", { width: (h2) => [l3("border-bottom-style", "var(--tw-border-style)"), l3("border-bottom-width", h2)], color: (h2) => [l3("border-bottom-color", h2)] }), d2("border-l", { width: (h2) => [l3("border-left-style", "var(--tw-border-style)"), l3("border-left-width", h2)], color: (h2) => [l3("border-left-color", h2)] }), n("divide-x", { defaultValue: r.get(["--default-border-width"]) ?? "1px", themeKeys: ["--divide-width", "--border-width"], handleBareValue: ({ value: h2 }) => p(h2) ? `${h2}px` : null, handle: (h2) => [I2([$2("--tw-divide-x-reverse", "0")]), M2(":where(& > :not(:last-child))", [l3("--tw-sort", "divide-x-width"), o(), l3("--tw-divide-x-reverse", "0"), l3("border-inline-style", "var(--tw-border-style)"), l3("border-inline-start-width", `calc(${h2} * var(--tw-divide-x-reverse))`), l3("border-inline-end-width", `calc(${h2} * calc(1 - var(--tw-divide-x-reverse)))`)])] }), n("divide-y", { defaultValue: r.get(["--default-border-width"]) ?? "1px", themeKeys: ["--divide-width", "--border-width"], handleBareValue: ({ value: h2 }) => p(h2) ? `${h2}px` : null, handle: (h2) => [I2([$2("--tw-divide-y-reverse", "0")]), M2(":where(& > :not(:last-child))", [l3("--tw-sort", "divide-y-width"), o(), l3("--tw-divide-y-reverse", "0"), l3("border-bottom-style", "var(--tw-border-style)"), l3("border-top-style", "var(--tw-border-style)"), l3("border-top-width", `calc(${h2} * var(--tw-divide-y-reverse))`), l3("border-bottom-width", `calc(${h2} * calc(1 - var(--tw-divide-y-reverse)))`)])] }), i("divide-x", () => [{ values: ["0", "2", "4", "8"], valueThemeKeys: ["--divide-width", "--border-width"], hasDefaultValue: true }]), i("divide-y", () => [{ values: ["0", "2", "4", "8"], valueThemeKeys: ["--divide-width", "--border-width"], hasDefaultValue: true }]), e("divide-x-reverse", [() => I2([$2("--tw-divide-x-reverse", "0")]), () => M2(":where(& > :not(:last-child))", [l3("--tw-divide-x-reverse", "1")])]), e("divide-y-reverse", [() => I2([$2("--tw-divide-y-reverse", "0")]), () => M2(":where(& > :not(:last-child))", [l3("--tw-divide-y-reverse", "1")])]);
    for (let h2 of ["solid", "dashed", "dotted", "double", "none"])
      e(`divide-${h2}`, [() => M2(":where(& > :not(:last-child))", [l3("--tw-sort", "divide-style"), l3("--tw-border-style", h2), l3("border-style", h2)])]);
  }
  e("bg-auto", [["background-size", "auto"]]), e("bg-cover", [["background-size", "cover"]]), e("bg-contain", [["background-size", "contain"]]), n("bg-size", { handle(o) {
    if (o)
      return [l3("background-size", o)];
  } }), e("bg-fixed", [["background-attachment", "fixed"]]), e("bg-local", [["background-attachment", "local"]]), e("bg-scroll", [["background-attachment", "scroll"]]), e("bg-top", [["background-position", "top"]]), e("bg-top-left", [["background-position", "left top"]]), e("bg-top-right", [["background-position", "right top"]]), e("bg-bottom", [["background-position", "bottom"]]), e("bg-bottom-left", [["background-position", "left bottom"]]), e("bg-bottom-right", [["background-position", "right bottom"]]), e("bg-left", [["background-position", "left"]]), e("bg-right", [["background-position", "right"]]), e("bg-center", [["background-position", "center"]]), n("bg-position", { handle(o) {
    if (o)
      return [l3("background-position", o)];
  } }), e("bg-repeat", [["background-repeat", "repeat"]]), e("bg-no-repeat", [["background-repeat", "no-repeat"]]), e("bg-repeat-x", [["background-repeat", "repeat-x"]]), e("bg-repeat-y", [["background-repeat", "repeat-y"]]), e("bg-repeat-round", [["background-repeat", "round"]]), e("bg-repeat-space", [["background-repeat", "space"]]), e("bg-none", [["background-image", "none"]]);
  {
    let h2 = function(C2) {
      let R2 = "in oklab";
      if ((C2 == null ? void 0 : C2.kind) === "named")
        switch (C2.value) {
          case "longer":
          case "shorter":
          case "increasing":
          case "decreasing":
            R2 = `in oklch ${C2.value} hue`;
            break;
          default:
            R2 = `in ${C2.value}`;
        }
      else
        (C2 == null ? void 0 : C2.kind) === "arbitrary" && (R2 = C2.value);
      return R2;
    }, A2 = function({ negative: C2 }) {
      return (R2) => {
        if (!R2.value)
          return;
        if (R2.value.kind === "arbitrary") {
          if (R2.modifier)
            return;
          let _2 = R2.value.value;
          switch (R2.value.dataType ?? pe(_2, ["angle"])) {
            case "angle":
              return _2 = C2 ? `calc(${_2} * -1)` : `${_2}`, [l3("--tw-gradient-position", _2), l3("background-image", `linear-gradient(var(--tw-gradient-stops,${_2}))`)];
            default:
              return C2 ? void 0 : [l3("--tw-gradient-position", _2), l3("background-image", `linear-gradient(var(--tw-gradient-stops,${_2}))`)];
          }
        }
        let V2 = R2.value.value;
        if (!C2 && d2.has(V2))
          V2 = d2.get(V2);
        else if (p(V2))
          V2 = C2 ? `calc(${V2}deg * -1)` : `${V2}deg`;
        else
          return;
        let T2 = h2(R2.modifier);
        return [l3("--tw-gradient-position", `${V2}`), H2("@supports (background-image: linear-gradient(in lab, red, red))", [l3("--tw-gradient-position", `${V2} ${T2}`)]), l3("background-image", "linear-gradient(var(--tw-gradient-stops))")];
      };
    }, y2 = function({ negative: C2 }) {
      return (R2) => {
        var _a2;
        if (((_a2 = R2.value) == null ? void 0 : _a2.kind) === "arbitrary") {
          if (R2.modifier)
            return;
          let _2 = R2.value.value;
          return [l3("--tw-gradient-position", _2), l3("background-image", `conic-gradient(var(--tw-gradient-stops,${_2}))`)];
        }
        let V2 = h2(R2.modifier);
        if (!R2.value)
          return [l3("--tw-gradient-position", V2), l3("background-image", "conic-gradient(var(--tw-gradient-stops))")];
        let T2 = R2.value.value;
        if (p(T2))
          return T2 = C2 ? `calc(${T2}deg * -1)` : `${T2}deg`, [l3("--tw-gradient-position", `from ${T2} ${V2}`), l3("background-image", "conic-gradient(var(--tw-gradient-stops))")];
      };
    };
    var G2 = h2, L2 = A2, q2 = y2;
    let o = ["oklab", "oklch", "srgb", "hsl", "longer", "shorter", "increasing", "decreasing"], d2 = /* @__PURE__ */ new Map([["to-t", "to top"], ["to-tr", "to top right"], ["to-r", "to right"], ["to-br", "to bottom right"], ["to-b", "to bottom"], ["to-bl", "to bottom left"], ["to-l", "to left"], ["to-tl", "to top left"]]);
    t.functional("-bg-linear", A2({ negative: true })), t.functional("bg-linear", A2({ negative: false })), i("bg-linear", () => [{ values: [...d2.keys()], modifiers: o }, { values: ["0", "30", "60", "90", "120", "150", "180", "210", "240", "270", "300", "330"], supportsNegative: true, modifiers: o }]), t.functional("-bg-conic", y2({ negative: true })), t.functional("bg-conic", y2({ negative: false })), i("bg-conic", () => [{ hasDefaultValue: true, modifiers: o }, { values: ["0", "30", "60", "90", "120", "150", "180", "210", "240", "270", "300", "330"], supportsNegative: true, modifiers: o }]), t.functional("bg-radial", (C2) => {
      if (!C2.value) {
        let R2 = h2(C2.modifier);
        return [l3("--tw-gradient-position", R2), l3("background-image", "radial-gradient(var(--tw-gradient-stops))")];
      }
      if (C2.value.kind === "arbitrary") {
        if (C2.modifier)
          return;
        let R2 = C2.value.value;
        return [l3("--tw-gradient-position", R2), l3("background-image", `radial-gradient(var(--tw-gradient-stops,${R2}))`)];
      }
    }), i("bg-radial", () => [{ hasDefaultValue: true, modifiers: o }]);
  }
  t.functional("bg", (o) => {
    if (o.value) {
      if (o.value.kind === "arbitrary") {
        let d2 = o.value.value;
        switch (o.value.dataType ?? pe(d2, ["image", "color", "percentage", "position", "bg-size", "length", "url"])) {
          case "percentage":
          case "position":
            return o.modifier ? void 0 : [l3("background-position", d2)];
          case "bg-size":
          case "length":
          case "size":
            return o.modifier ? void 0 : [l3("background-size", d2)];
          case "image":
          case "url":
            return o.modifier ? void 0 : [l3("background-image", d2)];
          default:
            return d2 = Q2(d2, o.modifier, r), d2 === null ? void 0 : [l3("background-color", d2)];
        }
      }
      {
        let d2 = te2(o, r, ["--background-color", "--color"]);
        if (d2)
          return [l3("background-color", d2)];
      }
      {
        if (o.modifier)
          return;
        let d2 = r.resolve(o.value.value, ["--background-image"]);
        if (d2)
          return [l3("background-image", d2)];
      }
    }
  }), i("bg", () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--background-color", "--color"], modifiers: Array.from({ length: 21 }, (o, d2) => `${d2 * 5}`) }, { values: [], valueThemeKeys: ["--background-image"] }]);
  let v2 = () => I2([$2("--tw-gradient-position"), $2("--tw-gradient-from", "#0000", "<color>"), $2("--tw-gradient-via", "#0000", "<color>"), $2("--tw-gradient-to", "#0000", "<color>"), $2("--tw-gradient-stops"), $2("--tw-gradient-via-stops"), $2("--tw-gradient-from-position", "0%", "<length-percentage>"), $2("--tw-gradient-via-position", "50%", "<length-percentage>"), $2("--tw-gradient-to-position", "100%", "<length-percentage>")]);
  function x(o, d2) {
    t.functional(o, (h2) => {
      if (h2.value) {
        if (h2.value.kind === "arbitrary") {
          let A2 = h2.value.value;
          switch (h2.value.dataType ?? pe(A2, ["color", "length", "percentage"])) {
            case "length":
            case "percentage":
              return h2.modifier ? void 0 : d2.position(A2);
            default:
              return A2 = Q2(A2, h2.modifier, r), A2 === null ? void 0 : d2.color(A2);
          }
        }
        {
          let A2 = te2(h2, r, ["--background-color", "--color"]);
          if (A2)
            return d2.color(A2);
        }
        {
          if (h2.modifier)
            return;
          let A2 = r.resolve(h2.value.value, ["--gradient-color-stop-positions"]);
          if (A2)
            return d2.position(A2);
          if (h2.value.value[h2.value.value.length - 1] === "%" && p(h2.value.value.slice(0, -1)))
            return d2.position(h2.value.value);
        }
      }
    }), i(o, () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--background-color", "--color"], modifiers: Array.from({ length: 21 }, (h2, A2) => `${A2 * 5}`) }, { values: Array.from({ length: 21 }, (h2, A2) => `${A2 * 5}%`), valueThemeKeys: ["--gradient-color-stop-positions"] }]);
  }
  x("from", { color: (o) => [v2(), l3("--tw-sort", "--tw-gradient-from"), l3("--tw-gradient-from", o), l3("--tw-gradient-stops", "var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")], position: (o) => [v2(), l3("--tw-gradient-from-position", o)] }), e("via-none", [["--tw-gradient-via-stops", "initial"]]), x("via", { color: (o) => [v2(), l3("--tw-sort", "--tw-gradient-via"), l3("--tw-gradient-via", o), l3("--tw-gradient-via-stops", "var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"), l3("--tw-gradient-stops", "var(--tw-gradient-via-stops)")], position: (o) => [v2(), l3("--tw-gradient-via-position", o)] }), x("to", { color: (o) => [v2(), l3("--tw-sort", "--tw-gradient-to"), l3("--tw-gradient-to", o), l3("--tw-gradient-stops", "var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")], position: (o) => [v2(), l3("--tw-gradient-to-position", o)] }), e("mask-none", [["mask-image", "none"]]), t.functional("mask", (o) => {
    if (!o.value || o.modifier || o.value.kind !== "arbitrary")
      return;
    let d2 = o.value.value;
    switch (o.value.dataType ?? pe(d2, ["image", "percentage", "position", "bg-size", "length", "url"])) {
      case "percentage":
      case "position":
        return o.modifier ? void 0 : [l3("mask-position", d2)];
      case "bg-size":
      case "length":
      case "size":
        return [l3("mask-size", d2)];
      case "image":
      case "url":
      default:
        return [l3("mask-image", d2)];
    }
  }), e("mask-add", [["mask-composite", "add"]]), e("mask-subtract", [["mask-composite", "subtract"]]), e("mask-intersect", [["mask-composite", "intersect"]]), e("mask-exclude", [["mask-composite", "exclude"]]), e("mask-alpha", [["mask-mode", "alpha"]]), e("mask-luminance", [["mask-mode", "luminance"]]), e("mask-match", [["mask-mode", "match-source"]]), e("mask-type-alpha", [["mask-type", "alpha"]]), e("mask-type-luminance", [["mask-type", "luminance"]]), e("mask-auto", [["mask-size", "auto"]]), e("mask-cover", [["mask-size", "cover"]]), e("mask-contain", [["mask-size", "contain"]]), n("mask-size", { handle(o) {
    if (o)
      return [l3("mask-size", o)];
  } }), e("mask-top", [["mask-position", "top"]]), e("mask-top-left", [["mask-position", "left top"]]), e("mask-top-right", [["mask-position", "right top"]]), e("mask-bottom", [["mask-position", "bottom"]]), e("mask-bottom-left", [["mask-position", "left bottom"]]), e("mask-bottom-right", [["mask-position", "right bottom"]]), e("mask-left", [["mask-position", "left"]]), e("mask-right", [["mask-position", "right"]]), e("mask-center", [["mask-position", "center"]]), n("mask-position", { handle(o) {
    if (o)
      return [l3("mask-position", o)];
  } }), e("mask-repeat", [["mask-repeat", "repeat"]]), e("mask-no-repeat", [["mask-repeat", "no-repeat"]]), e("mask-repeat-x", [["mask-repeat", "repeat-x"]]), e("mask-repeat-y", [["mask-repeat", "repeat-y"]]), e("mask-repeat-round", [["mask-repeat", "round"]]), e("mask-repeat-space", [["mask-repeat", "space"]]), e("mask-clip-border", [["mask-clip", "border-box"]]), e("mask-clip-padding", [["mask-clip", "padding-box"]]), e("mask-clip-content", [["mask-clip", "content-box"]]), e("mask-clip-fill", [["mask-clip", "fill-box"]]), e("mask-clip-stroke", [["mask-clip", "stroke-box"]]), e("mask-clip-view", [["mask-clip", "view-box"]]), e("mask-no-clip", [["mask-clip", "no-clip"]]), e("mask-origin-border", [["mask-origin", "border-box"]]), e("mask-origin-padding", [["mask-origin", "padding-box"]]), e("mask-origin-content", [["mask-origin", "content-box"]]), e("mask-origin-fill", [["mask-origin", "fill-box"]]), e("mask-origin-stroke", [["mask-origin", "stroke-box"]]), e("mask-origin-view", [["mask-origin", "view-box"]]);
  let k2 = () => I2([$2("--tw-mask-linear", "linear-gradient(#fff, #fff)"), $2("--tw-mask-radial", "linear-gradient(#fff, #fff)"), $2("--tw-mask-conic", "linear-gradient(#fff, #fff)")]);
  function N2(o, d2) {
    t.functional(o, (h2) => {
      if (h2.value) {
        if (h2.value.kind === "arbitrary") {
          let A2 = h2.value.value;
          switch (h2.value.dataType ?? pe(A2, ["length", "percentage", "color"])) {
            case "color":
              return A2 = Q2(A2, h2.modifier, r), A2 === null ? void 0 : d2.color(A2);
            case "percentage":
              return h2.modifier || !p(A2.slice(0, -1)) ? void 0 : d2.position(A2);
            default:
              return h2.modifier ? void 0 : d2.position(A2);
          }
        }
        {
          let A2 = te2(h2, r, ["--background-color", "--color"]);
          if (A2)
            return d2.color(A2);
        }
        {
          if (h2.modifier)
            return;
          let A2 = pe(h2.value.value, ["number", "percentage"]);
          if (!A2)
            return;
          switch (A2) {
            case "number": {
              let y2 = r.resolve(null, ["--spacing"]);
              return !y2 || !ue(h2.value.value) ? void 0 : d2.position(`calc(${y2} * ${h2.value.value})`);
            }
            case "percentage":
              return p(h2.value.value.slice(0, -1)) ? d2.position(h2.value.value) : void 0;
            default:
              return;
          }
        }
      }
    }), i(o, () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--background-color", "--color"], modifiers: Array.from({ length: 21 }, (h2, A2) => `${A2 * 5}`) }, { values: Array.from({ length: 21 }, (h2, A2) => `${A2 * 5}%`), valueThemeKeys: ["--gradient-color-stop-positions"] }]), i(o, () => [{ values: Array.from({ length: 21 }, (h2, A2) => `${A2 * 5}%`) }, { values: r.get(["--spacing"]) ? Ze : [] }, { values: ["current", "inherit", "transparent"], valueThemeKeys: ["--background-color", "--color"], modifiers: Array.from({ length: 21 }, (h2, A2) => `${A2 * 5}`) }]);
  }
  let b2 = () => I2([$2("--tw-mask-left", "linear-gradient(#fff, #fff)"), $2("--tw-mask-right", "linear-gradient(#fff, #fff)"), $2("--tw-mask-bottom", "linear-gradient(#fff, #fff)"), $2("--tw-mask-top", "linear-gradient(#fff, #fff)")]);
  function S(o, d2, h2) {
    N2(o, { color(A2) {
      let y2 = [k2(), b2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-linear", "var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];
      for (let C2 of ["top", "right", "bottom", "left"])
        h2[C2] && (y2.push(l3(`--tw-mask-${C2}`, `linear-gradient(to ${C2}, var(--tw-mask-${C2}-from-color) var(--tw-mask-${C2}-from-position), var(--tw-mask-${C2}-to-color) var(--tw-mask-${C2}-to-position))`)), y2.push(I2([$2(`--tw-mask-${C2}-from-position`, "0%"), $2(`--tw-mask-${C2}-to-position`, "100%"), $2(`--tw-mask-${C2}-from-color`, "black"), $2(`--tw-mask-${C2}-to-color`, "transparent")])), y2.push(l3(`--tw-mask-${C2}-${d2}-color`, A2)));
      return y2;
    }, position(A2) {
      let y2 = [k2(), b2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-linear", "var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];
      for (let C2 of ["top", "right", "bottom", "left"])
        h2[C2] && (y2.push(l3(`--tw-mask-${C2}`, `linear-gradient(to ${C2}, var(--tw-mask-${C2}-from-color) var(--tw-mask-${C2}-from-position), var(--tw-mask-${C2}-to-color) var(--tw-mask-${C2}-to-position))`)), y2.push(I2([$2(`--tw-mask-${C2}-from-position`, "0%"), $2(`--tw-mask-${C2}-to-position`, "100%"), $2(`--tw-mask-${C2}-from-color`, "black"), $2(`--tw-mask-${C2}-to-color`, "transparent")])), y2.push(l3(`--tw-mask-${C2}-${d2}-position`, A2)));
      return y2;
    } });
  }
  S("mask-x-from", "from", { top: false, right: true, bottom: false, left: true }), S("mask-x-to", "to", { top: false, right: true, bottom: false, left: true }), S("mask-y-from", "from", { top: true, right: false, bottom: true, left: false }), S("mask-y-to", "to", { top: true, right: false, bottom: true, left: false }), S("mask-t-from", "from", { top: true, right: false, bottom: false, left: false }), S("mask-t-to", "to", { top: true, right: false, bottom: false, left: false }), S("mask-r-from", "from", { top: false, right: true, bottom: false, left: false }), S("mask-r-to", "to", { top: false, right: true, bottom: false, left: false }), S("mask-b-from", "from", { top: false, right: false, bottom: true, left: false }), S("mask-b-to", "to", { top: false, right: false, bottom: true, left: false }), S("mask-l-from", "from", { top: false, right: false, bottom: false, left: true }), S("mask-l-to", "to", { top: false, right: false, bottom: false, left: true });
  let P2 = () => I2([$2("--tw-mask-linear-position", "0deg"), $2("--tw-mask-linear-from-position", "0%"), $2("--tw-mask-linear-to-position", "100%"), $2("--tw-mask-linear-from-color", "black"), $2("--tw-mask-linear-to-color", "transparent")]);
  n("mask-linear", { defaultValue: null, supportsNegative: true, supportsFractions: false, handleBareValue(o) {
    return p(o.value) ? `calc(1deg * ${o.value})` : null;
  }, handleNegativeBareValue(o) {
    return p(o.value) ? `calc(1deg * -${o.value})` : null;
  }, handle: (o) => [k2(), P2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-linear", "linear-gradient(var(--tw-mask-linear-stops, var(--tw-mask-linear-position)))"), l3("--tw-mask-linear-position", o)] }), i("mask-linear", () => [{ supportsNegative: true, values: ["0", "1", "2", "3", "6", "12", "45", "90", "180"] }]), N2("mask-linear-from", { color: (o) => [k2(), P2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-linear-stops", "var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"), l3("--tw-mask-linear", "linear-gradient(var(--tw-mask-linear-stops))"), l3("--tw-mask-linear-from-color", o)], position: (o) => [k2(), P2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-linear-stops", "var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"), l3("--tw-mask-linear", "linear-gradient(var(--tw-mask-linear-stops))"), l3("--tw-mask-linear-from-position", o)] }), N2("mask-linear-to", { color: (o) => [k2(), P2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-linear-stops", "var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"), l3("--tw-mask-linear", "linear-gradient(var(--tw-mask-linear-stops))"), l3("--tw-mask-linear-to-color", o)], position: (o) => [k2(), P2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-linear-stops", "var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"), l3("--tw-mask-linear", "linear-gradient(var(--tw-mask-linear-stops))"), l3("--tw-mask-linear-to-position", o)] });
  let j2 = () => I2([$2("--tw-mask-radial-from-position", "0%"), $2("--tw-mask-radial-to-position", "100%"), $2("--tw-mask-radial-from-color", "black"), $2("--tw-mask-radial-to-color", "transparent"), $2("--tw-mask-radial-shape", "ellipse"), $2("--tw-mask-radial-size", "farthest-corner"), $2("--tw-mask-radial-position", "center")]);
  e("mask-circle", [["--tw-mask-radial-shape", "circle"]]), e("mask-ellipse", [["--tw-mask-radial-shape", "ellipse"]]), e("mask-radial-closest-side", [["--tw-mask-radial-size", "closest-side"]]), e("mask-radial-farthest-side", [["--tw-mask-radial-size", "farthest-side"]]), e("mask-radial-closest-corner", [["--tw-mask-radial-size", "closest-corner"]]), e("mask-radial-farthest-corner", [["--tw-mask-radial-size", "farthest-corner"]]), e("mask-radial-at-top", [["--tw-mask-radial-position", "top"]]), e("mask-radial-at-top-left", [["--tw-mask-radial-position", "top left"]]), e("mask-radial-at-top-right", [["--tw-mask-radial-position", "top right"]]), e("mask-radial-at-bottom", [["--tw-mask-radial-position", "bottom"]]), e("mask-radial-at-bottom-left", [["--tw-mask-radial-position", "bottom left"]]), e("mask-radial-at-bottom-right", [["--tw-mask-radial-position", "bottom right"]]), e("mask-radial-at-left", [["--tw-mask-radial-position", "left"]]), e("mask-radial-at-right", [["--tw-mask-radial-position", "right"]]), e("mask-radial-at-center", [["--tw-mask-radial-position", "center"]]), n("mask-radial-at", { defaultValue: null, supportsNegative: false, supportsFractions: false, handle: (o) => [l3("--tw-mask-radial-position", o)] }), n("mask-radial", { defaultValue: null, supportsNegative: false, supportsFractions: false, handle: (o) => [k2(), j2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-radial", "radial-gradient(var(--tw-mask-radial-stops, var(--tw-mask-radial-size)))"), l3("--tw-mask-radial-size", o)] }), N2("mask-radial-from", { color: (o) => [k2(), j2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-radial-stops", "var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"), l3("--tw-mask-radial", "radial-gradient(var(--tw-mask-radial-stops))"), l3("--tw-mask-radial-from-color", o)], position: (o) => [k2(), j2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-radial-stops", "var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"), l3("--tw-mask-radial", "radial-gradient(var(--tw-mask-radial-stops))"), l3("--tw-mask-radial-from-position", o)] }), N2("mask-radial-to", { color: (o) => [k2(), j2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-radial-stops", "var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"), l3("--tw-mask-radial", "radial-gradient(var(--tw-mask-radial-stops))"), l3("--tw-mask-radial-to-color", o)], position: (o) => [k2(), j2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-radial-stops", "var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"), l3("--tw-mask-radial", "radial-gradient(var(--tw-mask-radial-stops))"), l3("--tw-mask-radial-to-position", o)] });
  let K2 = () => I2([$2("--tw-mask-conic-position", "0deg"), $2("--tw-mask-conic-from-position", "0%"), $2("--tw-mask-conic-to-position", "100%"), $2("--tw-mask-conic-from-color", "black"), $2("--tw-mask-conic-to-color", "transparent")]);
  n("mask-conic", { defaultValue: null, supportsNegative: true, supportsFractions: false, handleBareValue(o) {
    return p(o.value) ? `calc(1deg * ${o.value})` : null;
  }, handleNegativeBareValue(o) {
    return p(o.value) ? `calc(1deg * -${o.value})` : null;
  }, handle: (o) => [k2(), K2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-conic", "conic-gradient(var(--tw-mask-conic-stops, var(--tw-mask-conic-position)))"), l3("--tw-mask-conic-position", o)] }), i("mask-conic", () => [{ supportsNegative: true, values: ["0", "1", "2", "3", "6", "12", "45", "90", "180"] }]), N2("mask-conic-from", { color: (o) => [k2(), K2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-conic-stops", "from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"), l3("--tw-mask-conic", "conic-gradient(var(--tw-mask-conic-stops))"), l3("--tw-mask-conic-from-color", o)], position: (o) => [k2(), K2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-conic-stops", "from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"), l3("--tw-mask-conic", "conic-gradient(var(--tw-mask-conic-stops))"), l3("--tw-mask-conic-from-position", o)] }), N2("mask-conic-to", { color: (o) => [k2(), K2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-conic-stops", "from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"), l3("--tw-mask-conic", "conic-gradient(var(--tw-mask-conic-stops))"), l3("--tw-mask-conic-to-color", o)], position: (o) => [k2(), K2(), l3("mask-image", "var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"), l3("mask-composite", "intersect"), l3("--tw-mask-conic-stops", "from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"), l3("--tw-mask-conic", "conic-gradient(var(--tw-mask-conic-stops))"), l3("--tw-mask-conic-to-position", o)] }), e("box-decoration-slice", [["-webkit-box-decoration-break", "slice"], ["box-decoration-break", "slice"]]), e("box-decoration-clone", [["-webkit-box-decoration-break", "clone"], ["box-decoration-break", "clone"]]), e("bg-clip-text", [["background-clip", "text"]]), e("bg-clip-border", [["background-clip", "border-box"]]), e("bg-clip-padding", [["background-clip", "padding-box"]]), e("bg-clip-content", [["background-clip", "content-box"]]), e("bg-origin-border", [["background-origin", "border-box"]]), e("bg-origin-padding", [["background-origin", "padding-box"]]), e("bg-origin-content", [["background-origin", "content-box"]]);
  for (let o of ["normal", "multiply", "screen", "overlay", "darken", "lighten", "color-dodge", "color-burn", "hard-light", "soft-light", "difference", "exclusion", "hue", "saturation", "color", "luminosity"])
    e(`bg-blend-${o}`, [["background-blend-mode", o]]), e(`mix-blend-${o}`, [["mix-blend-mode", o]]);
  e("mix-blend-plus-darker", [["mix-blend-mode", "plus-darker"]]), e("mix-blend-plus-lighter", [["mix-blend-mode", "plus-lighter"]]), e("fill-none", [["fill", "none"]]), t.functional("fill", (o) => {
    if (!o.value)
      return;
    if (o.value.kind === "arbitrary") {
      let h2 = Q2(o.value.value, o.modifier, r);
      return h2 === null ? void 0 : [l3("fill", h2)];
    }
    let d2 = te2(o, r, ["--fill", "--color"]);
    if (d2)
      return [l3("fill", d2)];
  }), i("fill", () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--fill", "--color"], modifiers: Array.from({ length: 21 }, (o, d2) => `${d2 * 5}`) }]), e("stroke-none", [["stroke", "none"]]), t.functional("stroke", (o) => {
    if (o.value) {
      if (o.value.kind === "arbitrary") {
        let d2 = o.value.value;
        switch (o.value.dataType ?? pe(d2, ["color", "number", "length", "percentage"])) {
          case "number":
          case "length":
          case "percentage":
            return o.modifier ? void 0 : [l3("stroke-width", d2)];
          default:
            return d2 = Q2(o.value.value, o.modifier, r), d2 === null ? void 0 : [l3("stroke", d2)];
        }
      }
      {
        let d2 = te2(o, r, ["--stroke", "--color"]);
        if (d2)
          return [l3("stroke", d2)];
      }
      {
        let d2 = r.resolve(o.value.value, ["--stroke-width"]);
        if (d2)
          return [l3("stroke-width", d2)];
        if (p(o.value.value))
          return [l3("stroke-width", o.value.value)];
      }
    }
  }), i("stroke", () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--stroke", "--color"], modifiers: Array.from({ length: 21 }, (o, d2) => `${d2 * 5}`) }, { values: ["0", "1", "2", "3"], valueThemeKeys: ["--stroke-width"] }]), e("object-contain", [["object-fit", "contain"]]), e("object-cover", [["object-fit", "cover"]]), e("object-fill", [["object-fit", "fill"]]), e("object-none", [["object-fit", "none"]]), e("object-scale-down", [["object-fit", "scale-down"]]), e("object-top", [["object-position", "top"]]), e("object-top-left", [["object-position", "left top"]]), e("object-top-right", [["object-position", "right top"]]), e("object-bottom", [["object-position", "bottom"]]), e("object-bottom-left", [["object-position", "left bottom"]]), e("object-bottom-right", [["object-position", "right bottom"]]), e("object-left", [["object-position", "left"]]), e("object-right", [["object-position", "right"]]), e("object-center", [["object-position", "center"]]), n("object", { themeKeys: ["--object-position"], handle: (o) => [l3("object-position", o)] });
  for (let [o, d2] of [["p", "padding"], ["px", "padding-inline"], ["py", "padding-block"], ["ps", "padding-inline-start"], ["pe", "padding-inline-end"], ["pt", "padding-top"], ["pr", "padding-right"], ["pb", "padding-bottom"], ["pl", "padding-left"]])
    a(o, ["--padding", "--spacing"], (h2) => [l3(d2, h2)]);
  e("text-left", [["text-align", "left"]]), e("text-center", [["text-align", "center"]]), e("text-right", [["text-align", "right"]]), e("text-justify", [["text-align", "justify"]]), e("text-start", [["text-align", "start"]]), e("text-end", [["text-align", "end"]]), a("indent", ["--text-indent", "--spacing"], (o) => [l3("text-indent", o)], { supportsNegative: true }), e("align-baseline", [["vertical-align", "baseline"]]), e("align-top", [["vertical-align", "top"]]), e("align-middle", [["vertical-align", "middle"]]), e("align-bottom", [["vertical-align", "bottom"]]), e("align-text-top", [["vertical-align", "text-top"]]), e("align-text-bottom", [["vertical-align", "text-bottom"]]), e("align-sub", [["vertical-align", "sub"]]), e("align-super", [["vertical-align", "super"]]), n("align", { themeKeys: [], handle: (o) => [l3("vertical-align", o)] }), t.functional("font", (o) => {
    if (!(!o.value || o.modifier)) {
      if (o.value.kind === "arbitrary") {
        let d2 = o.value.value;
        switch (o.value.dataType ?? pe(d2, ["number", "generic-name", "family-name"])) {
          case "generic-name":
          case "family-name":
            return [l3("font-family", d2)];
          default:
            return [I2([$2("--tw-font-weight")]), l3("--tw-font-weight", d2), l3("font-weight", d2)];
        }
      }
      {
        let d2 = r.resolveWith(o.value.value, ["--font"], ["--font-feature-settings", "--font-variation-settings"]);
        if (d2) {
          let [h2, A2 = {}] = d2;
          return [l3("font-family", h2), l3("font-feature-settings", A2["--font-feature-settings"]), l3("font-variation-settings", A2["--font-variation-settings"])];
        }
      }
      {
        let d2 = r.resolve(o.value.value, ["--font-weight"]);
        if (d2)
          return [I2([$2("--tw-font-weight")]), l3("--tw-font-weight", d2), l3("font-weight", d2)];
      }
    }
  }), i("font", () => [{ values: [], valueThemeKeys: ["--font"] }, { values: [], valueThemeKeys: ["--font-weight"] }]), e("uppercase", [["text-transform", "uppercase"]]), e("lowercase", [["text-transform", "lowercase"]]), e("capitalize", [["text-transform", "capitalize"]]), e("normal-case", [["text-transform", "none"]]), e("italic", [["font-style", "italic"]]), e("not-italic", [["font-style", "normal"]]), e("underline", [["text-decoration-line", "underline"]]), e("overline", [["text-decoration-line", "overline"]]), e("line-through", [["text-decoration-line", "line-through"]]), e("no-underline", [["text-decoration-line", "none"]]), e("font-stretch-normal", [["font-stretch", "normal"]]), e("font-stretch-ultra-condensed", [["font-stretch", "ultra-condensed"]]), e("font-stretch-extra-condensed", [["font-stretch", "extra-condensed"]]), e("font-stretch-condensed", [["font-stretch", "condensed"]]), e("font-stretch-semi-condensed", [["font-stretch", "semi-condensed"]]), e("font-stretch-semi-expanded", [["font-stretch", "semi-expanded"]]), e("font-stretch-expanded", [["font-stretch", "expanded"]]), e("font-stretch-extra-expanded", [["font-stretch", "extra-expanded"]]), e("font-stretch-ultra-expanded", [["font-stretch", "ultra-expanded"]]), n("font-stretch", { handleBareValue: ({ value: o }) => {
    if (!o.endsWith("%"))
      return null;
    let d2 = Number(o.slice(0, -1));
    return !p(d2) || Number.isNaN(d2) || d2 < 50 || d2 > 200 ? null : o;
  }, handle: (o) => [l3("font-stretch", o)] }), i("font-stretch", () => [{ values: ["50%", "75%", "90%", "95%", "100%", "105%", "110%", "125%", "150%", "200%"] }]), s2("placeholder", { themeKeys: ["--background-color", "--color"], handle: (o) => [M2("&::placeholder", [l3("--tw-sort", "placeholder-color"), l3("color", o)])] }), e("decoration-solid", [["text-decoration-style", "solid"]]), e("decoration-double", [["text-decoration-style", "double"]]), e("decoration-dotted", [["text-decoration-style", "dotted"]]), e("decoration-dashed", [["text-decoration-style", "dashed"]]), e("decoration-wavy", [["text-decoration-style", "wavy"]]), e("decoration-auto", [["text-decoration-thickness", "auto"]]), e("decoration-from-font", [["text-decoration-thickness", "from-font"]]), t.functional("decoration", (o) => {
    if (o.value) {
      if (o.value.kind === "arbitrary") {
        let d2 = o.value.value;
        switch (o.value.dataType ?? pe(d2, ["color", "length", "percentage"])) {
          case "length":
          case "percentage":
            return o.modifier ? void 0 : [l3("text-decoration-thickness", d2)];
          default:
            return d2 = Q2(d2, o.modifier, r), d2 === null ? void 0 : [l3("text-decoration-color", d2)];
        }
      }
      {
        let d2 = r.resolve(o.value.value, ["--text-decoration-thickness"]);
        if (d2)
          return o.modifier ? void 0 : [l3("text-decoration-thickness", d2)];
        if (p(o.value.value))
          return o.modifier ? void 0 : [l3("text-decoration-thickness", `${o.value.value}px`)];
      }
      {
        let d2 = te2(o, r, ["--text-decoration-color", "--color"]);
        if (d2)
          return [l3("text-decoration-color", d2)];
      }
    }
  }), i("decoration", () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--text-decoration-color", "--color"], modifiers: Array.from({ length: 21 }, (o, d2) => `${d2 * 5}`) }, { values: ["0", "1", "2"], valueThemeKeys: ["--text-decoration-thickness"] }]), e("animate-none", [["animation", "none"]]), n("animate", { themeKeys: ["--animate"], handle: (o) => [l3("animation", o)] });
  {
    let o = ["var(--tw-blur,)", "var(--tw-brightness,)", "var(--tw-contrast,)", "var(--tw-grayscale,)", "var(--tw-hue-rotate,)", "var(--tw-invert,)", "var(--tw-saturate,)", "var(--tw-sepia,)", "var(--tw-drop-shadow,)"].join(" "), d2 = ["var(--tw-backdrop-blur,)", "var(--tw-backdrop-brightness,)", "var(--tw-backdrop-contrast,)", "var(--tw-backdrop-grayscale,)", "var(--tw-backdrop-hue-rotate,)", "var(--tw-backdrop-invert,)", "var(--tw-backdrop-opacity,)", "var(--tw-backdrop-saturate,)", "var(--tw-backdrop-sepia,)"].join(" "), h2 = () => I2([$2("--tw-blur"), $2("--tw-brightness"), $2("--tw-contrast"), $2("--tw-grayscale"), $2("--tw-hue-rotate"), $2("--tw-invert"), $2("--tw-opacity"), $2("--tw-saturate"), $2("--tw-sepia"), $2("--tw-drop-shadow"), $2("--tw-drop-shadow-color"), $2("--tw-drop-shadow-alpha", "100%", "<percentage>"), $2("--tw-drop-shadow-size")]), A2 = () => I2([$2("--tw-backdrop-blur"), $2("--tw-backdrop-brightness"), $2("--tw-backdrop-contrast"), $2("--tw-backdrop-grayscale"), $2("--tw-backdrop-hue-rotate"), $2("--tw-backdrop-invert"), $2("--tw-backdrop-opacity"), $2("--tw-backdrop-saturate"), $2("--tw-backdrop-sepia")]);
    t.functional("filter", (y2) => {
      if (!y2.modifier) {
        if (y2.value === null)
          return [h2(), l3("filter", o)];
        if (y2.value.kind === "arbitrary")
          return [l3("filter", y2.value.value)];
        switch (y2.value.value) {
          case "none":
            return [l3("filter", "none")];
        }
      }
    }), t.functional("backdrop-filter", (y2) => {
      if (!y2.modifier) {
        if (y2.value === null)
          return [A2(), l3("-webkit-backdrop-filter", d2), l3("backdrop-filter", d2)];
        if (y2.value.kind === "arbitrary")
          return [l3("-webkit-backdrop-filter", y2.value.value), l3("backdrop-filter", y2.value.value)];
        switch (y2.value.value) {
          case "none":
            return [l3("-webkit-backdrop-filter", "none"), l3("backdrop-filter", "none")];
        }
      }
    }), n("blur", { themeKeys: ["--blur"], handle: (y2) => [h2(), l3("--tw-blur", `blur(${y2})`), l3("filter", o)] }), e("blur-none", [h2, ["--tw-blur", " "], ["filter", o]]), n("backdrop-blur", { themeKeys: ["--backdrop-blur", "--blur"], handle: (y2) => [A2(), l3("--tw-backdrop-blur", `blur(${y2})`), l3("-webkit-backdrop-filter", d2), l3("backdrop-filter", d2)] }), e("backdrop-blur-none", [A2, ["--tw-backdrop-blur", " "], ["-webkit-backdrop-filter", d2], ["backdrop-filter", d2]]), n("brightness", { themeKeys: ["--brightness"], handleBareValue: ({ value: y2 }) => p(y2) ? `${y2}%` : null, handle: (y2) => [h2(), l3("--tw-brightness", `brightness(${y2})`), l3("filter", o)] }), n("backdrop-brightness", { themeKeys: ["--backdrop-brightness", "--brightness"], handleBareValue: ({ value: y2 }) => p(y2) ? `${y2}%` : null, handle: (y2) => [A2(), l3("--tw-backdrop-brightness", `brightness(${y2})`), l3("-webkit-backdrop-filter", d2), l3("backdrop-filter", d2)] }), i("brightness", () => [{ values: ["0", "50", "75", "90", "95", "100", "105", "110", "125", "150", "200"], valueThemeKeys: ["--brightness"] }]), i("backdrop-brightness", () => [{ values: ["0", "50", "75", "90", "95", "100", "105", "110", "125", "150", "200"], valueThemeKeys: ["--backdrop-brightness", "--brightness"] }]), n("contrast", { themeKeys: ["--contrast"], handleBareValue: ({ value: y2 }) => p(y2) ? `${y2}%` : null, handle: (y2) => [h2(), l3("--tw-contrast", `contrast(${y2})`), l3("filter", o)] }), n("backdrop-contrast", { themeKeys: ["--backdrop-contrast", "--contrast"], handleBareValue: ({ value: y2 }) => p(y2) ? `${y2}%` : null, handle: (y2) => [A2(), l3("--tw-backdrop-contrast", `contrast(${y2})`), l3("-webkit-backdrop-filter", d2), l3("backdrop-filter", d2)] }), i("contrast", () => [{ values: ["0", "50", "75", "100", "125", "150", "200"], valueThemeKeys: ["--contrast"] }]), i("backdrop-contrast", () => [{ values: ["0", "50", "75", "100", "125", "150", "200"], valueThemeKeys: ["--backdrop-contrast", "--contrast"] }]), n("grayscale", { themeKeys: ["--grayscale"], handleBareValue: ({ value: y2 }) => p(y2) ? `${y2}%` : null, defaultValue: "100%", handle: (y2) => [h2(), l3("--tw-grayscale", `grayscale(${y2})`), l3("filter", o)] }), n("backdrop-grayscale", { themeKeys: ["--backdrop-grayscale", "--grayscale"], handleBareValue: ({ value: y2 }) => p(y2) ? `${y2}%` : null, defaultValue: "100%", handle: (y2) => [A2(), l3("--tw-backdrop-grayscale", `grayscale(${y2})`), l3("-webkit-backdrop-filter", d2), l3("backdrop-filter", d2)] }), i("grayscale", () => [{ values: ["0", "25", "50", "75", "100"], valueThemeKeys: ["--grayscale"], hasDefaultValue: true }]), i("backdrop-grayscale", () => [{ values: ["0", "25", "50", "75", "100"], valueThemeKeys: ["--backdrop-grayscale", "--grayscale"], hasDefaultValue: true }]), n("hue-rotate", { supportsNegative: true, themeKeys: ["--hue-rotate"], handleBareValue: ({ value: y2 }) => p(y2) ? `${y2}deg` : null, handle: (y2) => [h2(), l3("--tw-hue-rotate", `hue-rotate(${y2})`), l3("filter", o)] }), n("backdrop-hue-rotate", { supportsNegative: true, themeKeys: ["--backdrop-hue-rotate", "--hue-rotate"], handleBareValue: ({ value: y2 }) => p(y2) ? `${y2}deg` : null, handle: (y2) => [A2(), l3("--tw-backdrop-hue-rotate", `hue-rotate(${y2})`), l3("-webkit-backdrop-filter", d2), l3("backdrop-filter", d2)] }), i("hue-rotate", () => [{ values: ["0", "15", "30", "60", "90", "180"], valueThemeKeys: ["--hue-rotate"] }]), i("backdrop-hue-rotate", () => [{ values: ["0", "15", "30", "60", "90", "180"], valueThemeKeys: ["--backdrop-hue-rotate", "--hue-rotate"] }]), n("invert", { themeKeys: ["--invert"], handleBareValue: ({ value: y2 }) => p(y2) ? `${y2}%` : null, defaultValue: "100%", handle: (y2) => [h2(), l3("--tw-invert", `invert(${y2})`), l3("filter", o)] }), n("backdrop-invert", { themeKeys: ["--backdrop-invert", "--invert"], handleBareValue: ({ value: y2 }) => p(y2) ? `${y2}%` : null, defaultValue: "100%", handle: (y2) => [A2(), l3("--tw-backdrop-invert", `invert(${y2})`), l3("-webkit-backdrop-filter", d2), l3("backdrop-filter", d2)] }), i("invert", () => [{ values: ["0", "25", "50", "75", "100"], valueThemeKeys: ["--invert"], hasDefaultValue: true }]), i("backdrop-invert", () => [{ values: ["0", "25", "50", "75", "100"], valueThemeKeys: ["--backdrop-invert", "--invert"], hasDefaultValue: true }]), n("saturate", { themeKeys: ["--saturate"], handleBareValue: ({ value: y2 }) => p(y2) ? `${y2}%` : null, handle: (y2) => [h2(), l3("--tw-saturate", `saturate(${y2})`), l3("filter", o)] }), n("backdrop-saturate", { themeKeys: ["--backdrop-saturate", "--saturate"], handleBareValue: ({ value: y2 }) => p(y2) ? `${y2}%` : null, handle: (y2) => [A2(), l3("--tw-backdrop-saturate", `saturate(${y2})`), l3("-webkit-backdrop-filter", d2), l3("backdrop-filter", d2)] }), i("saturate", () => [{ values: ["0", "50", "100", "150", "200"], valueThemeKeys: ["--saturate"] }]), i("backdrop-saturate", () => [{ values: ["0", "50", "100", "150", "200"], valueThemeKeys: ["--backdrop-saturate", "--saturate"] }]), n("sepia", { themeKeys: ["--sepia"], handleBareValue: ({ value: y2 }) => p(y2) ? `${y2}%` : null, defaultValue: "100%", handle: (y2) => [h2(), l3("--tw-sepia", `sepia(${y2})`), l3("filter", o)] }), n("backdrop-sepia", { themeKeys: ["--backdrop-sepia", "--sepia"], handleBareValue: ({ value: y2 }) => p(y2) ? `${y2}%` : null, defaultValue: "100%", handle: (y2) => [A2(), l3("--tw-backdrop-sepia", `sepia(${y2})`), l3("-webkit-backdrop-filter", d2), l3("backdrop-filter", d2)] }), i("sepia", () => [{ values: ["0", "50", "100"], valueThemeKeys: ["--sepia"], hasDefaultValue: true }]), i("backdrop-sepia", () => [{ values: ["0", "50", "100"], valueThemeKeys: ["--backdrop-sepia", "--sepia"], hasDefaultValue: true }]), e("drop-shadow-none", [h2, ["--tw-drop-shadow", " "], ["filter", o]]), t.functional("drop-shadow", (y2) => {
      let C2;
      if (y2.modifier && (y2.modifier.kind === "arbitrary" ? C2 = y2.modifier.value : p(y2.modifier.value) && (C2 = `${y2.modifier.value}%`)), !y2.value) {
        let R2 = r.get(["--drop-shadow"]), V2 = r.resolve(null, ["--drop-shadow"]);
        return R2 === null || V2 === null ? void 0 : [h2(), l3("--tw-drop-shadow-alpha", C2), ...Ye("--tw-drop-shadow-size", R2, C2, (T2) => `var(--tw-drop-shadow-color, ${T2})`), l3("--tw-drop-shadow", g(V2, ",").map((T2) => `drop-shadow(${T2})`).join(" ")), l3("filter", o)];
      }
      if (y2.value.kind === "arbitrary") {
        let R2 = y2.value.value;
        switch (y2.value.dataType ?? pe(R2, ["color"])) {
          case "color":
            return R2 = Q2(R2, y2.modifier, r), R2 === null ? void 0 : [h2(), l3("--tw-drop-shadow-color", Z2(R2, "var(--tw-drop-shadow-alpha)")), l3("--tw-drop-shadow", "var(--tw-drop-shadow-size)")];
          default:
            return y2.modifier && !C2 ? void 0 : [h2(), l3("--tw-drop-shadow-alpha", C2), ...Ye("--tw-drop-shadow-size", R2, C2, (T2) => `var(--tw-drop-shadow-color, ${T2})`), l3("--tw-drop-shadow", "var(--tw-drop-shadow-size)"), l3("filter", o)];
        }
      }
      {
        let R2 = r.get([`--drop-shadow-${y2.value.value}`]), V2 = r.resolve(y2.value.value, ["--drop-shadow"]);
        if (R2 && V2)
          return y2.modifier && !C2 ? void 0 : C2 ? [h2(), l3("--tw-drop-shadow-alpha", C2), ...Ye("--tw-drop-shadow-size", R2, C2, (T2) => `var(--tw-drop-shadow-color, ${T2})`), l3("--tw-drop-shadow", "var(--tw-drop-shadow-size)"), l3("filter", o)] : [h2(), l3("--tw-drop-shadow-alpha", C2), ...Ye("--tw-drop-shadow-size", R2, C2, (T2) => `var(--tw-drop-shadow-color, ${T2})`), l3("--tw-drop-shadow", g(V2, ",").map((T2) => `drop-shadow(${T2})`).join(" ")), l3("filter", o)];
      }
      {
        let R2 = te2(y2, r, ["--drop-shadow-color", "--color"]);
        if (R2)
          return R2 === "inherit" ? [h2(), l3("--tw-drop-shadow-color", "inherit"), l3("--tw-drop-shadow", "var(--tw-drop-shadow-size)")] : [h2(), l3("--tw-drop-shadow-color", Z2(R2, "var(--tw-drop-shadow-alpha)")), l3("--tw-drop-shadow", "var(--tw-drop-shadow-size)")];
      }
    }), i("drop-shadow", () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--drop-shadow-color", "--color"], modifiers: Array.from({ length: 21 }, (y2, C2) => `${C2 * 5}`) }, { valueThemeKeys: ["--drop-shadow"] }]), n("backdrop-opacity", { themeKeys: ["--backdrop-opacity", "--opacity"], handleBareValue: ({ value: y2 }) => de(y2) ? `${y2}%` : null, handle: (y2) => [A2(), l3("--tw-backdrop-opacity", `opacity(${y2})`), l3("-webkit-backdrop-filter", d2), l3("backdrop-filter", d2)] }), i("backdrop-opacity", () => [{ values: Array.from({ length: 21 }, (y2, C2) => `${C2 * 5}`), valueThemeKeys: ["--backdrop-opacity", "--opacity"] }]);
  }
  {
    let o = `var(--tw-ease, ${r.resolve(null, ["--default-transition-timing-function"]) ?? "ease"})`, d2 = `var(--tw-duration, ${r.resolve(null, ["--default-transition-duration"]) ?? "0s"})`;
    e("transition-none", [["transition-property", "none"]]), e("transition-all", [["transition-property", "all"], ["transition-timing-function", o], ["transition-duration", d2]]), e("transition-colors", [["transition-property", "color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"], ["transition-timing-function", o], ["transition-duration", d2]]), e("transition-opacity", [["transition-property", "opacity"], ["transition-timing-function", o], ["transition-duration", d2]]), e("transition-shadow", [["transition-property", "box-shadow"], ["transition-timing-function", o], ["transition-duration", d2]]), e("transition-transform", [["transition-property", "transform, translate, scale, rotate"], ["transition-timing-function", o], ["transition-duration", d2]]), n("transition", { defaultValue: "color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events", themeKeys: ["--transition-property"], handle: (h2) => [l3("transition-property", h2), l3("transition-timing-function", o), l3("transition-duration", d2)] }), e("transition-discrete", [["transition-behavior", "allow-discrete"]]), e("transition-normal", [["transition-behavior", "normal"]]), n("delay", { handleBareValue: ({ value: h2 }) => p(h2) ? `${h2}ms` : null, themeKeys: ["--transition-delay"], handle: (h2) => [l3("transition-delay", h2)] });
    {
      let h2 = () => I2([$2("--tw-duration")]);
      e("duration-initial", [h2, ["--tw-duration", "initial"]]), t.functional("duration", (A2) => {
        if (A2.modifier || !A2.value)
          return;
        let y2 = null;
        if (A2.value.kind === "arbitrary" ? y2 = A2.value.value : (y2 = r.resolve(A2.value.fraction ?? A2.value.value, ["--transition-duration"]), y2 === null && p(A2.value.value) && (y2 = `${A2.value.value}ms`)), y2 !== null)
          return [h2(), l3("--tw-duration", y2), l3("transition-duration", y2)];
      });
    }
    i("delay", () => [{ values: ["75", "100", "150", "200", "300", "500", "700", "1000"], valueThemeKeys: ["--transition-delay"] }]), i("duration", () => [{ values: ["75", "100", "150", "200", "300", "500", "700", "1000"], valueThemeKeys: ["--transition-duration"] }]);
  }
  {
    let o = () => I2([$2("--tw-ease")]);
    e("ease-initial", [o, ["--tw-ease", "initial"]]), e("ease-linear", [o, ["--tw-ease", "linear"], ["transition-timing-function", "linear"]]), n("ease", { themeKeys: ["--ease"], handle: (d2) => [o(), l3("--tw-ease", d2), l3("transition-timing-function", d2)] });
  }
  e("will-change-auto", [["will-change", "auto"]]), e("will-change-scroll", [["will-change", "scroll-position"]]), e("will-change-contents", [["will-change", "contents"]]), e("will-change-transform", [["will-change", "transform"]]), n("will-change", { themeKeys: [], handle: (o) => [l3("will-change", o)] }), e("content-none", [["--tw-content", "none"], ["content", "none"]]), n("content", { themeKeys: [], handle: (o) => [I2([$2("--tw-content", '""')]), l3("--tw-content", o), l3("content", "var(--tw-content)")] });
  {
    let o = "var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)", d2 = () => I2([$2("--tw-contain-size"), $2("--tw-contain-layout"), $2("--tw-contain-paint"), $2("--tw-contain-style")]);
    e("contain-none", [["contain", "none"]]), e("contain-content", [["contain", "content"]]), e("contain-strict", [["contain", "strict"]]), e("contain-size", [d2, ["--tw-contain-size", "size"], ["contain", o]]), e("contain-inline-size", [d2, ["--tw-contain-size", "inline-size"], ["contain", o]]), e("contain-layout", [d2, ["--tw-contain-layout", "layout"], ["contain", o]]), e("contain-paint", [d2, ["--tw-contain-paint", "paint"], ["contain", o]]), e("contain-style", [d2, ["--tw-contain-style", "style"], ["contain", o]]), n("contain", { themeKeys: [], handle: (h2) => [l3("contain", h2)] });
  }
  e("forced-color-adjust-none", [["forced-color-adjust", "none"]]), e("forced-color-adjust-auto", [["forced-color-adjust", "auto"]]), e("leading-none", [() => I2([$2("--tw-leading")]), ["--tw-leading", "1"], ["line-height", "1"]]), a("leading", ["--leading", "--spacing"], (o) => [I2([$2("--tw-leading")]), l3("--tw-leading", o), l3("line-height", o)]), n("tracking", { supportsNegative: true, themeKeys: ["--tracking"], handle: (o) => [I2([$2("--tw-tracking")]), l3("--tw-tracking", o), l3("letter-spacing", o)] }), e("antialiased", [["-webkit-font-smoothing", "antialiased"], ["-moz-osx-font-smoothing", "grayscale"]]), e("subpixel-antialiased", [["-webkit-font-smoothing", "auto"], ["-moz-osx-font-smoothing", "auto"]]);
  {
    let o = "var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)", d2 = () => I2([$2("--tw-ordinal"), $2("--tw-slashed-zero"), $2("--tw-numeric-figure"), $2("--tw-numeric-spacing"), $2("--tw-numeric-fraction")]);
    e("normal-nums", [["font-variant-numeric", "normal"]]), e("ordinal", [d2, ["--tw-ordinal", "ordinal"], ["font-variant-numeric", o]]), e("slashed-zero", [d2, ["--tw-slashed-zero", "slashed-zero"], ["font-variant-numeric", o]]), e("lining-nums", [d2, ["--tw-numeric-figure", "lining-nums"], ["font-variant-numeric", o]]), e("oldstyle-nums", [d2, ["--tw-numeric-figure", "oldstyle-nums"], ["font-variant-numeric", o]]), e("proportional-nums", [d2, ["--tw-numeric-spacing", "proportional-nums"], ["font-variant-numeric", o]]), e("tabular-nums", [d2, ["--tw-numeric-spacing", "tabular-nums"], ["font-variant-numeric", o]]), e("diagonal-fractions", [d2, ["--tw-numeric-fraction", "diagonal-fractions"], ["font-variant-numeric", o]]), e("stacked-fractions", [d2, ["--tw-numeric-fraction", "stacked-fractions"], ["font-variant-numeric", o]]);
  }
  {
    let o = () => I2([$2("--tw-outline-style", "solid")]);
    t.static("outline-hidden", () => [l3("--tw-outline-style", "none"), l3("outline-style", "none"), z2("@media", "(forced-colors: active)", [l3("outline", "2px solid transparent"), l3("outline-offset", "2px")])]), e("outline-none", [["--tw-outline-style", "none"], ["outline-style", "none"]]), e("outline-solid", [["--tw-outline-style", "solid"], ["outline-style", "solid"]]), e("outline-dashed", [["--tw-outline-style", "dashed"], ["outline-style", "dashed"]]), e("outline-dotted", [["--tw-outline-style", "dotted"], ["outline-style", "dotted"]]), e("outline-double", [["--tw-outline-style", "double"], ["outline-style", "double"]]), t.functional("outline", (d2) => {
      if (d2.value === null) {
        if (d2.modifier)
          return;
        let h2 = r.get(["--default-outline-width"]) ?? "1px";
        return [o(), l3("outline-style", "var(--tw-outline-style)"), l3("outline-width", h2)];
      }
      if (d2.value.kind === "arbitrary") {
        let h2 = d2.value.value;
        switch (d2.value.dataType ?? pe(h2, ["color", "length", "number", "percentage"])) {
          case "length":
          case "number":
          case "percentage":
            return d2.modifier ? void 0 : [o(), l3("outline-style", "var(--tw-outline-style)"), l3("outline-width", h2)];
          default:
            return h2 = Q2(h2, d2.modifier, r), h2 === null ? void 0 : [l3("outline-color", h2)];
        }
      }
      {
        let h2 = te2(d2, r, ["--outline-color", "--color"]);
        if (h2)
          return [l3("outline-color", h2)];
      }
      {
        if (d2.modifier)
          return;
        let h2 = r.resolve(d2.value.value, ["--outline-width"]);
        if (h2)
          return [o(), l3("outline-style", "var(--tw-outline-style)"), l3("outline-width", h2)];
        if (p(d2.value.value))
          return [o(), l3("outline-style", "var(--tw-outline-style)"), l3("outline-width", `${d2.value.value}px`)];
      }
    }), i("outline", () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--outline-color", "--color"], modifiers: Array.from({ length: 21 }, (d2, h2) => `${h2 * 5}`), hasDefaultValue: true }, { values: ["0", "1", "2", "4", "8"], valueThemeKeys: ["--outline-width"] }]), n("outline-offset", { supportsNegative: true, themeKeys: ["--outline-offset"], handleBareValue: ({ value: d2 }) => p(d2) ? `${d2}px` : null, handle: (d2) => [l3("outline-offset", d2)] }), i("outline-offset", () => [{ supportsNegative: true, values: ["0", "1", "2", "4", "8"], valueThemeKeys: ["--outline-offset"] }]);
  }
  n("opacity", { themeKeys: ["--opacity"], handleBareValue: ({ value: o }) => de(o) ? `${o}%` : null, handle: (o) => [l3("opacity", o)] }), i("opacity", () => [{ values: Array.from({ length: 21 }, (o, d2) => `${d2 * 5}`), valueThemeKeys: ["--opacity"] }]), e("underline-offset-auto", [["text-underline-offset", "auto"]]), n("underline-offset", { supportsNegative: true, themeKeys: ["--text-underline-offset"], handleBareValue: ({ value: o }) => p(o) ? `${o}px` : null, handle: (o) => [l3("text-underline-offset", o)] }), i("underline-offset", () => [{ supportsNegative: true, values: ["0", "1", "2", "4", "8"], valueThemeKeys: ["--text-underline-offset"] }]), t.functional("text", (o) => {
    if (o.value) {
      if (o.value.kind === "arbitrary") {
        let d2 = o.value.value;
        switch (o.value.dataType ?? pe(d2, ["color", "length", "percentage", "absolute-size", "relative-size"])) {
          case "size":
          case "length":
          case "percentage":
          case "absolute-size":
          case "relative-size": {
            if (o.modifier) {
              let A2 = o.modifier.kind === "arbitrary" ? o.modifier.value : r.resolve(o.modifier.value, ["--leading"]);
              if (!A2 && ue(o.modifier.value)) {
                let y2 = r.resolve(null, ["--spacing"]);
                if (!y2)
                  return null;
                A2 = `calc(${y2} * ${o.modifier.value})`;
              }
              return !A2 && o.modifier.value === "none" && (A2 = "1"), A2 ? [l3("font-size", d2), l3("line-height", A2)] : null;
            }
            return [l3("font-size", d2)];
          }
          default:
            return d2 = Q2(d2, o.modifier, r), d2 === null ? void 0 : [l3("color", d2)];
        }
      }
      {
        let d2 = te2(o, r, ["--text-color", "--color"]);
        if (d2)
          return [l3("color", d2)];
      }
      {
        let d2 = r.resolveWith(o.value.value, ["--text"], ["--line-height", "--letter-spacing", "--font-weight"]);
        if (d2) {
          let [h2, A2 = {}] = Array.isArray(d2) ? d2 : [d2];
          if (o.modifier) {
            let y2 = o.modifier.kind === "arbitrary" ? o.modifier.value : r.resolve(o.modifier.value, ["--leading"]);
            if (!y2 && ue(o.modifier.value)) {
              let R2 = r.resolve(null, ["--spacing"]);
              if (!R2)
                return null;
              y2 = `calc(${R2} * ${o.modifier.value})`;
            }
            if (!y2 && o.modifier.value === "none" && (y2 = "1"), !y2)
              return null;
            let C2 = [l3("font-size", h2)];
            return y2 && C2.push(l3("line-height", y2)), C2;
          }
          return typeof A2 == "string" ? [l3("font-size", h2), l3("line-height", A2)] : [l3("font-size", h2), l3("line-height", A2["--line-height"] ? `var(--tw-leading, ${A2["--line-height"]})` : void 0), l3("letter-spacing", A2["--letter-spacing"] ? `var(--tw-tracking, ${A2["--letter-spacing"]})` : void 0), l3("font-weight", A2["--font-weight"] ? `var(--tw-font-weight, ${A2["--font-weight"]})` : void 0)];
        }
      }
    }
  }), i("text", () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--text-color", "--color"], modifiers: Array.from({ length: 21 }, (o, d2) => `${d2 * 5}`) }, { values: [], valueThemeKeys: ["--text"], modifiers: [], modifierThemeKeys: ["--leading"] }]);
  let F2 = () => I2([$2("--tw-text-shadow-color"), $2("--tw-text-shadow-alpha", "100%", "<percentage>")]);
  e("text-shadow-initial", [F2, ["--tw-text-shadow-color", "initial"]]), t.functional("text-shadow", (o) => {
    let d2;
    if (o.modifier && (o.modifier.kind === "arbitrary" ? d2 = o.modifier.value : p(o.modifier.value) && (d2 = `${o.modifier.value}%`)), !o.value) {
      let h2 = r.get(["--text-shadow"]);
      return h2 === null ? void 0 : [F2(), l3("--tw-text-shadow-alpha", d2), ...ue2("text-shadow", h2, d2, (A2) => `var(--tw-text-shadow-color, ${A2})`)];
    }
    if (o.value.kind === "arbitrary") {
      let h2 = o.value.value;
      switch (o.value.dataType ?? pe(h2, ["color"])) {
        case "color":
          return h2 = Q2(h2, o.modifier, r), h2 === null ? void 0 : [F2(), l3("--tw-text-shadow-color", Z2(h2, "var(--tw-text-shadow-alpha)"))];
        default:
          return [F2(), l3("--tw-text-shadow-alpha", d2), ...ue2("text-shadow", h2, d2, (y2) => `var(--tw-text-shadow-color, ${y2})`)];
      }
    }
    switch (o.value.value) {
      case "none":
        return o.modifier ? void 0 : [F2(), l3("text-shadow", "none")];
      case "inherit":
        return o.modifier ? void 0 : [F2(), l3("--tw-text-shadow-color", "inherit")];
    }
    {
      let h2 = r.get([`--text-shadow-${o.value.value}`]);
      if (h2)
        return [F2(), l3("--tw-text-shadow-alpha", d2), ...ue2("text-shadow", h2, d2, (A2) => `var(--tw-text-shadow-color, ${A2})`)];
    }
    {
      let h2 = te2(o, r, ["--text-shadow-color", "--color"]);
      if (h2)
        return [F2(), l3("--tw-text-shadow-color", Z2(h2, "var(--tw-text-shadow-alpha)"))];
    }
  }), i("text-shadow", () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--text-shadow-color", "--color"], modifiers: Array.from({ length: 21 }, (o, d2) => `${d2 * 5}`) }, { values: ["none"] }, { valueThemeKeys: ["--text-shadow"], modifiers: Array.from({ length: 21 }, (o, d2) => `${d2 * 5}`), hasDefaultValue: r.get(["--text-shadow"]) !== null }]);
  {
    let y2 = function(V2) {
      return `var(--tw-ring-inset,) 0 0 0 calc(${V2} + var(--tw-ring-offset-width)) var(--tw-ring-color, ${A2})`;
    }, C2 = function(V2) {
      return `inset 0 0 0 ${V2} var(--tw-inset-ring-color, currentcolor)`;
    };
    var X2 = y2, re2 = C2;
    let o = ["var(--tw-inset-shadow)", "var(--tw-inset-ring-shadow)", "var(--tw-ring-offset-shadow)", "var(--tw-ring-shadow)", "var(--tw-shadow)"].join(", "), d2 = "0 0 #0000", h2 = () => I2([$2("--tw-shadow", d2), $2("--tw-shadow-color"), $2("--tw-shadow-alpha", "100%", "<percentage>"), $2("--tw-inset-shadow", d2), $2("--tw-inset-shadow-color"), $2("--tw-inset-shadow-alpha", "100%", "<percentage>"), $2("--tw-ring-color"), $2("--tw-ring-shadow", d2), $2("--tw-inset-ring-color"), $2("--tw-inset-ring-shadow", d2), $2("--tw-ring-inset"), $2("--tw-ring-offset-width", "0px", "<length>"), $2("--tw-ring-offset-color", "#fff"), $2("--tw-ring-offset-shadow", d2)]);
    e("shadow-initial", [h2, ["--tw-shadow-color", "initial"]]), t.functional("shadow", (V2) => {
      let T2;
      if (V2.modifier && (V2.modifier.kind === "arbitrary" ? T2 = V2.modifier.value : p(V2.modifier.value) && (T2 = `${V2.modifier.value}%`)), !V2.value) {
        let _2 = r.get(["--shadow"]);
        return _2 === null ? void 0 : [h2(), l3("--tw-shadow-alpha", T2), ...ue2("--tw-shadow", _2, T2, (oe) => `var(--tw-shadow-color, ${oe})`), l3("box-shadow", o)];
      }
      if (V2.value.kind === "arbitrary") {
        let _2 = V2.value.value;
        switch (V2.value.dataType ?? pe(_2, ["color"])) {
          case "color":
            return _2 = Q2(_2, V2.modifier, r), _2 === null ? void 0 : [h2(), l3("--tw-shadow-color", Z2(_2, "var(--tw-shadow-alpha)"))];
          default:
            return [h2(), l3("--tw-shadow-alpha", T2), ...ue2("--tw-shadow", _2, T2, (ot) => `var(--tw-shadow-color, ${ot})`), l3("box-shadow", o)];
        }
      }
      switch (V2.value.value) {
        case "none":
          return V2.modifier ? void 0 : [h2(), l3("--tw-shadow", d2), l3("box-shadow", o)];
        case "inherit":
          return V2.modifier ? void 0 : [h2(), l3("--tw-shadow-color", "inherit")];
      }
      {
        let _2 = r.get([`--shadow-${V2.value.value}`]);
        if (_2)
          return [h2(), l3("--tw-shadow-alpha", T2), ...ue2("--tw-shadow", _2, T2, (oe) => `var(--tw-shadow-color, ${oe})`), l3("box-shadow", o)];
      }
      {
        let _2 = te2(V2, r, ["--box-shadow-color", "--color"]);
        if (_2)
          return [h2(), l3("--tw-shadow-color", Z2(_2, "var(--tw-shadow-alpha)"))];
      }
    }), i("shadow", () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--box-shadow-color", "--color"], modifiers: Array.from({ length: 21 }, (V2, T2) => `${T2 * 5}`) }, { values: ["none"] }, { valueThemeKeys: ["--shadow"], modifiers: Array.from({ length: 21 }, (V2, T2) => `${T2 * 5}`), hasDefaultValue: r.get(["--shadow"]) !== null }]), e("inset-shadow-initial", [h2, ["--tw-inset-shadow-color", "initial"]]), t.functional("inset-shadow", (V2) => {
      let T2;
      if (V2.modifier && (V2.modifier.kind === "arbitrary" ? T2 = V2.modifier.value : p(V2.modifier.value) && (T2 = `${V2.modifier.value}%`)), !V2.value) {
        let _2 = r.get(["--inset-shadow"]);
        return _2 === null ? void 0 : [h2(), l3("--tw-inset-shadow-alpha", T2), ...ue2("--tw-inset-shadow", _2, T2, (oe) => `var(--tw-inset-shadow-color, ${oe})`), l3("box-shadow", o)];
      }
      if (V2.value.kind === "arbitrary") {
        let _2 = V2.value.value;
        switch (V2.value.dataType ?? pe(_2, ["color"])) {
          case "color":
            return _2 = Q2(_2, V2.modifier, r), _2 === null ? void 0 : [h2(), l3("--tw-inset-shadow-color", Z2(_2, "var(--tw-inset-shadow-alpha)"))];
          default:
            return [h2(), l3("--tw-inset-shadow-alpha", T2), ...ue2("--tw-inset-shadow", _2, T2, (ot) => `var(--tw-inset-shadow-color, ${ot})`, "inset "), l3("box-shadow", o)];
        }
      }
      switch (V2.value.value) {
        case "none":
          return V2.modifier ? void 0 : [h2(), l3("--tw-inset-shadow", d2), l3("box-shadow", o)];
        case "inherit":
          return V2.modifier ? void 0 : [h2(), l3("--tw-inset-shadow-color", "inherit")];
      }
      {
        let _2 = r.get([`--inset-shadow-${V2.value.value}`]);
        if (_2)
          return [h2(), l3("--tw-inset-shadow-alpha", T2), ...ue2("--tw-inset-shadow", _2, T2, (oe) => `var(--tw-inset-shadow-color, ${oe})`), l3("box-shadow", o)];
      }
      {
        let _2 = te2(V2, r, ["--box-shadow-color", "--color"]);
        if (_2)
          return [h2(), l3("--tw-inset-shadow-color", Z2(_2, "var(--tw-inset-shadow-alpha)"))];
      }
    }), i("inset-shadow", () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--box-shadow-color", "--color"], modifiers: Array.from({ length: 21 }, (V2, T2) => `${T2 * 5}`) }, { values: ["none"] }, { valueThemeKeys: ["--inset-shadow"], modifiers: Array.from({ length: 21 }, (V2, T2) => `${T2 * 5}`), hasDefaultValue: r.get(["--inset-shadow"]) !== null }]), e("ring-inset", [h2, ["--tw-ring-inset", "inset"]]);
    let A2 = r.get(["--default-ring-color"]) ?? "currentcolor";
    t.functional("ring", (V2) => {
      if (!V2.value) {
        if (V2.modifier)
          return;
        let T2 = r.get(["--default-ring-width"]) ?? "1px";
        return [h2(), l3("--tw-ring-shadow", y2(T2)), l3("box-shadow", o)];
      }
      if (V2.value.kind === "arbitrary") {
        let T2 = V2.value.value;
        switch (V2.value.dataType ?? pe(T2, ["color", "length"])) {
          case "length":
            return V2.modifier ? void 0 : [h2(), l3("--tw-ring-shadow", y2(T2)), l3("box-shadow", o)];
          default:
            return T2 = Q2(T2, V2.modifier, r), T2 === null ? void 0 : [l3("--tw-ring-color", T2)];
        }
      }
      {
        let T2 = te2(V2, r, ["--ring-color", "--color"]);
        if (T2)
          return [l3("--tw-ring-color", T2)];
      }
      {
        if (V2.modifier)
          return;
        let T2 = r.resolve(V2.value.value, ["--ring-width"]);
        if (T2 === null && p(V2.value.value) && (T2 = `${V2.value.value}px`), T2)
          return [h2(), l3("--tw-ring-shadow", y2(T2)), l3("box-shadow", o)];
      }
    }), i("ring", () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--ring-color", "--color"], modifiers: Array.from({ length: 21 }, (V2, T2) => `${T2 * 5}`) }, { values: ["0", "1", "2", "4", "8"], valueThemeKeys: ["--ring-width"], hasDefaultValue: true }]), t.functional("inset-ring", (V2) => {
      if (!V2.value)
        return V2.modifier ? void 0 : [h2(), l3("--tw-inset-ring-shadow", C2("1px")), l3("box-shadow", o)];
      if (V2.value.kind === "arbitrary") {
        let T2 = V2.value.value;
        switch (V2.value.dataType ?? pe(T2, ["color", "length"])) {
          case "length":
            return V2.modifier ? void 0 : [h2(), l3("--tw-inset-ring-shadow", C2(T2)), l3("box-shadow", o)];
          default:
            return T2 = Q2(T2, V2.modifier, r), T2 === null ? void 0 : [l3("--tw-inset-ring-color", T2)];
        }
      }
      {
        let T2 = te2(V2, r, ["--ring-color", "--color"]);
        if (T2)
          return [l3("--tw-inset-ring-color", T2)];
      }
      {
        if (V2.modifier)
          return;
        let T2 = r.resolve(V2.value.value, ["--ring-width"]);
        if (T2 === null && p(V2.value.value) && (T2 = `${V2.value.value}px`), T2)
          return [h2(), l3("--tw-inset-ring-shadow", C2(T2)), l3("box-shadow", o)];
      }
    }), i("inset-ring", () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--ring-color", "--color"], modifiers: Array.from({ length: 21 }, (V2, T2) => `${T2 * 5}`) }, { values: ["0", "1", "2", "4", "8"], valueThemeKeys: ["--ring-width"], hasDefaultValue: true }]);
    let R2 = "var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)";
    t.functional("ring-offset", (V2) => {
      if (V2.value) {
        if (V2.value.kind === "arbitrary") {
          let T2 = V2.value.value;
          switch (V2.value.dataType ?? pe(T2, ["color", "length"])) {
            case "length":
              return V2.modifier ? void 0 : [l3("--tw-ring-offset-width", T2), l3("--tw-ring-offset-shadow", R2)];
            default:
              return T2 = Q2(T2, V2.modifier, r), T2 === null ? void 0 : [l3("--tw-ring-offset-color", T2)];
          }
        }
        {
          let T2 = r.resolve(V2.value.value, ["--ring-offset-width"]);
          if (T2)
            return V2.modifier ? void 0 : [l3("--tw-ring-offset-width", T2), l3("--tw-ring-offset-shadow", R2)];
          if (p(V2.value.value))
            return V2.modifier ? void 0 : [l3("--tw-ring-offset-width", `${V2.value.value}px`), l3("--tw-ring-offset-shadow", R2)];
        }
        {
          let T2 = te2(V2, r, ["--ring-offset-color", "--color"]);
          if (T2)
            return [l3("--tw-ring-offset-color", T2)];
        }
      }
    });
  }
  return i("ring-offset", () => [{ values: ["current", "inherit", "transparent"], valueThemeKeys: ["--ring-offset-color", "--color"], modifiers: Array.from({ length: 21 }, (o, d2) => `${d2 * 5}`) }, { values: ["0", "1", "2", "4", "8"], valueThemeKeys: ["--ring-offset-width"] }]), t.functional("@container", (o) => {
    let d2 = null;
    if (o.value === null ? d2 = "inline-size" : o.value.kind === "arbitrary" ? d2 = o.value.value : o.value.kind === "named" && o.value.value === "normal" && (d2 = "normal"), d2 !== null)
      return o.modifier ? [l3("container-type", d2), l3("container-name", o.modifier.value)] : [l3("container-type", d2)];
  }), i("@container", () => [{ values: ["normal"], valueThemeKeys: [], hasDefaultValue: true }]), t;
}
var bt = ["number", "integer", "ratio", "percentage"];
function ur(r) {
  let t = r.params;
  return Ci.test(t) ? (i) => {
    let e = { "--value": { usedSpacingInteger: false, usedSpacingNumber: false, themeKeys: /* @__PURE__ */ new Set(), literals: /* @__PURE__ */ new Set() }, "--modifier": { usedSpacingInteger: false, usedSpacingNumber: false, themeKeys: /* @__PURE__ */ new Set(), literals: /* @__PURE__ */ new Set() } };
    D2(r.nodes, (n) => {
      if (n.kind !== "declaration" || !n.value || !n.value.includes("--value(") && !n.value.includes("--modifier("))
        return;
      let s2 = B2(n.value);
      ee2(s2, (a) => {
        if (a.kind !== "function")
          return;
        if (a.value === "--spacing" && !(e["--modifier"].usedSpacingNumber && e["--value"].usedSpacingNumber))
          return ee2(a.nodes, (u2) => {
            var _a2, _b;
            if (u2.kind !== "function" || u2.value !== "--value" && u2.value !== "--modifier")
              return;
            let f2 = u2.value;
            for (let g2 of u2.nodes)
              if (g2.kind === "word") {
                if (g2.value === "integer")
                  (_a2 = e[f2]).usedSpacingInteger || (_a2.usedSpacingInteger = true);
                else if (g2.value === "number" && ((_b = e[f2]).usedSpacingNumber || (_b.usedSpacingNumber = true), e["--modifier"].usedSpacingNumber && e["--value"].usedSpacingNumber))
                  return 2;
              }
          }), 0;
        if (a.value !== "--value" && a.value !== "--modifier")
          return;
        let c = g(Y2(a.nodes), ",");
        for (let [u2, f2] of c.entries())
          f2 = f2.replace(/\\\*/g, "*"), f2 = f2.replace(/--(.*?)\s--(.*?)/g, "--$1-*--$2"), f2 = f2.replace(/\s+/g, ""), f2 = f2.replace(/(-\*){2,}/g, "-*"), f2[0] === "-" && f2[1] === "-" && !f2.includes("-*") && (f2 += "-*"), c[u2] = f2;
        a.nodes = B2(c.join(","));
        for (let u2 of a.nodes)
          if (u2.kind === "word" && (u2.value[0] === '"' || u2.value[0] === "'") && u2.value[0] === u2.value[u2.value.length - 1]) {
            let f2 = u2.value.slice(1, -1);
            e[a.value].literals.add(f2);
          } else if (u2.kind === "word" && u2.value[0] === "-" && u2.value[1] === "-") {
            let f2 = u2.value.replace(/-\*.*$/g, "");
            e[a.value].themeKeys.add(f2);
          } else if (u2.kind === "word" && !(u2.value[0] === "[" && u2.value[u2.value.length - 1] === "]") && !bt.includes(u2.value)) {
            console.warn(`Unsupported bare value data type: "${u2.value}".
Only valid data types are: ${bt.map((x) => `"${x}"`).join(", ")}.
`);
            let f2 = u2.value, g2 = structuredClone(a), p2 = "¶";
            ee2(g2.nodes, (x, { replaceWith: k2 }) => {
              x.kind === "word" && x.value === f2 && k2({ kind: "word", value: p2 });
            });
            let m = "^".repeat(Y2([u2]).length), w2 = Y2([g2]).indexOf(p2), v2 = ["```css", Y2([a]), " ".repeat(w2) + m, "```"].join(`
`);
            console.warn(v2);
          }
      }), n.value = Y2(s2);
    }), i.utilities.functional(t.slice(0, -2), (n) => {
      let s2 = structuredClone(r), a = n.value, c = n.modifier;
      if (a === null)
        return;
      let u2 = false, f2 = false, g2 = false, p2 = false, m = /* @__PURE__ */ new Map(), w2 = false;
      if (D2([s2], (v2, { parent: x, replaceWith: k2 }) => {
        if ((x == null ? void 0 : x.kind) !== "rule" && (x == null ? void 0 : x.kind) !== "at-rule" || v2.kind !== "declaration" || !v2.value)
          return;
        let N2 = B2(v2.value);
        (ee2(N2, (S, { replaceWith: P2 }) => {
          if (S.kind === "function") {
            if (S.value === "--value") {
              u2 = true;
              let j2 = or(a, S, i);
              return j2 ? (f2 = true, j2.ratio ? w2 = true : m.set(v2, x), P2(j2.nodes), 1) : (u2 || (u2 = false), k2([]), 2);
            } else if (S.value === "--modifier") {
              if (c === null)
                return k2([]), 2;
              g2 = true;
              let j2 = or(c, S, i);
              return j2 ? (p2 = true, P2(j2.nodes), 1) : (g2 || (g2 = false), k2([]), 2);
            }
          }
        }) ?? 0) === 0 && (v2.value = Y2(N2));
      }), u2 && !f2 || g2 && !p2 || w2 && p2 || c && !w2 && !p2)
        return null;
      if (w2)
        for (let [v2, x] of m) {
          let k2 = x.nodes.indexOf(v2);
          k2 !== -1 && x.nodes.splice(k2, 1);
        }
      return s2.nodes;
    }), i.utilities.suggest(t.slice(0, -2), () => {
      let n = [], s2 = [];
      for (let [a, { literals: c, usedSpacingNumber: u2, usedSpacingInteger: f2, themeKeys: g2 }] of [[n, e["--value"]], [s2, e["--modifier"]]]) {
        for (let p2 of c)
          a.push(p2);
        if (u2)
          a.push(...Ze);
        else if (f2)
          for (let p2 of Ze)
            p(p2) && a.push(p2);
        for (let p2 of i.theme.keysInNamespaces(g2))
          a.push(p2.replace(ar, (m, w2, v2) => `${w2}.${v2}`));
      }
      return [{ values: n, modifiers: s2 }];
    });
  } : Ai.test(t) ? (i) => {
    i.utilities.static(t, () => structuredClone(r.nodes));
  } : null;
}
function or(r, t, i) {
  for (let e of t.nodes) {
    if (r.kind === "named" && e.kind === "word" && (e.value[0] === "'" || e.value[0] === '"') && e.value[e.value.length - 1] === e.value[0] && e.value.slice(1, -1) === r.value)
      return { nodes: B2(r.value) };
    if (r.kind === "named" && e.kind === "word" && e.value[0] === "-" && e.value[1] === "-") {
      let n = e.value;
      if (n.endsWith("-*")) {
        n = n.slice(0, -2);
        let s2 = i.theme.resolve(r.value, [n]);
        if (s2)
          return { nodes: B2(s2) };
      } else {
        let s2 = n.split("-*");
        if (s2.length <= 1)
          continue;
        let a = [s2.shift()], c = i.theme.resolveWith(r.value, a, s2);
        if (c) {
          let [, u2 = {}] = c;
          {
            let f2 = u2[s2.pop()];
            if (f2)
              return { nodes: B2(f2) };
          }
        }
      }
    } else if (r.kind === "named" && e.kind === "word") {
      if (!bt.includes(e.value))
        continue;
      let n = e.value === "ratio" && "fraction" in r ? r.fraction : r.value;
      if (!n)
        continue;
      let s2 = pe(n, [e.value]);
      if (s2 === null)
        continue;
      if (s2 === "ratio") {
        let [a, c] = g(n, "/");
        if (!p(a) || !p(c))
          continue;
      } else {
        if (s2 === "number" && !ue(n))
          continue;
        if (s2 === "percentage" && !p(n.slice(0, -1)))
          continue;
      }
      return { nodes: B2(n), ratio: s2 === "ratio" };
    } else if (r.kind === "arbitrary" && e.kind === "word" && e.value[0] === "[" && e.value[e.value.length - 1] === "]") {
      let n = e.value.slice(1, -1);
      if (n === "*")
        return { nodes: B2(r.value) };
      if ("dataType" in r && r.dataType && r.dataType !== n)
        continue;
      if ("dataType" in r && r.dataType)
        return { nodes: B2(r.value) };
      if (pe(r.value, [n]) !== null)
        return { nodes: B2(r.value) };
    }
  }
}
function ue2(r, t, i, e, n = "") {
  let s2 = false, a = Ee(t, (u2) => i == null ? e(u2) : u2.startsWith("current") ? e(Z2(u2, i)) : ((u2.startsWith("var(") || i.startsWith("var(")) && (s2 = true), e(lr(u2, i))));
  function c(u2) {
    return n ? g(u2, ",").map((f2) => n + f2).join(",") : u2;
  }
  return s2 ? [l3(r, c(Ee(t, e))), H2("@supports (color: lab(from red l a b))", [l3(r, c(a))])] : [l3(r, c(a))];
}
function Ye(r, t, i, e, n = "") {
  let s2 = false, a = g(t, ",").map((c) => Ee(c, (u2) => i == null ? e(u2) : u2.startsWith("current") ? e(Z2(u2, i)) : ((u2.startsWith("var(") || i.startsWith("var(")) && (s2 = true), e(lr(u2, i))))).map((c) => `drop-shadow(${c})`).join(" ");
  return s2 ? [l3(r, n + g(t, ",").map((c) => `drop-shadow(${Ee(c, e)})`).join(" ")), H2("@supports (color: lab(from red l a b))", [l3(r, n + a)])] : [l3(r, n + a)];
}
var kt = { "--alpha": $i, "--spacing": Vi, "--theme": Ni, theme: Si };
function $i(r, t, i, ...e) {
  let [n, s2] = g(i, "/").map((a) => a.trim());
  if (!n || !s2)
    throw new Error(`The --alpha(…) function requires a color and an alpha value, e.g.: \`--alpha(${n || "var(--my-color)"} / ${s2 || "50%"})\``);
  if (e.length > 0)
    throw new Error(`The --alpha(…) function only accepts one argument, e.g.: \`--alpha(${n || "var(--my-color)"} / ${s2 || "50%"})\``);
  return Z2(n, s2);
}
function Vi(r, t, i, ...e) {
  if (!i)
    throw new Error("The --spacing(…) function requires an argument, but received none.");
  if (e.length > 0)
    throw new Error(`The --spacing(…) function only accepts a single argument, but received ${e.length + 1}.`);
  let n = r.theme.resolve(null, ["--spacing"]);
  if (!n)
    throw new Error("The --spacing(…) function requires that the `--spacing` theme variable exists, but it was not found.");
  return `calc(${n} * ${i})`;
}
function Ni(r, t, i, ...e) {
  if (!i.startsWith("--"))
    throw new Error("The --theme(…) function can only be used with CSS variables from your theme.");
  let n = false;
  i.endsWith(" inline") && (n = true, i = i.slice(0, -7)), t.kind === "at-rule" && (n = true);
  let s2 = r.resolveThemeValue(i, n);
  if (!s2) {
    if (e.length > 0)
      return e.join(", ");
    throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`);
  }
  if (e.length === 0)
    return s2;
  let a = e.join(", ");
  if (a === "initial")
    return s2;
  if (s2 === "initial")
    return a;
  if (s2.startsWith("var(") || s2.startsWith("theme(") || s2.startsWith("--theme(")) {
    let c = B2(s2);
    return Ei(c, a), Y2(c);
  }
  return s2;
}
function Si(r, t, i, ...e) {
  i = Ti(i);
  let n = r.resolveThemeValue(i);
  if (!n && e.length > 0)
    return e.join(", ");
  if (!n)
    throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);
  return n;
}
var fr = new RegExp(Object.keys(kt).map((r) => `${r}\\(`).join("|"));
function xe(r, t) {
  let i = 0;
  return D2(r, (e) => {
    if (e.kind === "declaration" && e.value && fr.test(e.value)) {
      i |= 8, e.value = cr(e.value, e, t);
      return;
    }
    e.kind === "at-rule" && (e.name === "@media" || e.name === "@custom-media" || e.name === "@container" || e.name === "@supports") && fr.test(e.params) && (i |= 8, e.params = cr(e.params, e, t));
  }), i;
}
function cr(r, t, i) {
  let e = B2(r);
  return ee2(e, (n, { replaceWith: s2 }) => {
    if (n.kind === "function" && n.value in kt) {
      let a = g(Y2(n.nodes).trim(), ",").map((u2) => u2.trim()), c = kt[n.value](i, t, ...a);
      return s2(B2(c));
    }
  }), Y2(e);
}
function Ti(r) {
  if (r[0] !== "'" && r[0] !== '"')
    return r;
  let t = "", i = r[0];
  for (let e = 1; e < r.length - 1; e++) {
    let n = r[e], s2 = r[e + 1];
    n === "\\" && (s2 === i || s2 === "\\") ? (t += s2, e++) : t += n;
  }
  return t;
}
function Ei(r, t) {
  ee2(r, (i) => {
    if (i.kind === "function" && !(i.value !== "var" && i.value !== "theme" && i.value !== "--theme"))
      if (i.nodes.length === 1)
        i.nodes.push({ kind: "word", value: `, ${t}` });
      else {
        let e = i.nodes[i.nodes.length - 1];
        e.kind === "word" && e.value === "initial" && (e.value = t);
      }
  });
}
function Qe(r, t) {
  let i = r.length, e = t.length, n = i < e ? i : e;
  for (let s2 = 0; s2 < n; s2++) {
    let a = r.charCodeAt(s2), c = t.charCodeAt(s2);
    if (a >= 48 && a <= 57 && c >= 48 && c <= 57) {
      let u2 = s2, f2 = s2 + 1, g2 = s2, p2 = s2 + 1;
      for (a = r.charCodeAt(f2); a >= 48 && a <= 57; )
        a = r.charCodeAt(++f2);
      for (c = t.charCodeAt(p2); c >= 48 && c <= 57; )
        c = t.charCodeAt(++p2);
      let m = r.slice(u2, f2), w2 = t.slice(g2, p2), v2 = Number(m) - Number(w2);
      if (v2)
        return v2;
      if (m < w2)
        return -1;
      if (m > w2)
        return 1;
      continue;
    }
    if (a !== c)
      return a - c;
  }
  return r.length - t.length;
}
var Pi = /^\d+\/\d+$/;
function dr(r) {
  let t = new W2((n) => ({ name: n, utility: n, fraction: false, modifiers: [] }));
  for (let n of r.utilities.keys("static")) {
    let s2 = t.get(n);
    s2.fraction = false, s2.modifiers = [];
  }
  for (let n of r.utilities.keys("functional")) {
    let s2 = r.utilities.getCompletions(n);
    for (let a of s2)
      for (let c of a.values) {
        let u2 = c !== null && Pi.test(c), f2 = c === null ? n : `${n}-${c}`, g2 = t.get(f2);
        if (g2.utility = n, g2.fraction || (g2.fraction = u2), g2.modifiers.push(...a.modifiers), a.supportsNegative) {
          let p2 = t.get(`-${f2}`);
          p2.utility = `-${n}`, p2.fraction || (p2.fraction = u2), p2.modifiers.push(...a.modifiers);
        }
      }
  }
  if (t.size === 0)
    return [];
  let i = Array.from(t.values());
  return i.sort((n, s2) => Qe(n.name, s2.name)), Ri(i);
}
function Ri(r) {
  let t = [], i = null, e = /* @__PURE__ */ new Map(), n = new W2(() => []);
  for (let a of r) {
    let { utility: c, fraction: u2 } = a;
    i || (i = { utility: c, items: [] }, e.set(c, i)), c !== i.utility && (t.push(i), i = { utility: c, items: [] }, e.set(c, i)), u2 ? n.get(c).push(a) : i.items.push(a);
  }
  i && t[t.length - 1] !== i && t.push(i);
  for (let [a, c] of n) {
    let u2 = e.get(a);
    u2 && u2.items.push(...c);
  }
  let s2 = [];
  for (let a of t)
    for (let c of a.items)
      s2.push([c.name, { modifiers: c.modifiers }]);
  return s2;
}
function pr(r) {
  let t = [];
  for (let [e, n] of r.variants.entries()) {
    let c = function({ value: u2, modifier: f2 } = {}) {
      let g2 = e;
      u2 && (g2 += s2 ? `-${u2}` : u2), f2 && (g2 += `/${f2}`);
      let p2 = r.parseVariant(g2);
      if (!p2)
        return [];
      let m = M2(".__placeholder__", []);
      if (Ae(m, p2, r.variants) === null)
        return [];
      let w2 = [];
      return Ge(m.nodes, (v2, { path: x }) => {
        if (v2.kind !== "rule" && v2.kind !== "at-rule" || v2.nodes.length > 0)
          return;
        x.sort((b2, S) => {
          let P2 = b2.kind === "at-rule", j2 = S.kind === "at-rule";
          return P2 && !j2 ? -1 : !P2 && j2 ? 1 : 0;
        });
        let k2 = x.flatMap((b2) => b2.kind === "rule" ? b2.selector === "&" ? [] : [b2.selector] : b2.kind === "at-rule" ? [`${b2.name} ${b2.params}`] : []), N2 = "";
        for (let b2 = k2.length - 1; b2 >= 0; b2--)
          N2 = N2 === "" ? k2[b2] : `${k2[b2]} { ${N2} }`;
        w2.push(N2);
      }), w2;
    };
    var i = c;
    if (n.kind === "arbitrary")
      continue;
    let s2 = e !== "@", a = r.variants.getCompletions(e);
    switch (n.kind) {
      case "static": {
        t.push({ name: e, values: a, isArbitrary: false, hasDash: s2, selectors: c });
        break;
      }
      case "functional": {
        t.push({ name: e, values: a, isArbitrary: true, hasDash: s2, selectors: c });
        break;
      }
      case "compound": {
        t.push({ name: e, values: a, isArbitrary: true, hasDash: s2, selectors: c });
        break;
      }
    }
  }
  return t;
}
function mr(r, t) {
  var _a2;
  let { astNodes: i, nodeSorting: e } = de2(Array.from(t), r), n = new Map(t.map((a) => [a, null])), s2 = 0n;
  for (let a of i) {
    let c = (_a2 = e.get(a)) == null ? void 0 : _a2.candidate;
    c && n.set(c, n.get(c) ?? s2++);
  }
  return t.map((a) => [a, n.get(a) ?? null]);
}
var Xe = /^@?[a-zA-Z0-9_-]*$/;
var xt = class {
  constructor() {
    __publicField(this, "compareFns", /* @__PURE__ */ new Map());
    __publicField(this, "variants", /* @__PURE__ */ new Map());
    __publicField(this, "completions", /* @__PURE__ */ new Map());
    __publicField(this, "groupOrder", null);
    __publicField(this, "lastOrder", 0);
  }
  static(t, i, { compounds: e, order: n } = {}) {
    this.set(t, { kind: "static", applyFn: i, compoundsWith: 0, compounds: e ?? 2, order: n });
  }
  fromAst(t, i) {
    let e = [];
    D2(i, (n) => {
      n.kind === "rule" ? e.push(n.selector) : n.kind === "at-rule" && n.name !== "@slot" && e.push(`${n.name} ${n.params}`);
    }), this.static(t, (n) => {
      let s2 = structuredClone(i);
      At(s2, n.nodes), n.nodes = s2;
    }, { compounds: ye2(e) });
  }
  functional(t, i, { compounds: e, order: n } = {}) {
    this.set(t, { kind: "functional", applyFn: i, compoundsWith: 0, compounds: e ?? 2, order: n });
  }
  compound(t, i, e, { compounds: n, order: s2 } = {}) {
    this.set(t, { kind: "compound", applyFn: e, compoundsWith: i, compounds: n ?? 2, order: s2 });
  }
  group(t, i) {
    this.groupOrder = this.nextOrder(), i && this.compareFns.set(this.groupOrder, i), t(), this.groupOrder = null;
  }
  has(t) {
    return this.variants.has(t);
  }
  get(t) {
    return this.variants.get(t);
  }
  kind(t) {
    var _a2;
    return (_a2 = this.variants.get(t)) == null ? void 0 : _a2.kind;
  }
  compoundsWith(t, i) {
    let e = this.variants.get(t), n = typeof i == "string" ? this.variants.get(i) : i.kind === "arbitrary" ? { compounds: ye2([i.selector]) } : this.variants.get(i.root);
    return !(!e || !n || e.kind !== "compound" || n.compounds === 0 || e.compoundsWith === 0 || (e.compoundsWith & n.compounds) === 0);
  }
  suggest(t, i) {
    this.completions.set(t, i);
  }
  getCompletions(t) {
    var _a2;
    return ((_a2 = this.completions.get(t)) == null ? void 0 : _a2()) ?? [];
  }
  compare(t, i) {
    if (t === i)
      return 0;
    if (t === null)
      return -1;
    if (i === null)
      return 1;
    if (t.kind === "arbitrary" && i.kind === "arbitrary")
      return t.selector < i.selector ? -1 : 1;
    if (t.kind === "arbitrary")
      return 1;
    if (i.kind === "arbitrary")
      return -1;
    let e = this.variants.get(t.root).order, n = this.variants.get(i.root).order, s2 = e - n;
    if (s2 !== 0)
      return s2;
    if (t.kind === "compound" && i.kind === "compound") {
      let f2 = this.compare(t.variant, i.variant);
      return f2 !== 0 ? f2 : t.modifier && i.modifier ? t.modifier.value < i.modifier.value ? -1 : 1 : t.modifier ? 1 : i.modifier ? -1 : 0;
    }
    let a = this.compareFns.get(e);
    if (a !== void 0)
      return a(t, i);
    if (t.root !== i.root)
      return t.root < i.root ? -1 : 1;
    let c = t.value, u2 = i.value;
    return c === null ? -1 : u2 === null || c.kind === "arbitrary" && u2.kind !== "arbitrary" ? 1 : c.kind !== "arbitrary" && u2.kind === "arbitrary" || c.value < u2.value ? -1 : 1;
  }
  keys() {
    return this.variants.keys();
  }
  entries() {
    return this.variants.entries();
  }
  set(t, { kind: i, applyFn: e, compounds: n, compoundsWith: s2, order: a }) {
    let c = this.variants.get(t);
    c ? Object.assign(c, { kind: i, applyFn: e, compounds: n }) : (a === void 0 && (this.lastOrder = this.nextOrder(), a = this.lastOrder), this.variants.set(t, { kind: i, applyFn: e, order: a, compoundsWith: s2, compounds: n }));
  }
  nextOrder() {
    return this.groupOrder ?? this.lastOrder + 1;
  }
};
function ye2(r) {
  let t = 0;
  for (let i of r) {
    if (i[0] === "@") {
      if (!i.startsWith("@media") && !i.startsWith("@supports") && !i.startsWith("@container"))
        return 0;
      t |= 1;
      continue;
    }
    if (i.includes("::"))
      return 0;
    t |= 2;
  }
  return t;
}
function hr(r) {
  let t = new xt();
  function i(f2, g2, { compounds: p2 } = {}) {
    p2 = p2 ?? ye2(g2), t.static(f2, (m) => {
      m.nodes = g2.map((w2) => H2(w2, m.nodes));
    }, { compounds: p2 });
  }
  i("*", [":is(& > *)"], { compounds: 0 }), i("**", [":is(& *)"], { compounds: 0 });
  function e(f2, g2) {
    return g2.map((p2) => {
      p2 = p2.trim();
      let m = g(p2, " ");
      return m[0] === "not" ? m.slice(1).join(" ") : f2 === "@container" ? m[0][0] === "(" ? `not ${p2}` : m[1] === "not" ? `${m[0]} ${m.slice(2).join(" ")}` : `${m[0]} not ${m.slice(1).join(" ")}` : `not ${p2}`;
    });
  }
  let n = ["@media", "@supports", "@container"];
  function s2(f2) {
    for (let g2 of n) {
      if (g2 !== f2.name)
        continue;
      let p2 = g(f2.params, ",");
      return p2.length > 1 ? null : (p2 = e(f2.name, p2), z2(f2.name, p2.join(", ")));
    }
    return null;
  }
  function a(f2) {
    return f2.includes("::") ? null : `&:not(${g(f2, ",").map((p2) => (p2 = p2.replaceAll("&", "*"), p2)).join(", ")})`;
  }
  t.compound("not", 3, (f2, g2) => {
    if (g2.variant.kind === "arbitrary" && g2.variant.relative || g2.modifier)
      return null;
    let p2 = false;
    if (D2([f2], (m, { path: w2 }) => {
      if (m.kind !== "rule" && m.kind !== "at-rule")
        return 0;
      if (m.nodes.length > 0)
        return 0;
      let v2 = [], x = [];
      for (let N2 of w2)
        N2.kind === "at-rule" ? v2.push(N2) : N2.kind === "rule" && x.push(N2);
      if (v2.length > 1)
        return 2;
      if (x.length > 1)
        return 2;
      let k2 = [];
      for (let N2 of x) {
        let b2 = a(N2.selector);
        if (!b2)
          return p2 = false, 2;
        k2.push(M2(b2, []));
      }
      for (let N2 of v2) {
        let b2 = s2(N2);
        if (!b2)
          return p2 = false, 2;
        k2.push(b2);
      }
      return Object.assign(f2, M2("&", k2)), p2 = true, 1;
    }), f2.kind === "rule" && f2.selector === "&" && f2.nodes.length === 1 && Object.assign(f2, f2.nodes[0]), !p2)
      return null;
  }), t.suggest("not", () => Array.from(t.keys()).filter((f2) => t.compoundsWith("not", f2))), t.compound("group", 2, (f2, g2) => {
    if (g2.variant.kind === "arbitrary" && g2.variant.relative)
      return null;
    let p2 = g2.modifier ? `:where(.${r.prefix ? `${r.prefix}\\:` : ""}group\\/${g2.modifier.value})` : `:where(.${r.prefix ? `${r.prefix}\\:` : ""}group)`, m = false;
    if (D2([f2], (w2, { path: v2 }) => {
      if (w2.kind !== "rule")
        return 0;
      for (let k2 of v2.slice(0, -1))
        if (k2.kind === "rule")
          return m = false, 2;
      let x = w2.selector.replaceAll("&", p2);
      g(x, ",").length > 1 && (x = `:is(${x})`), w2.selector = `&:is(${x} *)`, m = true;
    }), !m)
      return null;
  }), t.suggest("group", () => Array.from(t.keys()).filter((f2) => t.compoundsWith("group", f2))), t.compound("peer", 2, (f2, g2) => {
    if (g2.variant.kind === "arbitrary" && g2.variant.relative)
      return null;
    let p2 = g2.modifier ? `:where(.${r.prefix ? `${r.prefix}\\:` : ""}peer\\/${g2.modifier.value})` : `:where(.${r.prefix ? `${r.prefix}\\:` : ""}peer)`, m = false;
    if (D2([f2], (w2, { path: v2 }) => {
      if (w2.kind !== "rule")
        return 0;
      for (let k2 of v2.slice(0, -1))
        if (k2.kind === "rule")
          return m = false, 2;
      let x = w2.selector.replaceAll("&", p2);
      g(x, ",").length > 1 && (x = `:is(${x})`), w2.selector = `&:is(${x} ~ *)`, m = true;
    }), !m)
      return null;
  }), t.suggest("peer", () => Array.from(t.keys()).filter((f2) => t.compoundsWith("peer", f2))), i("first-letter", ["&::first-letter"]), i("first-line", ["&::first-line"]), i("marker", ["& *::marker", "&::marker", "& *::-webkit-details-marker", "&::-webkit-details-marker"]), i("selection", ["& *::selection", "&::selection"]), i("file", ["&::file-selector-button"]), i("placeholder", ["&::placeholder"]), i("backdrop", ["&::backdrop"]), i("details-content", ["&::details-content"]);
  {
    let f2 = function() {
      return I2([z2("@property", "--tw-content", [l3("syntax", '"*"'), l3("initial-value", '""'), l3("inherits", "false")])]);
    };
    var c = f2;
    t.static("before", (g2) => {
      g2.nodes = [M2("&::before", [f2(), l3("content", "var(--tw-content)"), ...g2.nodes])];
    }, { compounds: 0 }), t.static("after", (g2) => {
      g2.nodes = [M2("&::after", [f2(), l3("content", "var(--tw-content)"), ...g2.nodes])];
    }, { compounds: 0 });
  }
  i("first", ["&:first-child"]), i("last", ["&:last-child"]), i("only", ["&:only-child"]), i("odd", ["&:nth-child(odd)"]), i("even", ["&:nth-child(even)"]), i("first-of-type", ["&:first-of-type"]), i("last-of-type", ["&:last-of-type"]), i("only-of-type", ["&:only-of-type"]), i("visited", ["&:visited"]), i("target", ["&:target"]), i("open", ["&:is([open], :popover-open, :open)"]), i("default", ["&:default"]), i("checked", ["&:checked"]), i("indeterminate", ["&:indeterminate"]), i("placeholder-shown", ["&:placeholder-shown"]), i("autofill", ["&:autofill"]), i("optional", ["&:optional"]), i("required", ["&:required"]), i("valid", ["&:valid"]), i("invalid", ["&:invalid"]), i("user-valid", ["&:user-valid"]), i("user-invalid", ["&:user-invalid"]), i("in-range", ["&:in-range"]), i("out-of-range", ["&:out-of-range"]), i("read-only", ["&:read-only"]), i("empty", ["&:empty"]), i("focus-within", ["&:focus-within"]), t.static("hover", (f2) => {
    f2.nodes = [M2("&:hover", [z2("@media", "(hover: hover)", f2.nodes)])];
  }), i("focus", ["&:focus"]), i("focus-visible", ["&:focus-visible"]), i("active", ["&:active"]), i("enabled", ["&:enabled"]), i("disabled", ["&:disabled"]), i("inert", ["&:is([inert], [inert] *)"]), t.compound("in", 2, (f2, g2) => {
    if (g2.modifier)
      return null;
    let p2 = false;
    if (D2([f2], (m, { path: w2 }) => {
      if (m.kind !== "rule")
        return 0;
      for (let v2 of w2.slice(0, -1))
        if (v2.kind === "rule")
          return p2 = false, 2;
      m.selector = `:where(${m.selector.replaceAll("&", "*")}) &`, p2 = true;
    }), !p2)
      return null;
  }), t.suggest("in", () => Array.from(t.keys()).filter((f2) => t.compoundsWith("in", f2))), t.compound("has", 2, (f2, g2) => {
    if (g2.modifier)
      return null;
    let p2 = false;
    if (D2([f2], (m, { path: w2 }) => {
      if (m.kind !== "rule")
        return 0;
      for (let v2 of w2.slice(0, -1))
        if (v2.kind === "rule")
          return p2 = false, 2;
      m.selector = `&:has(${m.selector.replaceAll("&", "*")})`, p2 = true;
    }), !p2)
      return null;
  }), t.suggest("has", () => Array.from(t.keys()).filter((f2) => t.compoundsWith("has", f2))), t.functional("aria", (f2, g2) => {
    if (!g2.value || g2.modifier)
      return null;
    g2.value.kind === "arbitrary" ? f2.nodes = [M2(`&[aria-${gr(g2.value.value)}]`, f2.nodes)] : f2.nodes = [M2(`&[aria-${g2.value.value}="true"]`, f2.nodes)];
  }), t.suggest("aria", () => ["busy", "checked", "disabled", "expanded", "hidden", "pressed", "readonly", "required", "selected"]), t.functional("data", (f2, g2) => {
    if (!g2.value || g2.modifier)
      return null;
    f2.nodes = [M2(`&[data-${gr(g2.value.value)}]`, f2.nodes)];
  }), t.functional("nth", (f2, g2) => {
    if (!g2.value || g2.modifier || g2.value.kind === "named" && !p(g2.value.value))
      return null;
    f2.nodes = [M2(`&:nth-child(${g2.value.value})`, f2.nodes)];
  }), t.functional("nth-last", (f2, g2) => {
    if (!g2.value || g2.modifier || g2.value.kind === "named" && !p(g2.value.value))
      return null;
    f2.nodes = [M2(`&:nth-last-child(${g2.value.value})`, f2.nodes)];
  }), t.functional("nth-of-type", (f2, g2) => {
    if (!g2.value || g2.modifier || g2.value.kind === "named" && !p(g2.value.value))
      return null;
    f2.nodes = [M2(`&:nth-of-type(${g2.value.value})`, f2.nodes)];
  }), t.functional("nth-last-of-type", (f2, g2) => {
    if (!g2.value || g2.modifier || g2.value.kind === "named" && !p(g2.value.value))
      return null;
    f2.nodes = [M2(`&:nth-last-of-type(${g2.value.value})`, f2.nodes)];
  }), t.functional("supports", (f2, g2) => {
    if (!g2.value || g2.modifier)
      return null;
    let p2 = g2.value.value;
    if (p2 === null)
      return null;
    if (/^[\w-]*\s*\(/.test(p2)) {
      let m = p2.replace(/\b(and|or|not)\b/g, " $1 ");
      f2.nodes = [z2("@supports", m, f2.nodes)];
      return;
    }
    p2.includes(":") || (p2 = `${p2}: var(--tw)`), (p2[0] !== "(" || p2[p2.length - 1] !== ")") && (p2 = `(${p2})`), f2.nodes = [z2("@supports", p2, f2.nodes)];
  }, { compounds: 1 }), i("motion-safe", ["@media (prefers-reduced-motion: no-preference)"]), i("motion-reduce", ["@media (prefers-reduced-motion: reduce)"]), i("contrast-more", ["@media (prefers-contrast: more)"]), i("contrast-less", ["@media (prefers-contrast: less)"]);
  {
    let f2 = function(g2, p2, m, w2) {
      if (g2 === p2)
        return 0;
      let v2 = w2.get(g2);
      if (v2 === null)
        return m === "asc" ? -1 : 1;
      let x = w2.get(p2);
      return x === null ? m === "asc" ? 1 : -1 : we(v2, x, m);
    };
    var u2 = f2;
    {
      let g2 = r.namespace("--breakpoint"), p2 = new W2((m) => {
        switch (m.kind) {
          case "static":
            return r.resolveValue(m.root, ["--breakpoint"]) ?? null;
          case "functional": {
            if (!m.value || m.modifier)
              return null;
            let w2 = null;
            return m.value.kind === "arbitrary" ? w2 = m.value.value : m.value.kind === "named" && (w2 = r.resolveValue(m.value.value, ["--breakpoint"])), !w2 || w2.includes("var(") ? null : w2;
          }
          case "arbitrary":
          case "compound":
            return null;
        }
      });
      t.group(() => {
        t.functional("max", (m, w2) => {
          if (w2.modifier)
            return null;
          let v2 = p2.get(w2);
          if (v2 === null)
            return null;
          m.nodes = [z2("@media", `(width < ${v2})`, m.nodes)];
        }, { compounds: 1 });
      }, (m, w2) => f2(m, w2, "desc", p2)), t.suggest("max", () => Array.from(g2.keys()).filter((m) => m !== null)), t.group(() => {
        for (let [m, w2] of r.namespace("--breakpoint"))
          m !== null && t.static(m, (v2) => {
            v2.nodes = [z2("@media", `(width >= ${w2})`, v2.nodes)];
          }, { compounds: 1 });
        t.functional("min", (m, w2) => {
          if (w2.modifier)
            return null;
          let v2 = p2.get(w2);
          if (v2 === null)
            return null;
          m.nodes = [z2("@media", `(width >= ${v2})`, m.nodes)];
        }, { compounds: 1 });
      }, (m, w2) => f2(m, w2, "asc", p2)), t.suggest("min", () => Array.from(g2.keys()).filter((m) => m !== null));
    }
    {
      let g2 = r.namespace("--container"), p2 = new W2((m) => {
        switch (m.kind) {
          case "functional": {
            if (m.value === null)
              return null;
            let w2 = null;
            return m.value.kind === "arbitrary" ? w2 = m.value.value : m.value.kind === "named" && (w2 = r.resolveValue(m.value.value, ["--container"])), !w2 || w2.includes("var(") ? null : w2;
          }
          case "static":
          case "arbitrary":
          case "compound":
            return null;
        }
      });
      t.group(() => {
        t.functional("@max", (m, w2) => {
          let v2 = p2.get(w2);
          if (v2 === null)
            return null;
          m.nodes = [z2("@container", w2.modifier ? `${w2.modifier.value} (width < ${v2})` : `(width < ${v2})`, m.nodes)];
        }, { compounds: 1 });
      }, (m, w2) => f2(m, w2, "desc", p2)), t.suggest("@max", () => Array.from(g2.keys()).filter((m) => m !== null)), t.group(() => {
        t.functional("@", (m, w2) => {
          let v2 = p2.get(w2);
          if (v2 === null)
            return null;
          m.nodes = [z2("@container", w2.modifier ? `${w2.modifier.value} (width >= ${v2})` : `(width >= ${v2})`, m.nodes)];
        }, { compounds: 1 }), t.functional("@min", (m, w2) => {
          let v2 = p2.get(w2);
          if (v2 === null)
            return null;
          m.nodes = [z2("@container", w2.modifier ? `${w2.modifier.value} (width >= ${v2})` : `(width >= ${v2})`, m.nodes)];
        }, { compounds: 1 });
      }, (m, w2) => f2(m, w2, "asc", p2)), t.suggest("@min", () => Array.from(g2.keys()).filter((m) => m !== null)), t.suggest("@", () => Array.from(g2.keys()).filter((m) => m !== null));
    }
  }
  return i("portrait", ["@media (orientation: portrait)"]), i("landscape", ["@media (orientation: landscape)"]), i("ltr", ['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']), i("rtl", ['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']), i("dark", ["@media (prefers-color-scheme: dark)"]), i("starting", ["@starting-style"]), i("print", ["@media print"]), i("forced-colors", ["@media (forced-colors: active)"]), i("inverted-colors", ["@media (inverted-colors: inverted)"]), i("pointer-none", ["@media (pointer: none)"]), i("pointer-coarse", ["@media (pointer: coarse)"]), i("pointer-fine", ["@media (pointer: fine)"]), i("any-pointer-none", ["@media (any-pointer: none)"]), i("any-pointer-coarse", ["@media (any-pointer: coarse)"]), i("any-pointer-fine", ["@media (any-pointer: fine)"]), i("noscript", ["@media (scripting: none)"]), t;
}
function gr(r) {
  if (r.includes("=")) {
    let [t, ...i] = g(r, "="), e = i.join("=").trim();
    if (e[0] === "'" || e[0] === '"')
      return r;
    if (e.length > 1) {
      let n = e[e.length - 1];
      if (e[e.length - 2] === " " && (n === "i" || n === "I" || n === "s" || n === "S"))
        return `${t}="${e.slice(0, -2)}" ${n}`;
    }
    return `${t}="${e}"`;
  }
  return r;
}
function At(r, t) {
  D2(r, (i, { replaceWith: e }) => {
    if (i.kind === "at-rule" && i.name === "@slot")
      e(t);
    else if (i.kind === "at-rule" && (i.name === "@keyframes" || i.name === "@property"))
      return Object.assign(i, I2([z2(i.name, i.params, i.nodes)])), 1;
  });
}
function vr(r) {
  let t = sr(r), i = hr(r), e = new W2((u2) => er(u2, c)), n = new W2((u2) => Array.from(Xt(u2, c))), s2 = new W2((u2) => {
    let f2 = wr(u2, c);
    try {
      xe(f2.map(({ node: g2 }) => g2), c);
    } catch {
      return [];
    }
    return f2;
  }), a = new W2((u2) => {
    for (let f2 of qe(u2))
      r.markUsedVariable(f2);
  }), c = { theme: r, utilities: t, variants: i, invalidCandidates: /* @__PURE__ */ new Set(), important: false, candidatesToCss(u2) {
    let f2 = [];
    for (let g2 of u2) {
      let p2 = false, { astNodes: m } = de2([g2], this, { onInvalidCandidate() {
        p2 = true;
      } });
      m = ve(m, c, 0), m.length === 0 || p2 ? f2.push(null) : f2.push(ne2(m));
    }
    return f2;
  }, getClassOrder(u2) {
    return mr(this, u2);
  }, getClassList() {
    return dr(this);
  }, getVariants() {
    return pr(this);
  }, parseCandidate(u2) {
    return n.get(u2);
  }, parseVariant(u2) {
    return e.get(u2);
  }, compileAstNodes(u2) {
    return s2.get(u2);
  }, printCandidate(u2) {
    return rr(c, u2);
  }, printVariant(u2) {
    return He(u2);
  }, getVariantOrder() {
    let u2 = Array.from(e.values());
    u2.sort((m, w2) => this.variants.compare(m, w2));
    let f2 = /* @__PURE__ */ new Map(), g2, p2 = 0;
    for (let m of u2)
      m !== null && (g2 !== void 0 && this.variants.compare(g2, m) !== 0 && p2++, f2.set(m, p2), g2 = m);
    return f2;
  }, resolveThemeValue(u2, f2 = true) {
    let g2 = u2.lastIndexOf("/"), p2 = null;
    g2 !== -1 && (p2 = u2.slice(g2 + 1).trim(), u2 = u2.slice(0, g2).trim());
    let m = r.resolve(null, [u2], f2 ? 1 : 0) ?? void 0;
    return p2 && m ? Z2(m, p2) : m;
  }, trackUsedVariables(u2) {
    a.get(u2);
  } };
  return c;
}
var Ct = ["container-type", "pointer-events", "visibility", "position", "inset", "inset-inline", "inset-block", "inset-inline-start", "inset-inline-end", "top", "right", "bottom", "left", "isolation", "z-index", "order", "grid-column", "grid-column-start", "grid-column-end", "grid-row", "grid-row-start", "grid-row-end", "float", "clear", "--tw-container-component", "margin", "margin-inline", "margin-block", "margin-inline-start", "margin-inline-end", "margin-top", "margin-right", "margin-bottom", "margin-left", "box-sizing", "display", "field-sizing", "aspect-ratio", "height", "max-height", "min-height", "width", "max-width", "min-width", "flex", "flex-shrink", "flex-grow", "flex-basis", "table-layout", "caption-side", "border-collapse", "border-spacing", "transform-origin", "translate", "--tw-translate-x", "--tw-translate-y", "--tw-translate-z", "scale", "--tw-scale-x", "--tw-scale-y", "--tw-scale-z", "rotate", "--tw-rotate-x", "--tw-rotate-y", "--tw-rotate-z", "--tw-skew-x", "--tw-skew-y", "transform", "animation", "cursor", "touch-action", "--tw-pan-x", "--tw-pan-y", "--tw-pinch-zoom", "resize", "scroll-snap-type", "--tw-scroll-snap-strictness", "scroll-snap-align", "scroll-snap-stop", "scroll-margin", "scroll-margin-inline", "scroll-margin-block", "scroll-margin-inline-start", "scroll-margin-inline-end", "scroll-margin-top", "scroll-margin-right", "scroll-margin-bottom", "scroll-margin-left", "scroll-padding", "scroll-padding-inline", "scroll-padding-block", "scroll-padding-inline-start", "scroll-padding-inline-end", "scroll-padding-top", "scroll-padding-right", "scroll-padding-bottom", "scroll-padding-left", "list-style-position", "list-style-type", "list-style-image", "appearance", "columns", "break-before", "break-inside", "break-after", "grid-auto-columns", "grid-auto-flow", "grid-auto-rows", "grid-template-columns", "grid-template-rows", "flex-direction", "flex-wrap", "place-content", "place-items", "align-content", "align-items", "justify-content", "justify-items", "gap", "column-gap", "row-gap", "--tw-space-x-reverse", "--tw-space-y-reverse", "divide-x-width", "divide-y-width", "--tw-divide-y-reverse", "divide-style", "divide-color", "place-self", "align-self", "justify-self", "overflow", "overflow-x", "overflow-y", "overscroll-behavior", "overscroll-behavior-x", "overscroll-behavior-y", "scroll-behavior", "border-radius", "border-start-radius", "border-end-radius", "border-top-radius", "border-right-radius", "border-bottom-radius", "border-left-radius", "border-start-start-radius", "border-start-end-radius", "border-end-end-radius", "border-end-start-radius", "border-top-left-radius", "border-top-right-radius", "border-bottom-right-radius", "border-bottom-left-radius", "border-width", "border-inline-width", "border-block-width", "border-inline-start-width", "border-inline-end-width", "border-top-width", "border-right-width", "border-bottom-width", "border-left-width", "border-style", "border-inline-style", "border-block-style", "border-inline-start-style", "border-inline-end-style", "border-top-style", "border-right-style", "border-bottom-style", "border-left-style", "border-color", "border-inline-color", "border-block-color", "border-inline-start-color", "border-inline-end-color", "border-top-color", "border-right-color", "border-bottom-color", "border-left-color", "background-color", "background-image", "--tw-gradient-position", "--tw-gradient-stops", "--tw-gradient-via-stops", "--tw-gradient-from", "--tw-gradient-from-position", "--tw-gradient-via", "--tw-gradient-via-position", "--tw-gradient-to", "--tw-gradient-to-position", "mask-image", "--tw-mask-top", "--tw-mask-top-from-color", "--tw-mask-top-from-position", "--tw-mask-top-to-color", "--tw-mask-top-to-position", "--tw-mask-right", "--tw-mask-right-from-color", "--tw-mask-right-from-position", "--tw-mask-right-to-color", "--tw-mask-right-to-position", "--tw-mask-bottom", "--tw-mask-bottom-from-color", "--tw-mask-bottom-from-position", "--tw-mask-bottom-to-color", "--tw-mask-bottom-to-position", "--tw-mask-left", "--tw-mask-left-from-color", "--tw-mask-left-from-position", "--tw-mask-left-to-color", "--tw-mask-left-to-position", "--tw-mask-linear", "--tw-mask-linear-position", "--tw-mask-linear-from-color", "--tw-mask-linear-from-position", "--tw-mask-linear-to-color", "--tw-mask-linear-to-position", "--tw-mask-radial", "--tw-mask-radial-shape", "--tw-mask-radial-size", "--tw-mask-radial-position", "--tw-mask-radial-from-color", "--tw-mask-radial-from-position", "--tw-mask-radial-to-color", "--tw-mask-radial-to-position", "--tw-mask-conic", "--tw-mask-conic-position", "--tw-mask-conic-from-color", "--tw-mask-conic-from-position", "--tw-mask-conic-to-color", "--tw-mask-conic-to-position", "box-decoration-break", "background-size", "background-attachment", "background-clip", "background-position", "background-repeat", "background-origin", "mask-composite", "mask-mode", "mask-type", "mask-size", "mask-clip", "mask-position", "mask-repeat", "mask-origin", "fill", "stroke", "stroke-width", "object-fit", "object-position", "padding", "padding-inline", "padding-block", "padding-inline-start", "padding-inline-end", "padding-top", "padding-right", "padding-bottom", "padding-left", "text-align", "text-indent", "vertical-align", "font-family", "font-size", "line-height", "font-weight", "letter-spacing", "text-wrap", "overflow-wrap", "word-break", "text-overflow", "hyphens", "white-space", "color", "text-transform", "font-style", "font-stretch", "font-variant-numeric", "text-decoration-line", "text-decoration-color", "text-decoration-style", "text-decoration-thickness", "text-underline-offset", "-webkit-font-smoothing", "placeholder-color", "caret-color", "accent-color", "color-scheme", "opacity", "background-blend-mode", "mix-blend-mode", "box-shadow", "--tw-shadow", "--tw-shadow-color", "--tw-ring-shadow", "--tw-ring-color", "--tw-inset-shadow", "--tw-inset-shadow-color", "--tw-inset-ring-shadow", "--tw-inset-ring-color", "--tw-ring-offset-width", "--tw-ring-offset-color", "outline", "outline-width", "outline-offset", "outline-color", "--tw-blur", "--tw-brightness", "--tw-contrast", "--tw-drop-shadow", "--tw-grayscale", "--tw-hue-rotate", "--tw-invert", "--tw-saturate", "--tw-sepia", "filter", "--tw-backdrop-blur", "--tw-backdrop-brightness", "--tw-backdrop-contrast", "--tw-backdrop-grayscale", "--tw-backdrop-hue-rotate", "--tw-backdrop-invert", "--tw-backdrop-opacity", "--tw-backdrop-saturate", "--tw-backdrop-sepia", "backdrop-filter", "transition-property", "transition-behavior", "transition-delay", "transition-duration", "transition-timing-function", "will-change", "contain", "content", "forced-color-adjust"];
function de2(r, t, { onInvalidCandidate: i } = {}) {
  let e = /* @__PURE__ */ new Map(), n = [], s2 = /* @__PURE__ */ new Map();
  for (let c of r) {
    if (t.invalidCandidates.has(c)) {
      i == null ? void 0 : i(c);
      continue;
    }
    let u2 = t.parseCandidate(c);
    if (u2.length === 0) {
      i == null ? void 0 : i(c);
      continue;
    }
    s2.set(c, u2);
  }
  let a = t.getVariantOrder();
  for (let [c, u2] of s2) {
    let f2 = false;
    for (let g2 of u2) {
      let p2 = t.compileAstNodes(g2);
      if (p2.length !== 0) {
        f2 = true;
        for (let { node: m, propertySort: w2 } of p2) {
          let v2 = 0n;
          for (let x of g2.variants)
            v2 |= 1n << BigInt(a.get(x));
          e.set(m, { properties: w2, variants: v2, candidate: c }), n.push(m);
        }
      }
    }
    f2 || (i == null ? void 0 : i(c));
  }
  return n.sort((c, u2) => {
    let f2 = e.get(c), g2 = e.get(u2);
    if (f2.variants - g2.variants !== 0n)
      return Number(f2.variants - g2.variants);
    let p2 = 0;
    for (; p2 < f2.properties.order.length && p2 < g2.properties.order.length && f2.properties.order[p2] === g2.properties.order[p2]; )
      p2 += 1;
    return (f2.properties.order[p2] ?? 1 / 0) - (g2.properties.order[p2] ?? 1 / 0) || g2.properties.count - f2.properties.count || Qe(f2.candidate, g2.candidate);
  }), { astNodes: n, nodeSorting: e };
}
function wr(r, t) {
  let i = Oi(r, t);
  if (i.length === 0)
    return [];
  let e = [], n = `.${fe(r.raw)}`;
  for (let s2 of i) {
    let a = Ki(s2);
    (r.important || t.important) && br(s2);
    let c = { kind: "rule", selector: n, nodes: s2 };
    for (let u2 of r.variants)
      if (Ae(c, u2, t.variants) === null)
        return [];
    e.push({ node: c, propertySort: a });
  }
  return e;
}
function Ae(r, t, i, e = 0) {
  if (t.kind === "arbitrary") {
    if (t.relative && e === 0)
      return null;
    r.nodes = [H2(t.selector, r.nodes)];
    return;
  }
  let { applyFn: n } = i.get(t.root);
  if (t.kind === "compound") {
    let a = z2("@slot");
    if (Ae(a, t.variant, i, e + 1) === null || t.root === "not" && a.nodes.length > 1)
      return null;
    for (let u2 of a.nodes)
      if (u2.kind !== "rule" && u2.kind !== "at-rule" || n(u2, t) === null)
        return null;
    D2(a.nodes, (u2) => {
      if ((u2.kind === "rule" || u2.kind === "at-rule") && u2.nodes.length <= 0)
        return u2.nodes = r.nodes, 1;
    }), r.nodes = a.nodes;
    return;
  }
  if (n(r, t) === null)
    return null;
}
function yr(r) {
  var _a2;
  let t = ((_a2 = r.options) == null ? void 0 : _a2.types) ?? [];
  return t.length > 1 && t.includes("any");
}
function Oi(r, t) {
  if (r.kind === "arbitrary") {
    let a = r.value;
    return r.modifier && (a = Q2(a, r.modifier, t.theme)), a === null ? [] : [[l3(r.property, a)]];
  }
  let i = t.utilities.get(r.root) ?? [], e = [], n = i.filter((a) => !yr(a));
  for (let a of n) {
    if (a.kind !== r.kind)
      continue;
    let c = a.compileFn(r);
    if (c !== void 0) {
      if (c === null)
        return e;
      e.push(c);
    }
  }
  if (e.length > 0)
    return e;
  let s2 = i.filter((a) => yr(a));
  for (let a of s2) {
    if (a.kind !== r.kind)
      continue;
    let c = a.compileFn(r);
    if (c !== void 0) {
      if (c === null)
        return e;
      e.push(c);
    }
  }
  return e;
}
function br(r) {
  for (let t of r)
    t.kind !== "at-root" && (t.kind === "declaration" ? t.important = true : (t.kind === "rule" || t.kind === "at-rule") && br(t.nodes));
}
function Ki(r) {
  let t = /* @__PURE__ */ new Set(), i = 0, e = r.slice(), n = false;
  for (; e.length > 0; ) {
    let s2 = e.shift();
    if (s2.kind === "declaration") {
      if (s2.value === void 0 || (i++, n))
        continue;
      if (s2.property === "--tw-sort") {
        let c = Ct.indexOf(s2.value ?? "");
        if (c !== -1) {
          t.add(c), n = true;
          continue;
        }
      }
      let a = Ct.indexOf(s2.property);
      a !== -1 && t.add(a);
    } else if (s2.kind === "rule" || s2.kind === "at-rule")
      for (let a of s2.nodes)
        e.push(a);
  }
  return { order: Array.from(t).sort((s2, a) => s2 - a), count: i };
}
function Oe(r, t) {
  let i = 0, e = H2("&", r), n = /* @__PURE__ */ new Set(), s2 = new W2(() => /* @__PURE__ */ new Set()), a = new W2(() => /* @__PURE__ */ new Set());
  D2([e], (p2, { parent: m, path: w2 }) => {
    if (p2.kind === "at-rule") {
      if (p2.name === "@keyframes")
        return D2(p2.nodes, (v2) => {
          if (v2.kind === "at-rule" && v2.name === "@apply")
            throw new Error("You cannot use `@apply` inside `@keyframes`.");
        }), 1;
      if (p2.name === "@utility") {
        let v2 = p2.params.replace(/-\*$/, "");
        a.get(v2).add(p2), D2(p2.nodes, (x) => {
          if (!(x.kind !== "at-rule" || x.name !== "@apply")) {
            n.add(p2);
            for (let k2 of kr(x, t))
              s2.get(p2).add(k2);
          }
        });
        return;
      }
      if (p2.name === "@apply") {
        if (m === null)
          return;
        i |= 1, n.add(m);
        for (let v2 of kr(p2, t))
          for (let x of w2)
            x !== p2 && n.has(x) && s2.get(x).add(v2);
      }
    }
  });
  let c = /* @__PURE__ */ new Set(), u2 = [], f2 = /* @__PURE__ */ new Set();
  function g2(p2, m = []) {
    if (!c.has(p2)) {
      if (f2.has(p2)) {
        let w2 = m[(m.indexOf(p2) + 1) % m.length];
        throw p2.kind === "at-rule" && p2.name === "@utility" && w2.kind === "at-rule" && w2.name === "@utility" && D2(p2.nodes, (v2) => {
          if (v2.kind !== "at-rule" || v2.name !== "@apply")
            return;
          let x = v2.params.split(/\s+/g);
          for (let k2 of x)
            for (let N2 of t.parseCandidate(k2))
              switch (N2.kind) {
                case "arbitrary":
                  break;
                case "static":
                case "functional":
                  if (w2.params.replace(/-\*$/, "") === N2.root)
                    throw new Error(`You cannot \`@apply\` the \`${k2}\` utility here because it creates a circular dependency.`);
                  break;
                default:
              }
        }), new Error(`Circular dependency detected:

${ne2([p2])}
Relies on:

${ne2([w2])}`);
      }
      f2.add(p2);
      for (let w2 of s2.get(p2))
        for (let v2 of a.get(w2))
          m.push(p2), g2(v2, m), m.pop();
      c.add(p2), f2.delete(p2), u2.push(p2);
    }
  }
  for (let p2 of n)
    g2(p2);
  for (let p2 of u2)
    "nodes" in p2 && D2(p2.nodes, (m, { replaceWith: w2 }) => {
      if (m.kind !== "at-rule" || m.name !== "@apply")
        return;
      let v2 = m.params.split(/(\s+)/g), x = {}, k2 = 0;
      for (let [N2, b2] of v2.entries())
        N2 % 2 === 0 && (x[b2] = k2), k2 += b2.length;
      {
        let N2 = Object.keys(x), b2 = de2(N2, t, { onInvalidCandidate: (K2) => {
          throw new Error(`Cannot apply unknown utility class: ${K2}`);
        } }), S = m.src, P2 = b2.astNodes.map((K2) => {
          var _a2;
          let F2 = (_a2 = b2.nodeSorting.get(K2)) == null ? void 0 : _a2.candidate, O2 = F2 ? x[F2] : void 0;
          if (K2 = structuredClone(K2), !S || !F2 || O2 === void 0)
            return D2([K2], (L2) => {
              L2.src = S;
            }), K2;
          let G2 = [S[0], S[1], S[2]];
          return G2[1] += 7 + O2, G2[2] = G2[1] + F2.length, D2([K2], (L2) => {
            L2.src = G2;
          }), K2;
        }), j2 = [];
        for (let K2 of P2)
          if (K2.kind === "rule")
            for (let F2 of K2.nodes)
              j2.push(F2);
          else
            j2.push(K2);
        w2(j2);
      }
    });
  return i;
}
function* kr(r, t) {
  for (let i of r.params.split(/\s+/g))
    for (let e of t.parseCandidate(i))
      switch (e.kind) {
        case "arbitrary":
          break;
        case "static":
        case "functional":
          yield e.root;
          break;
        default:
      }
}
async function $t(r, t, i, e = 0, n = false) {
  let s2 = 0, a = [];
  return D2(r, (c, { replaceWith: u2 }) => {
    if (c.kind === "at-rule" && (c.name === "@import" || c.name === "@reference")) {
      let f2 = _i(B2(c.params));
      if (f2 === null)
        return;
      c.name === "@reference" && (f2.media = "reference"), s2 |= 2;
      let { uri: g2, layer: p2, media: m, supports: w2 } = f2;
      if (g2.startsWith("data:") || g2.startsWith("http://") || g2.startsWith("https://"))
        return;
      let v2 = le({}, []);
      return a.push((async () => {
        if (e > 100)
          throw new Error(`Exceeded maximum recursion depth while resolving \`${g2}\` in \`${t}\`)`);
        let x = await i(g2, t), k2 = me(x.content, { from: n ? x.path : void 0 });
        await $t(k2, x.base, i, e + 1, n), v2.nodes = ji(c, [le({ base: x.base }, k2)], p2, m, w2);
      })()), u2(v2), 1;
    }
  }), a.length > 0 && await Promise.all(a), s2;
}
function _i(r) {
  let t, i = null, e = null, n = null;
  for (let s2 = 0; s2 < r.length; s2++) {
    let a = r[s2];
    if (a.kind !== "separator") {
      if (a.kind === "word" && !t) {
        if (!a.value || a.value[0] !== '"' && a.value[0] !== "'")
          return null;
        t = a.value.slice(1, -1);
        continue;
      }
      if (a.kind === "function" && a.value.toLowerCase() === "url" || !t)
        return null;
      if ((a.kind === "word" || a.kind === "function") && a.value.toLowerCase() === "layer") {
        if (i)
          return null;
        if (n)
          throw new Error("`layer(…)` in an `@import` should come before any other functions or conditions");
        "nodes" in a ? i = Y2(a.nodes) : i = "";
        continue;
      }
      if (a.kind === "function" && a.value.toLowerCase() === "supports") {
        if (n)
          return null;
        n = Y2(a.nodes);
        continue;
      }
      e = Y2(r.slice(s2));
      break;
    }
  }
  return t ? { uri: t, layer: i, media: e, supports: n } : null;
}
function ji(r, t, i, e, n) {
  let s2 = t;
  if (i !== null) {
    let a = z2("@layer", i, s2);
    a.src = r.src, s2 = [a];
  }
  if (e !== null) {
    let a = z2("@media", e, s2);
    a.src = r.src, s2 = [a];
  }
  if (n !== null) {
    let a = z2("@supports", n[0] === "(" ? n : `(${n})`, s2);
    a.src = r.src, s2 = [a];
  }
  return s2;
}
function Ce(r, t = null) {
  return Array.isArray(r) && r.length === 2 && typeof r[1] == "object" && typeof r[1] !== null ? t ? r[1][t] ?? null : r[0] : Array.isArray(r) && t === null ? r.join(", ") : typeof r == "string" && t === null ? r : null;
}
function xr(r, { theme: t }, i) {
  for (let e of i) {
    let n = et([e]);
    n && r.theme.clearNamespace(`--${n}`, 4);
  }
  for (let [e, n] of Di(t)) {
    if (typeof n != "string" && typeof n != "number")
      continue;
    if (typeof n == "string" && (n = n.replace(/<alpha-value>/g, "1")), e[0] === "opacity" && (typeof n == "number" || typeof n == "string")) {
      let a = typeof n == "string" ? parseFloat(n) : n;
      a >= 0 && a <= 1 && (n = a * 100 + "%");
    }
    let s2 = et(e);
    s2 && r.theme.add(`--${s2}`, "" + n, 7);
  }
  if (Object.hasOwn(t, "fontFamily")) {
    let e = 5;
    {
      let n = Ce(t.fontFamily.sans);
      n && r.theme.hasDefault("--font-sans") && (r.theme.add("--default-font-family", n, e), r.theme.add("--default-font-feature-settings", Ce(t.fontFamily.sans, "fontFeatureSettings") ?? "normal", e), r.theme.add("--default-font-variation-settings", Ce(t.fontFamily.sans, "fontVariationSettings") ?? "normal", e));
    }
    {
      let n = Ce(t.fontFamily.mono);
      n && r.theme.hasDefault("--font-mono") && (r.theme.add("--default-mono-font-family", n, e), r.theme.add("--default-mono-font-feature-settings", Ce(t.fontFamily.mono, "fontFeatureSettings") ?? "normal", e), r.theme.add("--default-mono-font-variation-settings", Ce(t.fontFamily.mono, "fontVariationSettings") ?? "normal", e));
    }
  }
  return t;
}
function Di(r) {
  let t = [];
  return Ar(r, [], (i, e) => {
    if (Ii(i))
      return t.push([e, i]), 1;
    if (Fi(i)) {
      t.push([e, i[0]]);
      for (let n of Reflect.ownKeys(i[1]))
        t.push([[...e, `-${n}`], i[1][n]]);
      return 1;
    }
    if (Array.isArray(i) && i.every((n) => typeof n == "string"))
      return e[0] === "fontSize" ? (t.push([e, i[0]]), i.length >= 2 && t.push([[...e, "-line-height"], i[1]])) : t.push([e, i.join(", ")]), 1;
  }), t;
}
var Ui = /^[a-zA-Z0-9-_%/\.]+$/;
function et(r) {
  if (r[0] === "container")
    return null;
  r = structuredClone(r), r[0] === "animation" && (r[0] = "animate"), r[0] === "aspectRatio" && (r[0] = "aspect"), r[0] === "borderRadius" && (r[0] = "radius"), r[0] === "boxShadow" && (r[0] = "shadow"), r[0] === "colors" && (r[0] = "color"), r[0] === "containers" && (r[0] = "container"), r[0] === "fontFamily" && (r[0] = "font"), r[0] === "fontSize" && (r[0] = "text"), r[0] === "letterSpacing" && (r[0] = "tracking"), r[0] === "lineHeight" && (r[0] = "leading"), r[0] === "maxWidth" && (r[0] = "container"), r[0] === "screens" && (r[0] = "breakpoint"), r[0] === "transitionTimingFunction" && (r[0] = "ease");
  for (let t of r)
    if (!Ui.test(t))
      return null;
  return r.map((t, i, e) => t === "1" && i !== e.length - 1 ? "" : t).map((t) => t.replaceAll(".", "_").replace(/([a-z])([A-Z])/g, (i, e, n) => `${e}-${n.toLowerCase()}`)).filter((t, i) => t !== "DEFAULT" || i !== r.length - 1).join("-");
}
function Ii(r) {
  return typeof r == "number" || typeof r == "string";
}
function Fi(r) {
  if (!Array.isArray(r) || r.length !== 2 || typeof r[0] != "string" && typeof r[0] != "number" || r[1] === void 0 || r[1] === null || typeof r[1] != "object")
    return false;
  for (let t of Reflect.ownKeys(r[1]))
    if (typeof t != "string" || typeof r[1][t] != "string" && typeof r[1][t] != "number")
      return false;
  return true;
}
function Ar(r, t = [], i) {
  for (let e of Reflect.ownKeys(r)) {
    let n = r[e];
    if (n == null)
      continue;
    let s2 = [...t, e], a = i(n, s2) ?? 0;
    if (a !== 1) {
      if (a === 2)
        return 2;
      if (!(!Array.isArray(n) && typeof n != "object") && Ar(n, s2, i) === 2)
        return 2;
    }
  }
}
function tt(r) {
  let t = [];
  for (let i of g(r, ".")) {
    if (!i.includes("[")) {
      t.push(i);
      continue;
    }
    let e = 0;
    for (; ; ) {
      let n = i.indexOf("[", e), s2 = i.indexOf("]", n);
      if (n === -1 || s2 === -1)
        break;
      n > e && t.push(i.slice(e, n)), t.push(i.slice(n + 1, s2)), e = s2 + 1;
    }
    e <= i.length - 1 && t.push(i.slice(e));
  }
  return t;
}
function $e(r) {
  if (Object.prototype.toString.call(r) !== "[object Object]")
    return false;
  let t = Object.getPrototypeOf(r);
  return t === null || Object.getPrototypeOf(t) === null;
}
function Ke(r, t, i, e = []) {
  for (let n of t)
    if (n != null)
      for (let s2 of Reflect.ownKeys(n)) {
        e.push(s2);
        let a = i(r[s2], n[s2], e);
        a !== void 0 ? r[s2] = a : !$e(r[s2]) || !$e(n[s2]) ? r[s2] = n[s2] : r[s2] = Ke({}, [r[s2], n[s2]], i, e), e.pop();
      }
  return r;
}
function rt(r, t, i) {
  return function(n, s2) {
    let a = n.lastIndexOf("/"), c = null;
    a !== -1 && (c = n.slice(a + 1).trim(), n = n.slice(0, a).trim());
    let u2 = (() => {
      var _a2;
      let f2 = tt(n), [g2, p2] = Li(r.theme, f2), m = i(Cr(t() ?? {}, f2) ?? null);
      if (typeof m == "string" && (m = m.replace("<alpha-value>", "1")), typeof g2 != "object")
        return typeof p2 != "object" && p2 & 4 ? m ?? g2 : g2;
      if (m !== null && typeof m == "object" && !Array.isArray(m)) {
        let w2 = Ke({}, [m], (v2, x) => x);
        if (g2 === null && Object.hasOwn(m, "__CSS_VALUES__")) {
          let v2 = {};
          for (let x in m.__CSS_VALUES__)
            v2[x] = m[x], delete w2[x];
          g2 = v2;
        }
        for (let v2 in g2)
          v2 !== "__CSS_VALUES__" && (((_a2 = m == null ? void 0 : m.__CSS_VALUES__) == null ? void 0 : _a2[v2]) & 4 && Cr(w2, v2.split("-")) !== void 0 || (w2[ge2(v2)] = g2[v2]));
        return w2;
      }
      if (Array.isArray(g2) && Array.isArray(p2) && Array.isArray(m)) {
        let w2 = g2[0], v2 = g2[1];
        p2[0] & 4 && (w2 = m[0] ?? w2);
        for (let x of Object.keys(v2))
          p2[1][x] & 4 && (v2[x] = m[1][x] ?? v2[x]);
        return [w2, v2];
      }
      return g2 ?? m;
    })();
    return c && typeof u2 == "string" && (u2 = Z2(u2, c)), u2 ?? s2;
  };
}
function Li(r, t) {
  if (t.length === 1 && t[0].startsWith("--"))
    return [r.get([t[0]]), r.getOptions(t[0])];
  let i = et(t), e = /* @__PURE__ */ new Map(), n = new W2(() => /* @__PURE__ */ new Map()), s2 = r.namespace(`--${i}`);
  if (s2.size === 0)
    return [null, 0];
  let a = /* @__PURE__ */ new Map();
  for (let [g2, p2] of s2) {
    if (!g2 || !g2.includes("--")) {
      e.set(g2, p2), a.set(g2, r.getOptions(g2 ? `--${i}-${g2}` : `--${i}`));
      continue;
    }
    let m = g2.indexOf("--"), w2 = g2.slice(0, m), v2 = g2.slice(m + 2);
    v2 = v2.replace(/-([a-z])/g, (x, k2) => k2.toUpperCase()), n.get(w2 === "" ? null : w2).set(v2, [p2, r.getOptions(`--${i}${g2}`)]);
  }
  let c = r.getOptions(`--${i}`);
  for (let [g2, p2] of n) {
    let m = e.get(g2);
    if (typeof m != "string")
      continue;
    let w2 = {}, v2 = {};
    for (let [x, [k2, N2]] of p2)
      w2[x] = k2, v2[x] = N2;
    e.set(g2, [m, w2]), a.set(g2, [c, v2]);
  }
  let u2 = {}, f2 = {};
  for (let [g2, p2] of e)
    $r(u2, [g2 ?? "DEFAULT"], p2);
  for (let [g2, p2] of a)
    $r(f2, [g2 ?? "DEFAULT"], p2);
  return t[t.length - 1] === "DEFAULT" ? [(u2 == null ? void 0 : u2.DEFAULT) ?? null, f2.DEFAULT ?? 0] : "DEFAULT" in u2 && Object.keys(u2).length === 1 ? [u2.DEFAULT, f2.DEFAULT ?? 0] : (u2.__CSS_VALUES__ = f2, [u2, f2]);
}
function Cr(r, t) {
  for (let i = 0; i < t.length; ++i) {
    let e = t[i];
    if ((r == null ? void 0 : r[e]) === void 0) {
      if (t[i + 1] === void 0)
        return;
      t[i + 1] = `${e}-${t[i + 1]}`;
      continue;
    }
    r = r[e];
  }
  return r;
}
function $r(r, t, i) {
  for (let e of t.slice(0, -1))
    r[e] === void 0 && (r[e] = {}), r = r[e];
  r[t[t.length - 1]] = i;
}
function zi(r) {
  return { kind: "combinator", value: r };
}
function Mi(r, t) {
  return { kind: "function", value: r, nodes: t };
}
function _e2(r) {
  return { kind: "selector", value: r };
}
function Wi(r) {
  return { kind: "separator", value: r };
}
function Bi(r) {
  return { kind: "value", value: r };
}
function je(r, t, i = null) {
  for (let e = 0; e < r.length; e++) {
    let n = r[e], s2 = false, a = 0, c = t(n, { parent: i, replaceWith(u2) {
      s2 || (s2 = true, Array.isArray(u2) ? u2.length === 0 ? (r.splice(e, 1), a = 0) : u2.length === 1 ? (r[e] = u2[0], a = 1) : (r.splice(e, 1, ...u2), a = u2.length) : (r[e] = u2, a = 1));
    } }) ?? 0;
    if (s2) {
      c === 0 ? e-- : e += a - 1;
      continue;
    }
    if (c === 2)
      return 2;
    if (c !== 1 && n.kind === "function" && je(n.nodes, t, n) === 2)
      return 2;
  }
}
function De(r) {
  let t = "";
  for (let i of r)
    switch (i.kind) {
      case "combinator":
      case "selector":
      case "separator":
      case "value": {
        t += i.value;
        break;
      }
      case "function":
        t += i.value + "(" + De(i.nodes) + ")";
    }
  return t;
}
var Vr = 92;
var qi = 93;
var Nr = 41;
var Gi = 58;
var Sr = 44;
var Ji = 34;
var Hi = 46;
var Tr = 62;
var Er = 10;
var Yi = 35;
var Pr = 91;
var Rr = 40;
var Or = 43;
var Zi = 39;
var Kr = 32;
var _r2 = 9;
var jr = 126;
function it(r) {
  r = r.replaceAll(`\r
`, `
`);
  let t = [], i = [], e = null, n = "", s2;
  for (let a = 0; a < r.length; a++) {
    let c = r.charCodeAt(a);
    switch (c) {
      case Sr:
      case Tr:
      case Er:
      case Kr:
      case Or:
      case _r2:
      case jr: {
        if (n.length > 0) {
          let m = _e2(n);
          e ? e.nodes.push(m) : t.push(m), n = "";
        }
        let u2 = a, f2 = a + 1;
        for (; f2 < r.length && (s2 = r.charCodeAt(f2), !(s2 !== Sr && s2 !== Tr && s2 !== Er && s2 !== Kr && s2 !== Or && s2 !== _r2 && s2 !== jr)); f2++)
          ;
        a = f2 - 1;
        let g2 = r.slice(u2, f2), p2 = g2.trim() === "," ? Wi(g2) : zi(g2);
        e ? e.nodes.push(p2) : t.push(p2);
        break;
      }
      case Rr: {
        let u2 = Mi(n, []);
        if (n = "", u2.value !== ":not" && u2.value !== ":where" && u2.value !== ":has" && u2.value !== ":is") {
          let f2 = a + 1, g2 = 0;
          for (let m = a + 1; m < r.length; m++) {
            if (s2 = r.charCodeAt(m), s2 === Rr) {
              g2++;
              continue;
            }
            if (s2 === Nr) {
              if (g2 === 0) {
                a = m;
                break;
              }
              g2--;
            }
          }
          let p2 = a;
          u2.nodes.push(Bi(r.slice(f2, p2))), n = "", a = p2, e ? e.nodes.push(u2) : t.push(u2);
          break;
        }
        e ? e.nodes.push(u2) : t.push(u2), i.push(u2), e = u2;
        break;
      }
      case Nr: {
        let u2 = i.pop();
        if (n.length > 0) {
          let f2 = _e2(n);
          u2.nodes.push(f2), n = "";
        }
        i.length > 0 ? e = i[i.length - 1] : e = null;
        break;
      }
      case Hi:
      case Gi:
      case Yi: {
        if (n.length > 0) {
          let u2 = _e2(n);
          e ? e.nodes.push(u2) : t.push(u2);
        }
        n = String.fromCharCode(c);
        break;
      }
      case Pr: {
        if (n.length > 0) {
          let g2 = _e2(n);
          e ? e.nodes.push(g2) : t.push(g2);
        }
        n = "";
        let u2 = a, f2 = 0;
        for (let g2 = a + 1; g2 < r.length; g2++) {
          if (s2 = r.charCodeAt(g2), s2 === Pr) {
            f2++;
            continue;
          }
          if (s2 === qi) {
            if (f2 === 0) {
              a = g2;
              break;
            }
            f2--;
          }
        }
        n += r.slice(u2, a + 1);
        break;
      }
      case Zi:
      case Ji: {
        let u2 = a;
        for (let f2 = a + 1; f2 < r.length; f2++)
          if (s2 = r.charCodeAt(f2), s2 === Vr)
            f2 += 1;
          else if (s2 === c) {
            a = f2;
            break;
          }
        n += r.slice(u2, a + 1);
        break;
      }
      case Vr: {
        let u2 = r.charCodeAt(a + 1);
        n += String.fromCharCode(c) + String.fromCharCode(u2), a += 1;
        break;
      }
      default:
        n += String.fromCharCode(c);
    }
  }
  return n.length > 0 && t.push(_e2(n)), t;
}
var Dr = /^[a-z@][a-zA-Z0-9/%._-]*$/;
function Vt({ designSystem: r, ast: t, resolvedConfig: i, featuresRef: e, referenceMode: n }) {
  let s2 = { addBase(a) {
    if (n)
      return;
    let c = ae(a);
    e.current |= xe(c, r), t.push(z2("@layer", "base", c));
  }, addVariant(a, c) {
    if (!Xe.test(a))
      throw new Error(`\`addVariant('${a}')\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);
    if (typeof c == "string") {
      if (c.includes(":merge("))
        return;
    } else if (Array.isArray(c)) {
      if (c.some((f2) => f2.includes(":merge(")))
        return;
    } else if (typeof c == "object") {
      let f2 = function(g2, p2) {
        return Object.entries(g2).some(([m, w2]) => m.includes(p2) || typeof w2 == "object" && f2(w2, p2));
      };
      var u2 = f2;
      if (f2(c, ":merge("))
        return;
    }
    typeof c == "string" || Array.isArray(c) ? r.variants.static(a, (f2) => {
      f2.nodes = Ur(c, f2.nodes);
    }, { compounds: ye2(typeof c == "string" ? [c] : c) }) : typeof c == "object" && r.variants.fromAst(a, ae(c));
  }, matchVariant(a, c, u2) {
    function f2(p2, m, w2) {
      let v2 = c(p2, { modifier: (m == null ? void 0 : m.value) ?? null });
      return Ur(v2, w2);
    }
    try {
      let p2 = c("a", { modifier: null });
      if (typeof p2 == "string" && p2.includes(":merge("))
        return;
      if (Array.isArray(p2) && p2.some((m) => m.includes(":merge(")))
        return;
    } catch {
    }
    let g2 = Object.keys((u2 == null ? void 0 : u2.values) ?? {});
    r.variants.group(() => {
      r.variants.functional(a, (p2, m) => {
        if (!m.value) {
          if ((u2 == null ? void 0 : u2.values) && "DEFAULT" in u2.values) {
            p2.nodes = f2(u2.values.DEFAULT, m.modifier, p2.nodes);
            return;
          }
          return null;
        }
        if (m.value.kind === "arbitrary")
          p2.nodes = f2(m.value.value, m.modifier, p2.nodes);
        else if (m.value.kind === "named" && (u2 == null ? void 0 : u2.values)) {
          let w2 = u2.values[m.value.value];
          if (typeof w2 != "string")
            return;
          p2.nodes = f2(w2, m.modifier, p2.nodes);
        }
      });
    }, (p2, m) => {
      var _a2, _b, _c, _d;
      if (p2.kind !== "functional" || m.kind !== "functional")
        return 0;
      let w2 = p2.value ? p2.value.value : "DEFAULT", v2 = m.value ? m.value.value : "DEFAULT", x = ((_a2 = u2 == null ? void 0 : u2.values) == null ? void 0 : _a2[w2]) ?? w2, k2 = ((_b = u2 == null ? void 0 : u2.values) == null ? void 0 : _b[v2]) ?? v2;
      if (u2 && typeof u2.sort == "function")
        return u2.sort({ value: x, modifier: ((_c = p2.modifier) == null ? void 0 : _c.value) ?? null }, { value: k2, modifier: ((_d = m.modifier) == null ? void 0 : _d.value) ?? null });
      let N2 = g2.indexOf(w2), b2 = g2.indexOf(v2);
      return N2 = N2 === -1 ? g2.length : N2, b2 = b2 === -1 ? g2.length : b2, N2 !== b2 ? N2 - b2 : x < k2 ? -1 : 1;
    });
  }, addUtilities(a) {
    a = Array.isArray(a) ? a : [a];
    let c = a.flatMap((f2) => Object.entries(f2));
    c = c.flatMap(([f2, g2]) => g(f2, ",").map((p2) => [p2.trim(), g2]));
    let u2 = new W2(() => []);
    for (let [f2, g2] of c) {
      if (f2.startsWith("@keyframes ")) {
        n || t.push(H2(f2, ae(g2)));
        continue;
      }
      let p2 = it(f2), m = false;
      if (je(p2, (w2) => {
        if (w2.kind === "selector" && w2.value[0] === "." && Dr.test(w2.value.slice(1))) {
          let v2 = w2.value;
          w2.value = "&";
          let x = De(p2), k2 = v2.slice(1), N2 = x === "&" ? ae(g2) : [H2(x, ae(g2))];
          u2.get(k2).push(...N2), m = true, w2.value = v2;
          return;
        }
        if (w2.kind === "function" && w2.value === ":not")
          return 1;
      }), !m)
        throw new Error(`\`addUtilities({ '${f2}' : … })\` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. \`.scrollbar-none\`.`);
    }
    for (let [f2, g2] of u2)
      r.theme.prefix && D2(g2, (p2) => {
        if (p2.kind === "rule") {
          let m = it(p2.selector);
          je(m, (w2) => {
            w2.kind === "selector" && w2.value[0] === "." && (w2.value = `.${r.theme.prefix}\\:${w2.value.slice(1)}`);
          }), p2.selector = De(m);
        }
      }), r.utilities.static(f2, (p2) => {
        let m = structuredClone(g2);
        return Ir(m, f2, p2.raw), e.current |= Oe(m, r), m;
      });
  }, matchUtilities(a, c) {
    let u2 = (c == null ? void 0 : c.type) ? Array.isArray(c == null ? void 0 : c.type) ? c.type : [c.type] : ["any"];
    for (let [g2, p2] of Object.entries(a)) {
      let m = function({ negative: w2 }) {
        return (v2) => {
          var _a2, _b;
          if (((_a2 = v2.value) == null ? void 0 : _a2.kind) === "arbitrary" && u2.length > 0 && !u2.includes("any") && (v2.value.dataType && !u2.includes(v2.value.dataType) || !v2.value.dataType && !pe(v2.value.value, u2)))
            return;
          let x = u2.includes("color"), k2 = null, N2 = false;
          {
            let P2 = (c == null ? void 0 : c.values) ?? {};
            x && (P2 = Object.assign({ inherit: "inherit", transparent: "transparent", current: "currentcolor" }, P2)), v2.value ? v2.value.kind === "arbitrary" ? k2 = v2.value.value : v2.value.fraction && P2[v2.value.fraction] ? (k2 = P2[v2.value.fraction], N2 = true) : P2[v2.value.value] ? k2 = P2[v2.value.value] : P2.__BARE_VALUE__ && (k2 = P2.__BARE_VALUE__(v2.value) ?? null, N2 = (v2.value.fraction !== null && (k2 == null ? void 0 : k2.includes("/"))) ?? false) : k2 = P2.DEFAULT ?? null;
          }
          if (k2 === null)
            return;
          let b2;
          {
            let P2 = (c == null ? void 0 : c.modifiers) ?? null;
            v2.modifier ? P2 === "any" || v2.modifier.kind === "arbitrary" ? b2 = v2.modifier.value : (P2 == null ? void 0 : P2[v2.modifier.value]) ? b2 = P2[v2.modifier.value] : x && !Number.isNaN(Number(v2.modifier.value)) ? b2 = `${v2.modifier.value}%` : b2 = null : b2 = null;
          }
          if (v2.modifier && b2 === null && !N2)
            return ((_b = v2.value) == null ? void 0 : _b.kind) === "arbitrary" ? null : void 0;
          x && b2 !== null && (k2 = Z2(k2, b2)), w2 && (k2 = `calc(${k2} * -1)`);
          let S = ae(p2(k2, { modifier: b2 }));
          return Ir(S, g2, v2.raw), e.current |= Oe(S, r), S;
        };
      };
      var f2 = m;
      if (!Dr.test(g2))
        throw new Error(`\`matchUtilities({ '${g2}' : … })\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. \`scrollbar\`.`);
      (c == null ? void 0 : c.supportsNegativeValues) && r.utilities.functional(`-${g2}`, m({ negative: true }), { types: u2 }), r.utilities.functional(g2, m({ negative: false }), { types: u2 }), r.utilities.suggest(g2, () => {
        let w2 = (c == null ? void 0 : c.values) ?? {}, v2 = new Set(Object.keys(w2));
        v2.delete("__BARE_VALUE__"), v2.has("DEFAULT") && (v2.delete("DEFAULT"), v2.add(null));
        let x = (c == null ? void 0 : c.modifiers) ?? {}, k2 = x === "any" ? [] : Object.keys(x);
        return [{ supportsNegative: (c == null ? void 0 : c.supportsNegativeValues) ?? false, values: Array.from(v2), modifiers: k2 }];
      });
    }
  }, addComponents(a, c) {
    this.addUtilities(a, c);
  }, matchComponents(a, c) {
    this.matchUtilities(a, c);
  }, theme: rt(r, () => i.theme ?? {}, (a) => a), prefix(a) {
    return a;
  }, config(a, c) {
    let u2 = i;
    if (!a)
      return u2;
    let f2 = tt(a);
    for (let g2 = 0; g2 < f2.length; ++g2) {
      let p2 = f2[g2];
      if (u2[p2] === void 0)
        return c;
      u2 = u2[p2];
    }
    return u2 ?? c;
  } };
  return s2.addComponents = s2.addComponents.bind(s2), s2.matchComponents = s2.matchComponents.bind(s2), s2;
}
function ae(r) {
  let t = [];
  r = Array.isArray(r) ? r : [r];
  let i = r.flatMap((e) => Object.entries(e));
  for (let [e, n] of i)
    if (typeof n != "object") {
      if (!e.startsWith("--")) {
        if (n === "@slot") {
          t.push(H2(e, [z2("@slot")]));
          continue;
        }
        e = e.replace(/([A-Z])/g, "-$1").toLowerCase();
      }
      t.push(l3(e, String(n)));
    } else if (Array.isArray(n))
      for (let s2 of n)
        typeof s2 == "string" ? t.push(l3(e, s2)) : t.push(H2(e, ae(s2)));
    else
      n !== null && t.push(H2(e, ae(n)));
  return t;
}
function Ur(r, t) {
  return (typeof r == "string" ? [r] : r).flatMap((e) => {
    if (e.trim().endsWith("}")) {
      let n = e.replace("}", "{@slot}}"), s2 = me(n);
      return At(s2, t), s2;
    } else
      return H2(e, t);
  });
}
function Ir(r, t, i) {
  D2(r, (e) => {
    if (e.kind === "rule") {
      let n = it(e.selector);
      je(n, (s2) => {
        s2.kind === "selector" && s2.value === `.${t}` && (s2.value = `.${fe(i)}`);
      }), e.selector = De(n);
    }
  });
}
function Fr(r, t, i) {
  for (let e of Xi(t))
    r.theme.addKeyframes(e);
}
function Xi(r) {
  let t = [];
  if ("keyframes" in r.theme)
    for (let [i, e] of Object.entries(r.theme.keyframes))
      t.push(z2("@keyframes", i, ae(e)));
  return t;
}
function Lr(r) {
  return { theme: { ...ye, colors: ({ theme: t }) => t("color", {}), extend: { fontSize: ({ theme: t }) => ({ ...t("text", {}) }), boxShadow: ({ theme: t }) => ({ ...t("shadow", {}) }), animation: ({ theme: t }) => ({ ...t("animate", {}) }), aspectRatio: ({ theme: t }) => ({ ...t("aspect", {}) }), borderRadius: ({ theme: t }) => ({ ...t("radius", {}) }), screens: ({ theme: t }) => ({ ...t("breakpoint", {}) }), letterSpacing: ({ theme: t }) => ({ ...t("tracking", {}) }), lineHeight: ({ theme: t }) => ({ ...t("leading", {}) }), transitionDuration: { DEFAULT: r.get(["--default-transition-duration"]) ?? null }, transitionTimingFunction: { DEFAULT: r.get(["--default-transition-timing-function"]) ?? null }, maxWidth: ({ theme: t }) => ({ ...t("container", {}) }) } } };
}
var en = { blocklist: [], future: {}, prefix: "", important: false, darkMode: null, theme: {}, plugins: [], content: { files: [] } };
function St(r, t) {
  let i = { design: r, configs: [], plugins: [], content: { files: [] }, theme: {}, extend: {}, result: structuredClone(en) };
  for (let n of t)
    Nt(i, n);
  for (let n of i.configs)
    "darkMode" in n && n.darkMode !== void 0 && (i.result.darkMode = n.darkMode ?? null), "prefix" in n && n.prefix !== void 0 && (i.result.prefix = n.prefix ?? ""), "blocklist" in n && n.blocklist !== void 0 && (i.result.blocklist = n.blocklist ?? []), "important" in n && n.important !== void 0 && (i.result.important = n.important ?? false);
  let e = rn(i);
  return { resolvedConfig: { ...i.result, content: i.content, theme: i.theme, plugins: i.plugins }, replacedThemeKeys: e };
}
function tn(r, t) {
  if (Array.isArray(r) && $e(r[0]))
    return r.concat(t);
  if (Array.isArray(t) && $e(t[0]) && $e(r))
    return [r, ...t];
  if (Array.isArray(t))
    return t;
}
function Nt(r, { config: t, base: i, path: e, reference: n }) {
  let s2 = [];
  for (let u2 of t.plugins ?? [])
    "__isOptionsFunction" in u2 ? s2.push({ ...u2(), reference: n }) : "handler" in u2 ? s2.push({ ...u2, reference: n }) : s2.push({ handler: u2, reference: n });
  if (Array.isArray(t.presets) && t.presets.length === 0)
    throw new Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");
  for (let u2 of t.presets ?? [])
    Nt(r, { path: e, base: i, config: u2, reference: n });
  for (let u2 of s2)
    r.plugins.push(u2), u2.config && Nt(r, { path: e, base: i, config: u2.config, reference: !!u2.reference });
  let a = t.content ?? [], c = Array.isArray(a) ? a : a.files;
  for (let u2 of c)
    r.content.files.push(typeof u2 == "object" ? u2 : { base: i, pattern: u2 });
  r.configs.push(t);
}
function rn(r) {
  var _a2;
  let t = /* @__PURE__ */ new Set(), i = rt(r.design, () => r.theme, n), e = Object.assign(i, { theme: i, colors: l });
  function n(s2) {
    return typeof s2 == "function" ? s2(e) ?? null : s2 ?? null;
  }
  for (let s2 of r.configs) {
    let a = s2.theme ?? {}, c = a.extend ?? {};
    for (let u2 in a)
      u2 !== "extend" && t.add(u2);
    Object.assign(r.theme, a);
    for (let u2 in c)
      (_a2 = r.extend)[u2] ?? (_a2[u2] = []), r.extend[u2].push(c[u2]);
  }
  delete r.theme.extend;
  for (let s2 in r.extend) {
    let a = [r.theme[s2], ...r.extend[s2]];
    r.theme[s2] = () => {
      let c = a.map(n);
      return Ke({}, c, tn);
    };
  }
  for (let s2 in r.theme)
    r.theme[s2] = n(r.theme[s2]);
  if (r.theme.screens && typeof r.theme.screens == "object")
    for (let s2 of Object.keys(r.theme.screens)) {
      let a = r.theme.screens[s2];
      a && typeof a == "object" && ("raw" in a || "max" in a || "min" in a && (r.theme.screens[s2] = a.min));
    }
  return t;
}
function zr(r, t) {
  let i = r.theme.container || {};
  if (typeof i != "object" || i === null)
    return;
  let e = nn(i, t);
  e.length !== 0 && t.utilities.static("container", () => structuredClone(e));
}
function nn({ center: r, padding: t, screens: i }, e) {
  let n = [], s2 = null;
  if (r && n.push(l3("margin-inline", "auto")), (typeof t == "string" || typeof t == "object" && t !== null && "DEFAULT" in t) && n.push(l3("padding-inline", typeof t == "string" ? t : t.DEFAULT)), typeof i == "object" && i !== null) {
    s2 = /* @__PURE__ */ new Map();
    let a = Array.from(e.theme.namespace("--breakpoint").entries());
    if (a.sort((c, u2) => we(c[1], u2[1], "asc")), a.length > 0) {
      let [c] = a[0];
      n.push(z2("@media", `(width >= --theme(--breakpoint-${c}))`, [l3("max-width", "none")]));
    }
    for (let [c, u2] of Object.entries(i)) {
      if (typeof u2 == "object")
        if ("min" in u2)
          u2 = u2.min;
        else
          continue;
      s2.set(c, z2("@media", `(width >= ${u2})`, [l3("max-width", u2)]));
    }
  }
  if (typeof t == "object" && t !== null) {
    let a = Object.entries(t).filter(([c]) => c !== "DEFAULT").map(([c, u2]) => [c, e.theme.resolveValue(c, ["--breakpoint"]), u2]).filter(Boolean);
    a.sort((c, u2) => we(c[1], u2[1], "asc"));
    for (let [c, , u2] of a)
      if (s2 && s2.has(c))
        s2.get(c).nodes.push(l3("padding-inline", u2));
      else {
        if (s2)
          continue;
        n.push(z2("@media", `(width >= theme(--breakpoint-${c}))`, [l3("padding-inline", u2)]));
      }
  }
  if (s2)
    for (let [, a] of s2)
      n.push(a);
  return n;
}
function Mr({ addVariant: r, config: t }) {
  let i = t("darkMode", null), [e, n = ".dark"] = Array.isArray(i) ? i : [i];
  if (e === "variant") {
    let s2;
    if (Array.isArray(n) || typeof n == "function" ? s2 = n : typeof n == "string" && (s2 = [n]), Array.isArray(s2))
      for (let a of s2)
        a === ".dark" ? (e = false, console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')) : a.includes("&") || (e = false, console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));
    n = s2;
  }
  e === null || (e === "selector" ? r("dark", `&:where(${n}, ${n} *)`) : e === "media" ? r("dark", "@media (prefers-color-scheme: dark)") : e === "variant" ? r("dark", n) : e === "class" && r("dark", `&:is(${n} *)`));
}
function Wr(r) {
  for (let [t, i] of [["t", "top"], ["tr", "top right"], ["r", "right"], ["br", "bottom right"], ["b", "bottom"], ["bl", "bottom left"], ["l", "left"], ["tl", "top left"]])
    r.utilities.static(`bg-gradient-to-${t}`, () => [l3("--tw-gradient-position", `to ${i} in oklab`), l3("background-image", "linear-gradient(var(--tw-gradient-stops))")]);
  r.utilities.static("bg-left-top", () => [l3("background-position", "left top")]), r.utilities.static("bg-right-top", () => [l3("background-position", "right top")]), r.utilities.static("bg-left-bottom", () => [l3("background-position", "left bottom")]), r.utilities.static("bg-right-bottom", () => [l3("background-position", "right bottom")]), r.utilities.static("object-left-top", () => [l3("object-position", "left top")]), r.utilities.static("object-right-top", () => [l3("object-position", "right top")]), r.utilities.static("object-left-bottom", () => [l3("object-position", "left bottom")]), r.utilities.static("object-right-bottom", () => [l3("object-position", "right bottom")]), r.utilities.functional("max-w-screen", (t) => {
    if (!t.value || t.value.kind === "arbitrary")
      return;
    let i = r.theme.resolve(t.value.value, ["--breakpoint"]);
    if (i)
      return [l3("max-width", i)];
  }), r.utilities.static("overflow-ellipsis", () => [l3("text-overflow", "ellipsis")]), r.utilities.static("decoration-slice", () => [l3("-webkit-box-decoration-break", "slice"), l3("box-decoration-break", "slice")]), r.utilities.static("decoration-clone", () => [l3("-webkit-box-decoration-break", "clone"), l3("box-decoration-break", "clone")]), r.utilities.functional("flex-shrink", (t) => {
    if (!t.modifier) {
      if (!t.value)
        return [l3("flex-shrink", "1")];
      if (t.value.kind === "arbitrary")
        return [l3("flex-shrink", t.value.value)];
      if (p(t.value.value))
        return [l3("flex-shrink", t.value.value)];
    }
  }), r.utilities.functional("flex-grow", (t) => {
    if (!t.modifier) {
      if (!t.value)
        return [l3("flex-grow", "1")];
      if (t.value.kind === "arbitrary")
        return [l3("flex-grow", t.value.value)];
      if (p(t.value.value))
        return [l3("flex-grow", t.value.value)];
    }
  });
}
function Br(r, t) {
  var _a2;
  let i = r.theme.screens || {}, e = ((_a2 = t.variants.get("min")) == null ? void 0 : _a2.order) ?? 0, n = [];
  for (let [a, c] of Object.entries(i)) {
    let m = function(w2) {
      t.variants.static(a, (v2) => {
        v2.nodes = [z2("@media", p2, v2.nodes)];
      }, { order: w2 });
    };
    var s2 = m;
    let u2 = t.variants.get(a), f2 = t.theme.resolveValue(a, ["--breakpoint"]);
    if (u2 && f2 && !t.theme.hasDefault(`--breakpoint-${a}`))
      continue;
    let g2 = true;
    typeof c == "string" && (g2 = false);
    let p2 = on(c);
    g2 ? n.push(m) : m(e);
  }
  if (n.length !== 0) {
    for (let [, a] of t.variants.variants)
      a.order > e && (a.order += n.length);
    t.variants.compareFns = new Map(Array.from(t.variants.compareFns).map(([a, c]) => (a > e && (a += n.length), [a, c])));
    for (let [a, c] of n.entries())
      c(e + a + 1);
  }
}
function on(r) {
  return (Array.isArray(r) ? r : [r]).map((i) => typeof i == "string" ? { min: i } : i && typeof i == "object" ? i : null).map((i) => {
    if (i === null)
      return null;
    if ("raw" in i)
      return i.raw;
    let e = "";
    return i.max !== void 0 && (e += `${i.max} >= `), e += "width", i.min !== void 0 && (e += ` >= ${i.min}`), `(${e})`;
  }).filter(Boolean).join(", ");
}
function qr(r, t) {
  let i = r.theme.aria || {}, e = r.theme.supports || {}, n = r.theme.data || {};
  if (Object.keys(i).length > 0) {
    let s2 = t.variants.get("aria"), a = s2 == null ? void 0 : s2.applyFn, c = s2 == null ? void 0 : s2.compounds;
    t.variants.functional("aria", (u2, f2) => {
      let g2 = f2.value;
      return g2 && g2.kind === "named" && g2.value in i ? a == null ? void 0 : a(u2, { ...f2, value: { kind: "arbitrary", value: i[g2.value] } }) : a == null ? void 0 : a(u2, f2);
    }, { compounds: c });
  }
  if (Object.keys(e).length > 0) {
    let s2 = t.variants.get("supports"), a = s2 == null ? void 0 : s2.applyFn, c = s2 == null ? void 0 : s2.compounds;
    t.variants.functional("supports", (u2, f2) => {
      let g2 = f2.value;
      return g2 && g2.kind === "named" && g2.value in e ? a == null ? void 0 : a(u2, { ...f2, value: { kind: "arbitrary", value: e[g2.value] } }) : a == null ? void 0 : a(u2, f2);
    }, { compounds: c });
  }
  if (Object.keys(n).length > 0) {
    let s2 = t.variants.get("data"), a = s2 == null ? void 0 : s2.applyFn, c = s2 == null ? void 0 : s2.compounds;
    t.variants.functional("data", (u2, f2) => {
      let g2 = f2.value;
      return g2 && g2.kind === "named" && g2.value in n ? a == null ? void 0 : a(u2, { ...f2, value: { kind: "arbitrary", value: n[g2.value] } }) : a == null ? void 0 : a(u2, f2);
    }, { compounds: c });
  }
}
var ln = /^[a-z]+$/;
async function Jr({ designSystem: r, base: t, ast: i, loadModule: e, sources: n }) {
  let s2 = 0, a = [], c = [];
  D2(i, (p2, { parent: m, replaceWith: w2, context: v2 }) => {
    if (p2.kind === "at-rule") {
      if (p2.name === "@plugin") {
        if (m !== null)
          throw new Error("`@plugin` cannot be nested.");
        let x = p2.params.slice(1, -1);
        if (x.length === 0)
          throw new Error("`@plugin` must have a path.");
        let k2 = {};
        for (let N2 of p2.nodes ?? []) {
          if (N2.kind !== "declaration")
            throw new Error(`Unexpected \`@plugin\` option:

${ne2([N2])}

\`@plugin\` options must be a flat list of declarations.`);
          if (N2.value === void 0)
            continue;
          let b2 = N2.value, S = g(b2, ",").map((P2) => {
            if (P2 = P2.trim(), P2 === "null")
              return null;
            if (P2 === "true")
              return true;
            if (P2 === "false")
              return false;
            if (Number.isNaN(Number(P2))) {
              if (P2[0] === '"' && P2[P2.length - 1] === '"' || P2[0] === "'" && P2[P2.length - 1] === "'")
                return P2.slice(1, -1);
              if (P2[0] === "{" && P2[P2.length - 1] === "}")
                throw new Error(`Unexpected \`@plugin\` option: Value of declaration \`${ne2([N2]).trim()}\` is not supported.

Using an object as a plugin option is currently only supported in JavaScript configuration files.`);
            } else
              return Number(P2);
            return P2;
          });
          k2[N2.property] = S.length === 1 ? S[0] : S;
        }
        a.push([{ id: x, base: v2.base, reference: !!v2.reference }, Object.keys(k2).length > 0 ? k2 : null]), w2([]), s2 |= 4;
        return;
      }
      if (p2.name === "@config") {
        if (p2.nodes.length > 0)
          throw new Error("`@config` cannot have a body.");
        if (m !== null)
          throw new Error("`@config` cannot be nested.");
        c.push({ id: p2.params.slice(1, -1), base: v2.base, reference: !!v2.reference }), w2([]), s2 |= 4;
        return;
      }
    }
  }), Wr(r);
  let u2 = r.resolveThemeValue;
  if (r.resolveThemeValue = function(m, w2) {
    return m.startsWith("--") ? u2(m, w2) : (s2 |= Gr({ designSystem: r, base: t, ast: i, sources: n, configs: [], pluginDetails: [] }), r.resolveThemeValue(m, w2));
  }, !a.length && !c.length)
    return 0;
  let [f2, g2] = await Promise.all([Promise.all(c.map(async ({ id: p2, base: m, reference: w2 }) => {
    let v2 = await e(p2, m, "config");
    return { path: p2, base: v2.base, config: v2.module, reference: w2 };
  })), Promise.all(a.map(async ([{ id: p2, base: m, reference: w2 }, v2]) => {
    let x = await e(p2, m, "plugin");
    return { path: p2, base: x.base, plugin: x.module, options: v2, reference: w2 };
  }))]);
  return s2 |= Gr({ designSystem: r, base: t, ast: i, sources: n, configs: f2, pluginDetails: g2 }), s2;
}
function Gr({ designSystem: r, base: t, ast: i, sources: e, configs: n, pluginDetails: s2 }) {
  let a = 0, u2 = [...s2.map((k2) => {
    if (!k2.options)
      return { config: { plugins: [k2.plugin] }, base: k2.base, reference: k2.reference };
    if ("__isOptionsFunction" in k2.plugin)
      return { config: { plugins: [k2.plugin(k2.options)] }, base: k2.base, reference: k2.reference };
    throw new Error(`The plugin "${k2.path}" does not accept options`);
  }), ...n], { resolvedConfig: f2 } = St(r, [{ config: Lr(r.theme), base: t, reference: true }, ...u2, { config: { plugins: [Mr] }, base: t, reference: true }]), { resolvedConfig: g2, replacedThemeKeys: p2 } = St(r, u2), m = r.resolveThemeValue;
  r.resolveThemeValue = function(N2, b2) {
    if (N2[0] === "-" && N2[1] === "-")
      return m(N2, b2);
    let S = v2.theme(N2, void 0);
    if (Array.isArray(S) && S.length === 2)
      return S[0];
    if (Array.isArray(S))
      return S.join(", ");
    if (typeof S == "string")
      return S;
  };
  let w2 = { designSystem: r, ast: i, resolvedConfig: f2, featuresRef: { set current(k2) {
    a |= k2;
  } } }, v2 = Vt({ ...w2, referenceMode: false }), x;
  for (let { handler: k2, reference: N2 } of f2.plugins)
    N2 ? (x || (x = Vt({ ...w2, referenceMode: true })), k2(x)) : k2(v2);
  if (xr(r, g2, p2), Fr(r, g2, p2), qr(g2, r), Br(g2, r), zr(g2, r), !r.theme.prefix && f2.prefix) {
    if (f2.prefix.endsWith("-") && (f2.prefix = f2.prefix.slice(0, -1), console.warn(`The prefix "${f2.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing \`-\` to silence this warning.`)), !ln.test(f2.prefix))
      throw new Error(`The prefix "${f2.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);
    r.theme.prefix = f2.prefix;
  }
  if (!r.important && f2.important === true && (r.important = true), typeof f2.important == "string") {
    let k2 = f2.important;
    D2(i, (N2, { replaceWith: b2, parent: S }) => {
      if (N2.kind === "at-rule" && !(N2.name !== "@tailwind" || N2.params !== "utilities"))
        return (S == null ? void 0 : S.kind) === "rule" && S.selector === k2 ? 2 : (b2(M2(k2, [N2])), 2);
    });
  }
  for (let k2 of f2.blocklist)
    r.invalidCandidates.add(k2);
  for (let k2 of f2.content.files) {
    if ("raw" in k2)
      throw new Error(`Error in the config file/plugin/preset. The \`content\` key contains a \`raw\` entry:

${JSON.stringify(k2, null, 2)}

This feature is not currently supported.`);
    let N2 = false;
    k2.pattern[0] == "!" && (N2 = true, k2.pattern = k2.pattern.slice(1)), e.push({ ...k2, negated: N2 });
  }
  return a;
}
function Hr(r) {
  let t = [0];
  for (let n = 0; n < r.length; n++)
    r.charCodeAt(n) === 10 && t.push(n + 1);
  function i(n) {
    let s2 = 0, a = t.length;
    for (; a > 0; ) {
      let u2 = (a | 0) >> 1, f2 = s2 + u2;
      t[f2] <= n ? (s2 = f2 + 1, a = a - u2 - 1) : a = u2;
    }
    s2 -= 1;
    let c = n - t[s2];
    return { line: s2 + 1, column: c };
  }
  function e({ line: n, column: s2 }) {
    n -= 1, n = Math.min(Math.max(n, 0), t.length - 1);
    let a = t[n], c = t[n + 1] ?? a;
    return Math.min(Math.max(a + s2, 0), c);
  }
  return { find: i, findOffset: e };
}
function Yr({ ast: r }) {
  let t = new W2((n) => Hr(n.code)), i = new W2((n) => ({ url: n.file, content: n.code, ignore: false })), e = { file: null, sources: [], mappings: [] };
  D2(r, (n) => {
    if (!n.src || !n.dst)
      return;
    let s2 = i.get(n.src[0]);
    if (!s2.content)
      return;
    let a = t.get(n.src[0]), c = t.get(n.dst[0]), u2 = s2.content.slice(n.src[1], n.src[2]), f2 = 0;
    for (let m of u2.split(`
`)) {
      if (m.trim() !== "") {
        let w2 = a.find(n.src[1] + f2), v2 = c.find(n.dst[1]);
        e.mappings.push({ name: null, originalPosition: { source: s2, ...w2 }, generatedPosition: v2 });
      }
      f2 += m.length, f2 += 1;
    }
    let g2 = a.find(n.src[2]), p2 = c.find(n.dst[2]);
    e.mappings.push({ name: null, originalPosition: { source: s2, ...g2 }, generatedPosition: p2 });
  });
  for (let n of t.keys())
    e.sources.push(i.get(n));
  return e.mappings.sort((n, s2) => {
    var _a2, _b, _c, _d;
    return n.generatedPosition.line - s2.generatedPosition.line || n.generatedPosition.column - s2.generatedPosition.column || (((_a2 = n.originalPosition) == null ? void 0 : _a2.line) ?? 0) - (((_b = s2.originalPosition) == null ? void 0 : _b.line) ?? 0) || (((_c = n.originalPosition) == null ? void 0 : _c.column) ?? 0) - (((_d = s2.originalPosition) == null ? void 0 : _d.column) ?? 0);
  }), e;
}
var Zr = /^(-?\d+)\.\.(-?\d+)(?:\.\.(-?\d+))?$/;
function nt(r) {
  let t = r.indexOf("{");
  if (t === -1)
    return [r];
  let i = [], e = r.slice(0, t), n = r.slice(t), s2 = 0, a = n.lastIndexOf("}");
  for (let p2 = 0; p2 < n.length; p2++) {
    let m = n[p2];
    if (m === "{")
      s2++;
    else if (m === "}" && (s2--, s2 === 0)) {
      a = p2;
      break;
    }
  }
  if (a === -1)
    throw new Error(`The pattern \`${r}\` is not balanced.`);
  let c = n.slice(1, a), u2 = n.slice(a + 1), f2;
  an(c) ? f2 = sn(c) : f2 = g(c, ","), f2 = f2.flatMap((p2) => nt(p2));
  let g2 = nt(u2);
  for (let p2 of g2)
    for (let m of f2)
      i.push(e + m + p2);
  return i;
}
function an(r) {
  return Zr.test(r);
}
function sn(r) {
  let t = r.match(Zr);
  if (!t)
    return [r];
  let [, i, e, n] = t, s2 = n ? parseInt(n, 10) : void 0, a = [];
  if (/^-?\d+$/.test(i) && /^-?\d+$/.test(e)) {
    let c = parseInt(i, 10), u2 = parseInt(e, 10);
    if (s2 === void 0 && (s2 = c <= u2 ? 1 : -1), s2 === 0)
      throw new Error("Step cannot be zero in sequence expansion.");
    let f2 = c < u2;
    f2 && s2 < 0 && (s2 = -s2), !f2 && s2 > 0 && (s2 = -s2);
    for (let g2 = c; f2 ? g2 <= u2 : g2 >= u2; g2 += s2)
      a.push(g2.toString());
  }
  return a;
}
var un = /^[a-z]+$/;
var pt = ((n) => (n[n.None = 0] = "None", n[n.AtProperty = 1] = "AtProperty", n[n.ColorMix = 2] = "ColorMix", n[n.All = 3] = "All", n))(pt || {});
function fn() {
  throw new Error("No `loadModule` function provided to `compile`");
}
function cn() {
  throw new Error("No `loadStylesheet` function provided to `compile`");
}
function dn(r) {
  let t = 0, i = null;
  for (let e of g(r, " "))
    e === "reference" ? t |= 2 : e === "inline" ? t |= 1 : e === "default" ? t |= 4 : e === "static" ? t |= 8 : e.startsWith("prefix(") && e.endsWith(")") && (i = e.slice(7, -1));
  return [t, i];
}
var Pe = ((c) => (c[c.None = 0] = "None", c[c.AtApply = 1] = "AtApply", c[c.AtImport = 2] = "AtImport", c[c.JsPluginCompat = 4] = "JsPluginCompat", c[c.ThemeFunction = 8] = "ThemeFunction", c[c.Utilities = 16] = "Utilities", c[c.Variants = 32] = "Variants", c))(Pe || {});
async function Qr(r, { base: t = "", from: i, loadModule: e = fn, loadStylesheet: n = cn } = {}) {
  let s2 = 0;
  r = [le({ base: t }, r)], s2 |= await $t(r, t, n, 0, i !== void 0);
  let a = null, c = new Be(), u2 = [], f2 = [], g2 = null, p2 = null, m = [], w2 = [], v2 = [], x = [], k2 = null;
  D2(r, (b2, { parent: S, replaceWith: P2, context: j2 }) => {
    if (b2.kind === "at-rule") {
      if (b2.name === "@tailwind" && (b2.params === "utilities" || b2.params.startsWith("utilities"))) {
        if (p2 !== null) {
          P2([]);
          return;
        }
        if (j2.reference) {
          P2([]);
          return;
        }
        let K2 = g(b2.params, " ");
        for (let F2 of K2)
          if (F2.startsWith("source(")) {
            let O2 = F2.slice(7, -1);
            if (O2 === "none") {
              k2 = O2;
              continue;
            }
            if (O2[0] === '"' && O2[O2.length - 1] !== '"' || O2[0] === "'" && O2[O2.length - 1] !== "'" || O2[0] !== "'" && O2[0] !== '"')
              throw new Error("`source(…)` paths must be quoted.");
            k2 = { base: j2.sourceBase ?? j2.base, pattern: O2.slice(1, -1) };
          }
        p2 = b2, s2 |= 16;
      }
      if (b2.name === "@utility") {
        if (S !== null)
          throw new Error("`@utility` cannot be nested.");
        if (b2.nodes.length === 0)
          throw new Error(`\`@utility ${b2.params}\` is empty. Utilities should include at least one property.`);
        let K2 = ur(b2);
        if (K2 === null)
          throw new Error(`\`@utility ${b2.params}\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter.`);
        f2.push(K2);
      }
      if (b2.name === "@source") {
        if (b2.nodes.length > 0)
          throw new Error("`@source` cannot have a body.");
        if (S !== null)
          throw new Error("`@source` cannot be nested.");
        let K2 = false, F2 = false, O2 = b2.params;
        if (O2[0] === "n" && O2.startsWith("not ") && (K2 = true, O2 = O2.slice(4)), O2[0] === "i" && O2.startsWith("inline(") && (F2 = true, O2 = O2.slice(7, -1)), O2[0] === '"' && O2[O2.length - 1] !== '"' || O2[0] === "'" && O2[O2.length - 1] !== "'" || O2[0] !== "'" && O2[0] !== '"')
          throw new Error("`@source` paths must be quoted.");
        let G2 = O2.slice(1, -1);
        if (F2) {
          let L2 = K2 ? x : v2, q2 = g(G2, " ");
          for (let X2 of q2)
            for (let re2 of nt(X2))
              L2.push(re2);
        } else
          w2.push({ base: j2.base, pattern: G2, negated: K2 });
        P2([]);
        return;
      }
      if (b2.name === "@variant" && (S === null ? b2.nodes.length === 0 ? b2.name = "@custom-variant" : (D2(b2.nodes, (K2) => {
        if (K2.kind === "at-rule" && K2.name === "@slot")
          return b2.name = "@custom-variant", 2;
      }), b2.name === "@variant" && m.push(b2)) : m.push(b2)), b2.name === "@custom-variant") {
        if (S !== null)
          throw new Error("`@custom-variant` cannot be nested.");
        P2([]);
        let [K2, F2] = g(b2.params, " ");
        if (!Xe.test(K2))
          throw new Error(`\`@custom-variant ${K2}\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);
        if (b2.nodes.length > 0 && F2)
          throw new Error(`\`@custom-variant ${K2}\` cannot have both a selector and a body.`);
        if (b2.nodes.length === 0) {
          if (!F2)
            throw new Error(`\`@custom-variant ${K2}\` has no selector or body.`);
          let O2 = g(F2.slice(1, -1), ",");
          if (O2.length === 0 || O2.some((q2) => q2.trim() === ""))
            throw new Error(`\`@custom-variant ${K2} (${O2.join(",")})\` selector is invalid.`);
          let G2 = [], L2 = [];
          for (let q2 of O2)
            q2 = q2.trim(), q2[0] === "@" ? G2.push(q2) : L2.push(q2);
          u2.push((q2) => {
            q2.variants.static(K2, (X2) => {
              let re2 = [];
              L2.length > 0 && re2.push(M2(L2.join(", "), X2.nodes));
              for (let o of G2)
                re2.push(H2(o, X2.nodes));
              X2.nodes = re2;
            }, { compounds: ye2([...L2, ...G2]) });
          });
          return;
        } else {
          u2.push((O2) => {
            O2.variants.fromAst(K2, b2.nodes);
          });
          return;
        }
      }
      if (b2.name === "@media") {
        let K2 = g(b2.params, " "), F2 = [];
        for (let O2 of K2)
          if (O2.startsWith("source(")) {
            let G2 = O2.slice(7, -1);
            D2(b2.nodes, (L2, { replaceWith: q2 }) => {
              if (L2.kind === "at-rule" && L2.name === "@tailwind" && L2.params === "utilities")
                return L2.params += ` source(${G2})`, q2([le({ sourceBase: j2.base }, [L2])]), 2;
            });
          } else if (O2.startsWith("theme(")) {
            let G2 = O2.slice(6, -1), L2 = G2.includes("reference");
            D2(b2.nodes, (q2) => {
              if (q2.kind !== "at-rule") {
                if (L2)
                  throw new Error('Files imported with `@import "…" theme(reference)` must only contain `@theme` blocks.\nUse `@reference "…";` instead.');
                return 0;
              }
              if (q2.name === "@theme")
                return q2.params += " " + G2, 1;
            });
          } else if (O2.startsWith("prefix(")) {
            let G2 = O2.slice(7, -1);
            D2(b2.nodes, (L2) => {
              if (L2.kind === "at-rule" && L2.name === "@theme")
                return L2.params += ` prefix(${G2})`, 1;
            });
          } else
            O2 === "important" ? a = true : O2 === "reference" ? b2.nodes = [le({ reference: true }, b2.nodes)] : F2.push(O2);
        F2.length > 0 ? b2.params = F2.join(" ") : K2.length > 0 && P2(b2.nodes);
      }
      if (b2.name === "@theme") {
        let [K2, F2] = dn(b2.params);
        if (j2.reference && (K2 |= 2), F2) {
          if (!un.test(F2))
            throw new Error(`The prefix "${F2}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);
          c.prefix = F2;
        }
        return D2(b2.nodes, (O2) => {
          if (O2.kind === "at-rule" && O2.name === "@keyframes")
            return c.addKeyframes(O2), 1;
          if (O2.kind === "comment")
            return;
          if (O2.kind === "declaration" && O2.property.startsWith("--")) {
            c.add(ge2(O2.property), O2.value ?? "", K2, O2.src);
            return;
          }
          let G2 = ne2([z2(b2.name, b2.params, [O2])]).split(`
`).map((L2, q2, X2) => `${q2 === 0 || q2 >= X2.length - 2 ? " " : ">"} ${L2}`).join(`
`);
          throw new Error(`\`@theme\` blocks must only contain custom properties or \`@keyframes\`.

${G2}`);
        }), g2 ? P2([]) : (g2 = M2(":root, :host", []), g2.src = b2.src, P2([g2])), 1;
      }
    }
  });
  let N2 = vr(c);
  if (a && (N2.important = a), x.length > 0)
    for (let b2 of x)
      N2.invalidCandidates.add(b2);
  s2 |= await Jr({ designSystem: N2, base: t, ast: r, loadModule: e, sources: w2 });
  for (let b2 of u2)
    b2(N2);
  for (let b2 of f2)
    b2(N2);
  if (g2) {
    let b2 = [];
    for (let [P2, j2] of N2.theme.entries()) {
      if (j2.options & 2)
        continue;
      let K2 = l3(fe(P2), j2.value);
      K2.src = j2.src, b2.push(K2);
    }
    let S = N2.theme.getKeyframes();
    for (let P2 of S)
      r.push(le({ theme: true }, [I2([P2])]));
    g2.nodes = [le({ theme: true }, b2)];
  }
  if (p2) {
    let b2 = p2;
    b2.kind = "context", b2.context = {};
  }
  if (m.length > 0) {
    for (let b2 of m) {
      let S = M2("&", b2.nodes), P2 = b2.params, j2 = N2.parseVariant(P2);
      if (j2 === null)
        throw new Error(`Cannot use \`@variant\` with unknown variant: ${P2}`);
      if (Ae(S, j2, N2.variants) === null)
        throw new Error(`Cannot use \`@variant\` with variant: ${P2}`);
      Object.assign(b2, S);
    }
    s2 |= 32;
  }
  return s2 |= xe(r, N2), s2 |= Oe(r, N2), D2(r, (b2, { replaceWith: S }) => {
    if (b2.kind === "at-rule")
      return b2.name === "@utility" && S([]), 1;
  }), { designSystem: N2, ast: r, sources: w2, root: k2, utilitiesNode: p2, features: s2, inlineCandidates: v2 };
}
async function pn(r, t = {}) {
  let { designSystem: i, ast: e, sources: n, root: s2, utilitiesNode: a, features: c, inlineCandidates: u2 } = await Qr(r, t);
  e.unshift(We(`! tailwindcss v${Rt} | MIT License | https://tailwindcss.com `));
  function f2(v2) {
    i.invalidCandidates.add(v2);
  }
  let g2 = /* @__PURE__ */ new Set(), p2 = null, m = 0, w2 = false;
  for (let v2 of u2)
    i.invalidCandidates.has(v2) || (g2.add(v2), w2 = true);
  return { sources: n, root: s2, features: c, build(v2) {
    if (c === 0)
      return r;
    if (!a)
      return p2 ?? (p2 = ve(e, i, t.polyfills)), p2;
    let x = w2, k2 = false;
    w2 = false;
    let N2 = g2.size;
    for (let S of v2)
      if (!i.invalidCandidates.has(S))
        if (S[0] === "-" && S[1] === "-") {
          let P2 = i.theme.markUsedVariable(S);
          x || (x = P2), k2 || (k2 = P2);
        } else
          g2.add(S), x || (x = g2.size !== N2);
    if (!x)
      return p2 ?? (p2 = ve(e, i, t.polyfills)), p2;
    let b2 = de2(g2, i, { onInvalidCandidate: f2 }).astNodes;
    return t.from && D2(b2, (S) => {
      S.src ?? (S.src = a.src);
    }), !k2 && m === b2.length ? (p2 ?? (p2 = ve(e, i, t.polyfills)), p2) : (m = b2.length, a.nodes = b2, p2 = ve(e, i, t.polyfills), p2);
  } };
}
async function xa(r, t = {}) {
  let i = me(r, { from: t.from }), e = await pn(i, t), n = i, s2 = r;
  return { ...e, build(a) {
    let c = e.build(a);
    return c === n || (s2 = ne2(c, !!t.from), n = c), s2;
  }, buildSourceMap() {
    return Yr({ ast: n });
  } };
}
async function Aa(r, t = {}) {
  return (await Qr(me(r), t)).designSystem;
}
function mn() {
  throw new Error("It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.");
}
export {
  Pe as Features,
  pt as Polyfills,
  Aa as __unstable__loadDesignSystem,
  xa as compile,
  pn as compileAst,
  mn as default
};
//# sourceMappingURL=tailwindcss.js.map
