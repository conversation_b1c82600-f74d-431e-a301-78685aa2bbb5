{"version": 3, "file": "consistent-type-assertions.js", "sourceRoot": "", "sources": ["../../src/rules/consistent-type-assertions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,wEAAsE;AACtE,+CAAiC;AAEjC,kCAOiB;AACjB,2DAAwD;AAoBxD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,4BAA4B;IAClC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;QACpB,IAAI,EAAE;YACJ,WAAW,EAAE,6CAA6C;YAC1D,WAAW,EAAE,WAAW;SACzB;QACD,QAAQ,EAAE;YACR,EAAE,EAAE,4CAA4C;YAChD,eAAe,EAAE,4CAA4C;YAC7D,KAAK,EAAE,iCAAiC;YACxC,6BAA6B,EAAE,qCAAqC;YACp<PERSON>,wCAAwC,EACtC,0CAA0C;YAC5C,uCAAuC,EACrC,mDAAmD;SACtD;QACD,MAAM,EAAE;YACN;gBACE,KAAK,EAAE;oBACL;wBACE,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,cAAc,EAAE;gCACd,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,CAAC,OAAO,CAAC;6BAChB;yBACF;wBACD,oBAAoB,EAAE,KAAK;wBAC3B,QAAQ,EAAE,CAAC,gBAAgB,CAAC;qBAC7B;oBACD;wBACE,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,cAAc,EAAE;gCACd,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;6BAC9B;4BACD,2BAA2B,EAAE;gCAC3B,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,CAAC,OAAO,EAAE,oBAAoB,EAAE,OAAO,CAAC;6BAC/C;yBACF;wBACD,oBAAoB,EAAE,KAAK;wBAC3B,QAAQ,EAAE,CAAC,gBAAgB,CAAC;qBAC7B;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,cAAc,EAAE,IAAI;YACpB,2BAA2B,EAAE,OAAO;SACrC;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAExD,SAAS,OAAO,CAAC,IAAuB;YACtC,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;gBACjD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,CACL,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAChD,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,CAC/B,CAAC;QACJ,CAAC;QAED,SAAS,sBAAsB,CAAC,IAAmB;YACjD,gDAAgD;YAChD,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,IAAI,IAAA,sBAAe,EAAC,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC;gBACtC,MAAM,gBAAgB,GAAG,UAAU,CAAC,cAAc,CAChD,IAAI,EACJ,0BAAmB,CACnB,CAAC;gBACH,MAAM,gBAAgB,GAAG,UAAU,CAAC,aAAa,CAC/C,IAAI,EACJ,0BAAmB,CACnB,CAAC;gBAEH,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACxD,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC;YAED,OAAO,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;QAC3D,CAAC;QAED,SAAS,4BAA4B,CACnC,IAAwD;YAExD,MAAM,SAAS,GAAG,OAAO,CAAC,cAAc,CAAC;YAEzC,0DAA0D;YAC1D,IAAI,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;gBAC1D,OAAO;YACT,CAAC;YACD,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI;gBACJ,SAAS;gBACT,IAAI,EACF,SAAS,KAAK,OAAO;oBACnB,CAAC,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;oBACnD,CAAC,CAAC,EAAE;gBACR,GAAG,EACD,SAAS,KAAK,IAAI;oBAChB,CAAC,CAAC,CAAC,KAAK,EAAoB,EAAE;wBAC1B,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CACrD,IAAgC,CACjC,CAAC;wBAEF;;;2BAGG;wBACH,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBAC3D,MAAM,kBAAkB,GAAG,UAAU,CAAC,OAAO,CAC3C,IAAI,CAAC,cAAc,CACpB,CAAC;wBAEF,MAAM,YAAY,GAAG,IAAA,4BAAqB,EACxC,EAAE,CAAC,UAAU,CAAC,YAAY,EAC1B,EAAE,CAAC,UAAU,CAAC,OAAO,CACtB,CAAC;wBACF,MAAM,gBAAgB,GAAG,IAAA,4BAAqB,EAC5C,MAAM,CAAC,MAAM,CAAC,IAAI,EAClB,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC,MAAM,CAAC;4BAClC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI;4BAClC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EACzB,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC;4BAC/B,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI;gCAC7B,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;4BACtC,CAAC,CAAC,SAAS,CACd,CAAC;wBAEF,MAAM,IAAI,GAAG,GAAG,cAAc,OAAO,kBAAkB,EAAE,CAAC;wBAC1D,OAAO,KAAK,CAAC,WAAW,CACtB,IAAI,EACJ,IAAA,sBAAe,EAAC,IAAI,EAAE,UAAU,CAAC;4BAC/B,CAAC,CAAC,IAAI;4BACN,CAAC,CAAC,IAAA,+BAAc,EAAC,IAAI,EAAE,YAAY,EAAE,gBAAgB,CAAC,CACzD,CAAC;oBACJ,CAAC;oBACH,CAAC,CAAC,SAAS;aAChB,CAAC,CAAC;QACL,CAAC;QAED,SAAS,SAAS,CAAC,IAAuB;YACxC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,sBAAc,CAAC,YAAY,CAAC;gBACjC,KAAK,sBAAc,CAAC,gBAAgB;oBAClC,OAAO,KAAK,CAAC;gBACf,KAAK,sBAAc,CAAC,eAAe;oBACjC,OAAO;oBACL,kCAAkC;oBAClC,CAAC,OAAO,CAAC,IAAI,CAAC;wBACd,uEAAuE;wBACvE,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,CACtD,CAAC;gBAEJ;oBACE,OAAO,IAAI,CAAC;YAChB,CAAC;QACH,CAAC;QAED,SAAS,eAAe,CACtB,IAAwD;YAExD,IACE,OAAO,CAAC,cAAc,KAAK,OAAO;gBAClC,OAAO,CAAC,2BAA2B,KAAK,OAAO;gBAC/C,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EACxD,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IACE,OAAO,CAAC,2BAA2B,KAAK,oBAAoB;gBAC5D,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa;oBAChD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;oBAClD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;oBAClD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;oBACrD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB,CAAC,EAC7D,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAI,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;gBACnC,MAAM,OAAO,GAA+C,EAAE,CAAC;gBAC/D,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;oBACtD,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,EAC9B,CAAC;oBACD,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;oBACxB,OAAO,CAAC,IAAI,CAAC;wBACX,SAAS,EAAE,0CAA0C;wBACrD,IAAI,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;wBACvD,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;4BACZ,KAAK,CAAC,eAAe,CACnB,MAAM,CAAC,EAAE,EACT,KAAK,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAC/C;4BACD,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;yBACjE;qBACF,CAAC,CAAC;gBACL,CAAC;gBACD,OAAO,CAAC,IAAI,CAAC;oBACX,SAAS,EAAE,yCAAyC;oBACpD,IAAI,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;oBACvD,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;wBACZ,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBAChE,KAAK,CAAC,eAAe,CACnB,IAAI,EACJ,cAAc,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC,OAAO,CAC1C,IAAI,CAAC,cAAc,CACpB,EAAE,CACJ;qBACF;iBACF,CAAC,CAAC;gBAEH,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,+BAA+B;oBAC1C,OAAO;iBACR,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,eAAe,CAAC,IAAI;gBAClB,IAAI,OAAO,CAAC,cAAc,KAAK,eAAe,EAAE,CAAC;oBAC/C,4BAA4B,CAAC,IAAI,CAAC,CAAC;oBACnC,OAAO;gBACT,CAAC;gBAED,eAAe,CAAC,IAAI,CAAC,CAAC;YACxB,CAAC;YACD,cAAc,CAAC,IAAI;gBACjB,IAAI,OAAO,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;oBACpC,4BAA4B,CAAC,IAAI,CAAC,CAAC;oBACnC,OAAO;gBACT,CAAC;gBAED,eAAe,CAAC,IAAI,CAAC,CAAC;YACxB,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}