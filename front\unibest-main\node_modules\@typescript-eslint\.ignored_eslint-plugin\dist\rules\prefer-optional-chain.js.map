{"version": 3, "file": "prefer-optional-chain.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-optional-chain.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,wEAAsE;AAEtE,+CAAiC;AAEjC,kCAKiB;AACjB,6EAA0E;AAE1E,+FAG6D;AAM7D,kBAAe,IAAA,iBAAU,EAGvB;IACA,IAAI,EAAE,uBAAuB;IAC7B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,yHAAyH;YAC3H,WAAW,EAAE,WAAW;YACxB,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,mBAAmB,EACjB,6FAA6F;YAC/F,oBAAoB,EAAE,8BAA8B;SACrD;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,QAAQ,EAAE;wBACR,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,kFAAkF;qBACrF;oBACD,YAAY,EAAE;wBACZ,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,sFAAsF;qBACzF;oBACD,WAAW,EAAE;wBACX,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,qFAAqF;qBACxF;oBACD,WAAW,EAAE;wBACX,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,qFAAqF;qBACxF;oBACD,YAAY,EAAE;wBACZ,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,sFAAsF;qBACzF;oBACD,WAAW,EAAE;wBACX,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,qFAAqF;qBACxF;oBACD,cAAc,EAAE;wBACd,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,2GAA2G;qBAC9G;oBACD,kEAAkE,EAAE;wBAClE,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,kIAAkI;qBACrI;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI;YAClB,WAAW,EAAE,IAAI;YACjB,WAAW,EAAE,IAAI;YACjB,YAAY,EAAE,IAAI;YAClB,WAAW,EAAE,IAAI;YACjB,cAAc,EAAE,KAAK;YACrB,kEAAkE,EAAE,KAAK;SAC1E;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAElD,MAAM,YAAY,GAAG,IAAI,GAAG,EAA8B,CAAC;QAE3D,OAAO;YACL,8DAA8D;YAC9D,oEAAoE,CAClE,IAAgC;gBAEhC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;gBAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;gBAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC/B,MAAM,+BAA+B,GACnC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBAClD,SAAS,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC;gBACpC,IACE,CAAC,+BAA+B;oBAChC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBACnD,UAAU,CAAC,QAAQ,EACnB,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAEvB,SAAS,yBAAyB;oBAChC,MAAM,aAAa,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAErE,MAAM,UAAU,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACtE,MAAM,QAAQ,GAAG,EAAE,CAAC,kBAAkB,CAAC,aAAa,CAAC;wBACnD,CAAC,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI;wBAClC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;oBAC1B,MAAM,cAAc,GAAG,IAAA,4BAAqB,EAC1C,UAAU,CAAC,IAAI,EACf,QAAQ,CACT,CAAC;oBAEF,OAAO,cAAc,GAAG,yBAAkB,CAAC,YAAY,CAAC;gBAC1D,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,UAAU;oBAChB,SAAS,EAAE,qBAAqB;oBAChC,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,sBAAsB;4BACjC,GAAG,EAAE,CAAC,KAAK,EAAW,EAAE;gCACtB,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gCAClD,wEAAwE;gCACxE,MAAM,oBAAoB,GAAG,yBAAyB,EAAE;oCACtD,CAAC,CAAC,IAAI,YAAY,GAAG;oCACrB,CAAC,CAAC,YAAY,CAAC;gCACjB,MAAM,wBAAwB,GAAG,UAAU,CAAC,OAAO,CACjD,UAAU,CAAC,QAAQ,CACpB,CAAC;gCACF,MAAM,oBAAoB,GAAG,UAAU,CAAC,QAAQ;oCAC9C,CAAC,CAAC,IAAI,wBAAwB,GAAG;oCACjC,CAAC,CAAC,wBAAwB,CAAC;gCAC7B,OAAO,KAAK,CAAC,gBAAgB,CAC3B,UAAU,CAAC,KAAK,EAChB,GAAG,oBAAoB,KAAK,oBAAoB,EAAE,CACnD,CAAC;4BACJ,CAAC;yBACF;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;YAED,mCAAmC,CACjC,IAAgC;gBAEhC,IAAI,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC3B,OAAO;gBACT,CAAC;gBAED,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG,IAAA,6CAAqB,EAC3D,IAAI,EACJ,cAAc,EACd,OAAO,CACR,CAAC;gBAEF,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE,CAAC;oBACxC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC5B,CAAC;gBAED,IAAI,YAAY,GAAmB,EAAE,CAAC;gBACtC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;oBAC/B,IAAI,OAAO,CAAC,IAAI,4CAA4B,EAAE,CAAC;wBAC7C,IAAA,2BAAY,EACV,OAAO,EACP,UAAU,EACV,cAAc,EACd,OAAO,EACP,IAAI,CAAC,QAAQ,EACb,YAAY,CACb,CAAC;wBACF,YAAY,GAAG,EAAE,CAAC;oBACpB,CAAC;yBAAM,CAAC;wBACN,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC7B,CAAC;gBACH,CAAC;gBAED,qCAAqC;gBACrC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5B,IAAA,2BAAY,EACV,OAAO,EACP,UAAU,EACV,cAAc,EACd,OAAO,EACP,IAAI,CAAC,QAAQ,EACb,YAAY,CACb,CAAC;gBACJ,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}