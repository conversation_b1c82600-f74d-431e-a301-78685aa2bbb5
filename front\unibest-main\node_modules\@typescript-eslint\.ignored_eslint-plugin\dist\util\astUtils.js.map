{"version": 3, "file": "astUtils.js", "sourceRoot": "", "sources": ["../../src/util/astUtils.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,+CAAiC;AAEjC,iDAA8C;AAE9C,oCAAoC;AACpC,qEAAmD;AAEnD,0FAA0F;AAC1F,0HAA0H;AAC1H,sGAAsG;AACtG;;;;;;GAMG;AACH,SAAgB,uCAAuC,CACrD,UAA+B,EAC/B,OAAyB,EACzB,IAAY;IAEZ,MAAM,WAAW,GAAG,IAAI,MAAM,CAC5B,SAAS,IAAA,2BAAY,EAAC,IAAI,CAAC,eAAe,EAC1C,IAAI,CACL,CAAC;IAEF,qCAAqC;IACrC,WAAW,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAE5D,gCAAgC;IAChC,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAE9C,4BAA4B;IAC5B,MAAM,KAAK,GAAG,UAAU,CAAC,eAAe,CACtC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC/D,CAAC;IACF,MAAM,GAAG,GAAG;QACV,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,MAAM,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;KACjD,CAAC;IAEF,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;AACxB,CAAC;AA1BD,0FA0BC;AAED,+IAA+I;AAC/I,2EAA2E;AAC3E,6FAA6F;AAC7F,SAAgB,sBAAsB,CACpC,IAAc,EACd,OAAwC;IAExC,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;IAEtB,SAAS,QAAQ,CAAC,IAAa;QAC7B,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe;gBAChC,OAAO,OAAO,CAAC,IAA0B,CAAC,CAAC;YAC7C,KAAK,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC;YAC7B,KAAK,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC;YACzB,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;YAC/B,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;YAC/B,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC;YAClC,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC;YAChC,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC;YAClC,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC;YAClC,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC;YACjC,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC;YACnC,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;YAC9B,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC;YACjC,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;YACpC,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC;YAChC,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW;gBAC5B,OAAO,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AA9BD,wDA8BC"}