{"version": 3, "file": "explicitReturnTypeUtils.js", "sourceRoot": "", "sources": ["../../src/util/explicitReturnTypeUtils.ts"], "names": [], "mappings": ";;;AACA,oDAAuE;AAEvE,yCAAsE;AACtE,6DAA0D;AAO1D;;;;;GAKG;AACH,SAAS,sCAAsC,CAC7C,IAAmB;IAEnB,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAC5E,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAS,sCAAsC,CAC7C,IAAmB;IAEnB,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CACzE,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAS,kBAAkB,CACzB,MAAqB,EACrB,MAA2B;IAE3B,OAAO,CACL,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;QAC7C,+BAA+B;QAC/B,MAAM,CAAC,MAAM,KAAK,MAAM,CACzB,CAAC;AACJ,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,UAAU,CACjB,IAAmB;IAEnB,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB;QACnD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CAChD,CAAC;AACJ,CAAC;AAED,SAAS,aAAa,CACpB,MAAqB,EACrB,MAA2B;IAE3B,OAAO,CACL,IAAA,0BAAe,EAAC,MAAM,CAAC;QACvB,sCAAsC,CAAC,MAAM,CAAC;QAC9C,sCAAsC,CAAC,MAAM,CAAC;QAC9C,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC;QAClC,UAAU,CAAC,MAAM,CAAC,CACnB,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,SAAS,qBAAqB,CAC5B,IAAmB;IAEnB,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,CAAC;AACpD,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,0BAA0B,CACjC,QAAmC;IAEnC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ,EAAE,CAAC;QAC3D,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;IACnC,IAAI,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE,CAAC;QACxD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IAEjC,OAAO,aAAa,CAAC,MAAM,CAAC,IAAI,0BAA0B,CAAC,MAAM,CAAC,CAAC;AACrE,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAS,uCAAuC,CAAC,EAC/C,IAAI,GACS;IACb,mDAAmD;IACnD,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC1E,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;QAE9B,iEAAiE;QACjE,IACE,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;YACjD,CAAC,CAAC,SAAS,CAAC,QAAQ,EACpB,CAAC;YACD,8CAA8C;YAC9C,IAAI,GAAG,SAAS,CAAC,QAAQ,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,4DAA4D;IAC5D,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;QACpD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CAChD,CAAC;AACJ,CAAC;AAqMC,0FAAuC;AAnMzC;;;;;GAKG;AACH,SAAS,6BAA6B,CACpC,IAAsC;IAEtC,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;IACtB,IAAI,IAAA,0BAAe,EAAC,IAAI,CAAC,EAAE,CAAC;QAC1B,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;QAChC,IAAI,cAAc,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;YAC3D,MAAM,EAAE,QAAQ,EAAE,GAAG,cAAc,CAAC;YACpC,IACE,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC3C,QAAQ,CAAC,IAAI,KAAK,OAAO,EACzB,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AASD;;GAEG;AACH,SAAS,yBAAyB,CAChC,IAAwB,EACxB,OAAgB;IAEhB,MAAM,MAAM,GAAG,mBAAW,CAAC,UAAU,CACnC,IAAI,CAAC,MAAM,EACX,mBAAW,CAAC,iBAAiB,CAAC,aAAa,CAC5C,CAAC;IAEF,IAAI,CAAC,OAAO,CAAC,6BAA6B,EAAE,CAAC;QAC3C,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,CACL,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC;QAC3B,0BAA0B,CAAC,MAAM,CAAC;QAClC,qBAAqB,CAAC,MAAM,CAAC,CAC9B,CAAC;AACJ,CAAC;AAgJC,8DAAyB;AA9I3B;;;GAGG;AACH,SAAS,mCAAmC,CAC1C,IAAwB,EACxB,OAAgB;IAEhB,IAAI,yBAAyB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC;QAC7C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,MAAM,GAAG,mBAAW,CAAC,UAAU,CACnC,IAAI,CAAC,MAAM,EACX,mBAAW,CAAC,iBAAiB,CAAC,aAAa,CAC5C,CAAC;IACF,IACE,OAAO,CAAC,gBAAgB;QACxB,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;QACjD,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC/C,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB;QACvD,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EACjD,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,oEAAoE;IACpE,OAAO,CACL,OAAO,CAAC,yCAAyC,KAAK,IAAI;QAC1D,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;QACpD,6BAA6B,CAAC,IAAI,CAAC,CACpC,CAAC;AACJ,CAAC;AA+GC,kFAAmC;AA7GrC;;GAEG;AACH,SAAS,yBAAyB,CAChC,IAAkB,EAClB,OAAgB;IAEhB,IACE,OAAO,CAAC,yBAAyB;QACjC,uCAAuC,CAAC,IAAI,CAAC,EAC7C,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CACL,IAAI,CAAC,UAAU,IAAI,IAAI;QACvB,IAAA,wBAAa,EAAC,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAA,mBAAQ,EAAC,IAAI,CAAC,MAAM,CAAC,CACtB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,uBAAuB,CAC9B,IAAkB,EAClB,OAAgB,EAChB,UAA+B,EAC/B,MAA8C;IAE9C,IAAI,yBAAyB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC;QAC7C,OAAO;IACT,CAAC;IAED,MAAM,CAAC,IAAA,uCAAkB,EAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;AAC/C,CAAC;AAqEC,0DAAuB;AAnEzB;;GAEG;AACH,SAAS,iCAAiC,CACxC,IAAwB,EACxB,OAAgB,EAChB,UAA+B,EAC/B,MAA8C;IAE9C,IAAI,mCAAmC,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC;QACvD,OAAO;IACT,CAAC;IAED,uBAAuB,CAAC,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;AAC7D,CAAC;AAoDC,8EAAiC;AAlDnC;;GAEG;AACH,SAAS,qBAAqB,CAAC,IAAkB;IAC/C,IAAI,QAAQ,GAA8B,IAAI,CAAC,MAAM,CAAC;IAEtD,IAAI,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ,EAAE,CAAC;QAC9C,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC;IAC5B,CAAC;IAED,oGAAoG;IACpG,MAAM,iBAAiB,GAAG,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,CAAC;IAC3E,MAAM,eAAe,GACnB,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;QACxD,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC;IACvD,IAAI,CAAC,iBAAiB,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3C,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,QAAQ,EAAE,CAAC;QAChB,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,KAAK,sBAAc,CAAC,uBAAuB,CAAC;YAC5C,KAAK,sBAAc,CAAC,kBAAkB,CAAC;YACvC,KAAK,sBAAc,CAAC,mBAAmB;gBACrC,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;oBACxB,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,MAAM;YAER,2BAA2B;YAC3B,6DAA6D;YAC7D,KAAK,sBAAc,CAAC,kBAAkB;gBACpC,IAAI,QAAQ,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC;oBAC/B,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,MAAM;YACR,KAAK,sBAAc,CAAC,kBAAkB;gBACpC,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;oBAC5B,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,MAAM;QACV,CAAC;QAED,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC7B,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAUC,sDAAqB"}