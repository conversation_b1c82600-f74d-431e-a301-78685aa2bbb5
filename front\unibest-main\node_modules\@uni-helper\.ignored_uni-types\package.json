{"name": "@uni-helper/uni-types", "version": "1.0.0-alpha.3", "description": "为 uni-app 组件、uni-ui 组件和 uni-cloud 组件提供 TypeScript 类型", "keywords": ["uni-app", "uniapp", "types", "type"], "homepage": "https://github.com/uni-helper/uni-types/tree/main/packages/uni-types", "bugs": {"url": "https://github.com/uni-helper/uni-types/issues"}, "repository": {"type": "git", "url": "git+https://github.com/uni-helper/uni-types.git"}, "funding": "https://github.com/ModyQyW/sponsors", "license": "MIT", "author": {"name": "ModyQyW", "email": "<EMAIL>", "url": "https://modyqyw.top"}, "sideEffects": false, "type": "module", "exports": {".": {"import": {"types": "./dist/index.d.mts"}, "require": {"types": "./dist/index.d.cts"}}, "./volar-plugin": {"import": {"types": "./dist/volar-plugin.d.mts", "default": "./dist/volar-plugin.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/volar-plugin.cjs"}}}, "main": "dist/index.d.ts", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist"], "dependencies": {"@uni-helper/uni-app-types": "1.0.0-alpha.3", "@uni-helper/uni-cloud-types": "1.0.0-alpha.3", "@uni-helper/uni-ui-types": "1.0.0-alpha.3"}, "devDependencies": {"vue": "3.4.36"}, "peerDependencies": {"typescript": "^5.5.4", "vue": "^3.4.21", "@uni-helper/uni-app-types": "1.0.0-alpha.3", "@uni-helper/uni-ui-types": "1.0.0-alpha.3", "@uni-helper/uni-cloud-types": "1.0.0-alpha.3"}, "engines": {"node": ">=14.18"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}