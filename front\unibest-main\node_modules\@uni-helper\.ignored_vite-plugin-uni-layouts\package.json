{"name": "@uni-helper/vite-plugin-uni-layouts", "type": "module", "version": "0.1.11", "packageManager": "pnpm@8.9.0", "description": "Customizable layouts framework for uni-app applications using Vite.", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "https://github.com/uni-helper/vite-plugin-uni-layouts#readme", "repository": {"type": "git", "url": "git+https://github.com/uni-helper/vite-plugin-uni-layouts.git"}, "bugs": "https://github.com/uni-helper/vite-plugin-uni-layouts/issues", "keywords": [], "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./client": {"types": "./client.d.ts"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"], "client": ["./client.d.ts"]}}, "files": ["client.d.ts", "dist"], "dependencies": {"@babel/types": "^7.23.5", "@uni-helper/uni-env": "^0.1.1", "@vue/compiler-core": "3.4.21", "@vue/compiler-sfc": "3.4.21", "ast-kit": "^0.11.3", "c12": "^1.5.1", "chokidar": "^3.5.3", "fast-glob": "^3.3.2", "jsonc-parser": "^3.2.0", "magic-string": "^0.30.5", "scule": "^1.1.1"}, "devDependencies": {"@antfu/eslint-config": "1.0.0-beta.26", "@types/node": "^20.10.4", "bumpp": "^9.2.1", "eslint": "^8.55.0", "typescript": "^5.3.3", "unbuild": "^2.0.0", "vite": "^4.4.12", "vitest": "^1.0.4"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub", "release": "bumpp", "play": "npm -C playground run dev:h5", "play:mp-weixin": "npm -C playground run dev:mp-weixin", "test": "vitest", "lint": "eslint .", "lint:fix": "eslint . --fix"}}