'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

const node_fs = require('node:fs');
const process = require('node:process');
const c12 = require('c12');
const node_path = require('node:path');
const vite = require('vite');

function _interopDefaultCompat (e) { return e && typeof e === 'object' && 'default' in e ? e.default : e; }

const process__default = /*#__PURE__*/_interopDefaultCompat(process);

const manifestJsonPath = vite.normalizePath(
  node_path.resolve(process__default.env.UNI_INPUT_DIR || `${process__default.cwd()}/src`, "manifest.json")
);
const defaultManifestConfig = {
  "name": "",
  "appid": "",
  "description": "",
  "versionName": "1.0.0",
  "versionCode": "100",
  "transformPx": false,
  /* 5+App特有相关 */
  "app-plus": {
    usingComponents: true,
    nvueStyleCompiler: "uni-app",
    compilerVersion: 3,
    splashscreen: {
      alwaysShowBeforeRender: true,
      waiting: true,
      autoclose: true,
      delay: 0
    },
    /* 模块配置 */
    modules: {},
    /* 应用发布信息 */
    distribute: {
      /* android打包配置 */
      android: {
        permissions: []
      },
      /* ios打包配置 */
      ios: {},
      /* SDK配置 */
      sdkConfigs: {}
    }
  },
  /* 快应用特有相关 */
  "quickapp": {},
  /* 小程序特有相关 */
  "mp-weixin": {
    appid: "",
    setting: {
      urlCheck: false
    },
    usingComponents: true
  },
  "mp-alipay": {
    usingComponents: true
  },
  "mp-baidu": {
    usingComponents: true
  },
  "mp-toutiao": {
    usingComponents: true
  },
  "uniStatistics": {
    enable: false
  },
  "vueVersion": "3"
};

function resolveOptions(userOptions) {
  return {
    minify: false,
    ...userOptions
  };
}

class ManifestContext {
  options;
  unwatch;
  constructor(options) {
    this.options = resolveOptions(options);
  }
  async setup() {
    const { config, unwatch } = await c12.watchConfig({
      cwd: process__default.env.VITE_ROOT_DIR,
      name: "manifest",
      defaultConfig: defaultManifestConfig,
      rcFile: false,
      packageJson: false,
      onUpdate: (config2) => {
        ManifestContext.WriteManifestJSON(config2.newConfig.config, this.options.minify);
      }
    });
    ManifestContext.WriteManifestJSON(config, this.options.minify);
    this.unwatch = unwatch;
  }
  static WriteManifestJSON(config = {}, minify = false) {
    node_fs.writeFileSync(
      manifestJsonPath,
      JSON.stringify(config, null, minify ? 0 : 2)
    );
  }
  static CheckManifestJsonFile() {
    if (!node_fs.existsSync(manifestJsonPath))
      ManifestContext.WriteManifestJSON();
  }
}

function defineManifestConfig(config) {
  return config;
}

ManifestContext.CheckManifestJsonFile();
async function VitePluginUniManifest(userOptions = {}) {
  const ctx = new ManifestContext(userOptions);
  ctx.setup();
  return {
    name: "vite-plugin-uni-manifest",
    enforce: "pre",
    buildEnd: () => ctx.unwatch()
  };
}

exports.VitePluginUniManifest = VitePluginUniManifest;
exports.default = VitePluginUniManifest;
exports.defineManifestConfig = defineManifestConfig;
