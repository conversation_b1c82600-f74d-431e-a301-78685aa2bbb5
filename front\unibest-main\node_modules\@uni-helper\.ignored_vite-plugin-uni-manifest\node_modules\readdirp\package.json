{"name": "readdirp", "description": "Recursive version of fs.readdir with small RAM & CPU footprint.", "version": "4.1.2", "homepage": "https://github.com/paulmillr/readdirp", "repository": {"type": "git", "url": "git://github.com/paulmillr/readdirp.git"}, "license": "MIT", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "author": "<PERSON><PERSON> <<EMAIL>> (thlorenz.com)", "contributors": ["<PERSON><PERSON> <<EMAIL>> (thlorenz.com)", "<PERSON> (https://paulmillr.com)"], "engines": {"node": ">= 14.18.0"}, "files": ["index.js", "index.d.ts", "index.d.ts.map", "index.js.map", "esm"], "main": "./index.js", "module": "./esm/index.js", "types": "./index.d.ts", "exports": {".": {"import": "./esm/index.js", "require": "./index.js"}}, "sideEffects": false, "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "scripts": {"build": "tsc && tsc -p tsconfig.cjs.json", "lint": "prettier --check index.ts test/index.test.js", "format": "prettier --write index.ts test/index.test.js", "test": "node test/index.test.js", "test:coverage": "c8 node test/index.test.js"}, "devDependencies": {"@paulmillr/jsbt": "0.3.1", "@types/node": "20.14.8", "c8": "10.1.3", "chai": "4.3.4", "chai-subset": "1.6.0", "micro-should": "0.5.0", "prettier": "3.1.1", "typescript": "5.5.2"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}}