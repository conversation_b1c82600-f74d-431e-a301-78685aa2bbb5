import path, { join, dirname, resolve } from 'node:path';
import { spawn } from 'node:child_process';
import process from 'node:process';
import { normalizePath, createLogger } from 'vite';
import MagicString from 'magic-string';
import chokidar from 'chokidar';
import { loadConfig } from 'unconfig';
import Debug from 'debug';
import { platform } from '@uni-helper/uni-env';
import fs, { existsSync } from 'node:fs';
import { readFile, mkdir, writeFile as writeFile$1 } from 'node:fs/promises';
import { groupBy } from 'lodash-unified';
import JSON5 from 'json5';
import { parse as parse$1 } from 'yaml';
import { parse } from '@vue/compiler-sfc';
import fg from 'fast-glob';

function slash(str) {
  return str.replace(/\\/g, "/");
}

function getDeclaration(ctx) {
  const subPagesPath = ctx.subPageMetaData.map((sub) => {
    return sub.pages.map((v) => `"/${normalizePath(join(sub.root, v.path))}"`);
  }).flat();
  const tabsPagesPath = ctx.pagesGlobConfig?.tabBar?.list?.map((v) => {
    return `"/${v.pagePath}"`;
  }) ?? [];
  const allPagesPath = [...ctx.pageMetaData.filter((page) => !tabsPagesPath.includes(page.path)).map((v) => `"/${v.path}"`), ...subPagesPath];
  const code = `/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: ${allPagesPath.join(" |\n       ")};
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  ${tabsPagesPath.length ? `url: ${tabsPagesPath.join(" | ")}` : ""}
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
`;
  return code;
}
async function writeFile(filePath, content) {
  await mkdir(dirname(filePath), { recursive: true });
  return await writeFile$1(filePath, content, "utf-8");
}
async function writeDeclaration(ctx, filepath) {
  const originalContent = existsSync(filepath) ? await readFile(filepath, "utf-8") : "";
  const code = getDeclaration(ctx);
  if (!code)
    return;
  if (code !== originalContent)
    await writeFile(filepath, code);
}

const MODULE_ID_VIRTUAL = "virtual:uni-pages";
const RESOLVED_MODULE_ID_VIRTUAL = `\0${MODULE_ID_VIRTUAL}`;
const OUTPUT_NAME = "pages.json";
const FILE_EXTENSIONS = ["vue", "nvue", "uvue"];

async function parseSFC(code) {
  try {
    return parse(code, {
      pad: "space"
    }).descriptor || parse({
      source: code
    });
  } catch (error) {
    throw new Error(`[vite-plugin-uni-pages] Vue3's "@vue/compiler-sfc" is required. 
Original error: 
${error}`);
  }
}
function parseCustomBlock(block, filePath, options) {
  const lang = block.lang ?? options.routeBlockLang;
  const attr = {
    type: "page",
    ...block.attrs
  };
  let content;
  debug.routeBlock(`use ${lang} parser`);
  if (lang === "json5" || lang === "jsonc") {
    try {
      content = JSON5.parse(block.content);
    } catch (err) {
      throw new Error(
        `Invalid JSON5 format of <${block.type}> content in ${filePath}
${err.message}`
      );
    }
  } else if (lang === "json") {
    try {
      content = JSON.parse(block.content);
    } catch (err) {
      throw new Error(
        `Invalid JSON format of <${block.type}> content in ${filePath}
${err.message}`
      );
    }
  } else if (lang === "yaml" || lang === "yml") {
    try {
      content = parse$1(block.content);
    } catch (err) {
      throw new Error(
        `Invalid YAML format of <${block.type}> content in ${filePath}
${err.message}`
      );
    }
  }
  return {
    attr,
    content: content ?? {}
  };
}
async function getRouteSfcBlock(path) {
  const content = fs.readFileSync(path, "utf8");
  const parsedSFC = await parseSFC(content);
  const blockStr = parsedSFC?.customBlocks.find((b) => b.type === "route");
  return blockStr;
}
async function getRouteBlock(path, blockStr, options) {
  if (!blockStr)
    return;
  return parseCustomBlock(blockStr, path, options);
}

function invalidatePagesModule(server) {
  const { moduleGraph } = server;
  const mods = moduleGraph.getModulesByFile(RESOLVED_MODULE_ID_VIRTUAL);
  if (mods) {
    const seen = /* @__PURE__ */ new Set();
    mods.forEach((mod) => {
      moduleGraph.invalidateModule(mod, seen);
    });
  }
}
const debug = {
  hmr: Debug("vite-plugin-uni-pages:hmr"),
  routeBlock: Debug("vite-plugin-uni-pages:routeBlock"),
  options: Debug("vite-plugin-uni-pages:options"),
  pages: Debug("vite-plugin-uni-pages:pages"),
  subPages: Debug("vite-plugin-uni-pages:subPages"),
  error: Debug("vite-plugin-uni-pages:error"),
  cache: Debug("vite-plugin-uni-pages:cache"),
  declaration: Debug("vite-plugin-uni-pages:declaration")
};
function extsToGlob(extensions) {
  return extensions.length > 1 ? `{${extensions.join(",")}}` : extensions[0] || "";
}
function isTargetFile(path) {
  const ext = path.split(".").pop();
  return FILE_EXTENSIONS.includes(ext);
}
function mergePageMetaDataArray(pageMetaData) {
  const pageMetaDataObj = groupBy(pageMetaData, "path");
  const result = [];
  for (const path in pageMetaDataObj) {
    const _pageMetaData = pageMetaDataObj[path];
    const options = _pageMetaData[0];
    for (const page of _pageMetaData) {
      options.style = Object.assign(options.style ?? {}, page.style ?? {});
      Object.assign(options, page);
    }
    result.push(options);
  }
  return result;
}
function useCachedPages() {
  const pages = /* @__PURE__ */ new Map();
  function parseData(block) {
    return {
      content: block?.loc.source.trim() ?? "",
      attr: block?.attrs ?? ""
    };
  }
  function setCache(filePath, routeBlock) {
    pages.set(filePath, JSON.stringify(parseData(routeBlock)));
  }
  async function hasChanged(filePath, routeBlock) {
    if (!routeBlock)
      routeBlock = await getRouteSfcBlock(normalizePath(filePath));
    return !pages.has(filePath) || JSON.stringify(parseData(routeBlock)) !== pages.get(filePath);
  }
  return {
    setCache,
    hasChanged
  };
}

function resolveOptions(userOptions, viteRoot = process.cwd()) {
  const {
    dts = true,
    configSource = "pages.config",
    homePage = ["pages/index", "pages/index/index"],
    mergePages = true,
    dir = "src/pages",
    subPackages = [],
    outDir = "src",
    exclude = ["node_modules", ".git", "**/__*__/**"],
    routeBlockLang = "json5",
    minify = false,
    debug = false,
    onBeforeLoadUserConfig = () => {
    },
    onAfterLoadUserConfig = () => {
    },
    onBeforeScanPages = () => {
    },
    onAfterScanPages = () => {
    },
    onBeforeMergePageMetaData = () => {
    },
    onAfterMergePageMetaData = () => {
    },
    onBeforeWriteFile = () => {
    },
    onAfterWriteFile = () => {
    }
  } = userOptions;
  const root = viteRoot || slash(process.cwd());
  const resolvedDirs = resolvePageDirs(dir, root, exclude);
  const resolvedSubDirs = subPackages.map((dir2) => slash(dir2));
  const resolvedHomePage = typeof homePage === "string" ? [homePage] : homePage;
  const resolvedConfigSource = typeof configSource === "string" ? [{ files: configSource }] : configSource;
  const resolvedDts = !dts ? false : typeof dts === "string" ? dts : resolve(viteRoot, "uni-pages.d.ts");
  const resolvedOptions = {
    dts: resolvedDts,
    configSource: Array.isArray(resolvedConfigSource) ? resolvedConfigSource : [resolvedConfigSource],
    homePage: resolvedHomePage,
    mergePages,
    dirs: resolvedDirs,
    subPackages: resolvedSubDirs,
    outDir,
    exclude,
    routeBlockLang,
    root,
    minify,
    debug,
    onBeforeLoadUserConfig,
    onAfterLoadUserConfig,
    onBeforeScanPages,
    onAfterScanPages,
    onBeforeMergePageMetaData,
    onAfterMergePageMetaData,
    onBeforeWriteFile,
    onAfterWriteFile
  };
  return resolvedOptions;
}
function resolvePageDirs(dir, root, exclude) {
  const dirs = fg.sync(slash(dir), {
    ignore: exclude,
    onlyDirectories: true,
    dot: true,
    unique: true,
    cwd: root
  });
  return dirs;
}

function getPageFiles(path, options) {
  const { exclude } = options;
  const ext = extsToGlob(FILE_EXTENSIONS);
  const files = fg.sync(`**/*.${ext}`, {
    ignore: exclude,
    onlyFiles: true,
    cwd: path
  });
  return files;
}
function checkPagesJsonFile(path) {
  if (!fs.existsSync(path)) {
    writeFileSync(path, JSON.stringify({ pages: [{ path: "" }] }, null, 2));
    return false;
  }
  return true;
}
function writeFileSync(path, content) {
  fs.writeFileSync(path, content, { encoding: "utf-8" });
}

var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
let lsatPagesJson = "";
const { setCache, hasChanged } = useCachedPages();
class PageContext {
  constructor(userOptions, viteRoot = process.cwd()) {
    __publicField(this, "_server");
    __publicField(this, "pagesGlobConfig");
    __publicField(this, "pagesConfigSourcePaths", []);
    __publicField(this, "pagesPath", []);
    __publicField(this, "subPagesPath", {});
    __publicField(this, "pageMetaData", []);
    __publicField(this, "subPageMetaData", []);
    __publicField(this, "resolvedPagesJSONPath", "");
    __publicField(this, "rawOptions");
    __publicField(this, "root");
    __publicField(this, "options");
    __publicField(this, "logger");
    __publicField(this, "withUniPlatform", false);
    this.rawOptions = userOptions;
    this.root = slash(viteRoot);
    debug.options("root", this.root);
    this.options = resolveOptions(userOptions, this.root);
    const debugOption = this.options.debug;
    if (debugOption) {
      const prefix = "vite-plugin-uni-pages:";
      const suffix = typeof debugOption === "boolean" ? "*" : debugOption;
      Debug.enable(`${prefix}${suffix}`);
    }
    this.resolvedPagesJSONPath = path.join(this.root, this.options.outDir, OUTPUT_NAME);
    debug.options(this.options);
  }
  setLogger(logger) {
    this.logger = logger;
  }
  async loadUserPagesConfig() {
    const configSource = this.options.configSource;
    const { config, sources } = await loadConfig({ cwd: this.root, sources: configSource, defaults: {} });
    this.pagesGlobConfig = config;
    this.pagesConfigSourcePaths = sources;
    debug.options(config);
  }
  async scanPages() {
    const pageDirFiles = this.options.dirs.map((dir) => {
      return { dir, files: getPagePaths(dir, this.options) };
    });
    this.pagesPath = pageDirFiles.map((page) => page.files).flat();
    debug.pages(this.pagesPath);
  }
  async scanSubPages() {
    const subPagesPath = {};
    for (const dir of this.options.subPackages) {
      const pagePaths = getPagePaths(dir, this.options);
      subPagesPath[dir] = pagePaths;
    }
    this.subPagesPath = subPagesPath;
    debug.subPages(this.subPagesPath);
  }
  setupViteServer(server) {
    if (this._server === server)
      return;
    this._server = server;
    this.setupWatcher(server.watcher);
  }
  async setupWatcher(watcher) {
    watcher.add(this.pagesConfigSourcePaths);
    const targetDirs = [...this.options.dirs, ...this.options.subPackages].map((v) => slash(path.resolve(this.root, v)));
    const isInTargetDirs = (filePath) => targetDirs.some((v) => slash(path.resolve(this.root, filePath)).startsWith(v));
    watcher.on("add", async (path2) => {
      path2 = slash(path2);
      if (!isTargetFile(path2))
        return;
      if (!isInTargetDirs(path2))
        return;
      debug.pages(`File added: ${path2}`);
      if (await this.updatePagesJSON())
        this.onUpdate();
    });
    watcher.on("change", async (path2) => {
      path2 = slash(path2);
      if (!isTargetFile(path2))
        return;
      if (!isInTargetDirs(path2))
        return;
      debug.pages(`File changed: ${path2}`);
      debug.pages(targetDirs);
      debug.pages(isInTargetDirs(path2));
      if (await this.updatePagesJSON(path2))
        this.onUpdate();
    });
    watcher.on("change", async (path2) => {
      if (this.pagesConfigSourcePaths.includes(path2)) {
        debug.pages(`Config source changed: ${path2}`);
        if (await this.updatePagesJSON())
          this.onUpdate();
      }
    });
    watcher.on("unlink", async (path2) => {
      path2 = slash(path2);
      if (!isTargetFile(path2))
        return;
      if (!isInTargetDirs(path2))
        return;
      debug.pages(`File removed: ${path2}`);
      if (await this.updatePagesJSON())
        this.onUpdate();
    });
  }
  onUpdate() {
    if (!this._server)
      return;
    invalidatePagesModule(this._server);
    debug.hmr("Reload generated pages.");
    this._server.ws.send({
      type: "full-reload"
    });
  }
  async parsePage(page) {
    const { relativePath, absolutePath } = page;
    const routeSfcBlock = await getRouteSfcBlock(absolutePath);
    const routeBlock = await getRouteBlock(absolutePath, routeSfcBlock, this.options);
    setCache(absolutePath, routeSfcBlock);
    const relativePathWithFileName = relativePath.replace(path.extname(relativePath), "");
    const pageMetaDatum = {
      path: normalizePath(relativePathWithFileName),
      type: routeBlock?.attr.type ?? "page"
    };
    if (routeBlock)
      Object.assign(pageMetaDatum, routeBlock.content);
    return pageMetaDatum;
  }
  /**
   * parse pages rules && set page type
   * @param pages page path array
   * @param type  page type
   * @param overrides custom page config
   * @returns pages rules
   */
  async parsePages(pages, type, overrides) {
    const generatedPageMetaData = await Promise.all(pages.map(async (page) => await this.parsePage(page)));
    const customPageMetaData = overrides || [];
    const result = customPageMetaData.length ? mergePageMetaDataArray(generatedPageMetaData.concat(customPageMetaData)) : generatedPageMetaData;
    return type === "main" ? this.setHomePage(result) : result;
  }
  /**
   * set home page
   * @param result pages rules array
   * @returns pages rules
   */
  setHomePage(result) {
    const hasHome = result.some(({ type }) => type === "home");
    if (!hasHome) {
      const isFoundHome = result.some((item) => {
        const isFound = this.options.homePage.find((v) => v === item.path);
        if (isFound)
          item.type = "home";
        return isFound;
      });
      if (!isFoundHome) {
        this.logger?.warn('No home page found, check the configuration of pages.config.ts, or add the `homePage` option to UniPages in vite.config.js, or add `type="home"` to the routeBlock of your vue page.', {
          timestamp: true
        });
      }
    }
    result.sort((page) => page.type === "home" ? -1 : 0);
    return result;
  }
  async mergePageMetaData() {
    const pageMetaData = await this.parsePages(this.pagesPath, "main", this.pagesGlobConfig?.pages);
    this.pageMetaData = pageMetaData;
    debug.pages(this.pageMetaData);
  }
  async mergeSubPageMetaData() {
    const subPageMaps = {};
    const subPackages = this.pagesGlobConfig?.subPackages || [];
    for (const [dir, pages] of Object.entries(this.subPagesPath)) {
      const basePath = slash(path.join(this.options.root, this.options.outDir));
      const root = slash(path.relative(basePath, path.join(this.options.root, dir)));
      const globPackage = subPackages?.find((v) => v.root === root);
      subPageMaps[root] = await this.parsePages(pages, "sub", globPackage?.pages);
      subPageMaps[root] = subPageMaps[root].map((page) => ({ ...page, path: slash(path.relative(root, page.path)) }));
    }
    for (const { root, pages } of subPackages) {
      if (root && !subPageMaps[root])
        subPageMaps[root] = pages || [];
    }
    const subPageMetaData = Object.keys(subPageMaps).map((root) => ({ root, pages: subPageMaps[root] }));
    this.subPageMetaData = subPageMetaData;
    debug.subPages(this.subPageMetaData);
  }
  async updatePagesJSON(filepath) {
    if (filepath) {
      if (!await hasChanged(filepath)) {
        debug.cache(`The route block on page ${filepath} did not send any changes, skipping`);
        return false;
      }
    }
    checkPagesJsonFile(this.resolvedPagesJSONPath);
    this.options.onBeforeLoadUserConfig(this);
    await this.loadUserPagesConfig();
    this.options.onAfterLoadUserConfig(this);
    if (this.options.mergePages) {
      this.options.onBeforeScanPages(this);
      await this.scanPages();
      await this.scanSubPages();
      this.options.onAfterScanPages(this);
    }
    this.options.onBeforeMergePageMetaData(this);
    await this.mergePageMetaData();
    await this.mergeSubPageMetaData();
    this.options.onAfterMergePageMetaData(this);
    const pagesMap = /* @__PURE__ */ new Map();
    const pages = this.withUniPlatform ? this.pageMetaData.filter((v) => !/\..*$/.test(v.path) || v.path.includes(platform)).map((v) => ({ ...v, path: v.path.replace(/\..*$/, "") })) : this.pageMetaData;
    pages.forEach((v) => pagesMap.set(v.path, v));
    this.pageMetaData = [...pagesMap.values()];
    this.options.onBeforeWriteFile(this);
    const data = {
      ...this.pagesGlobConfig,
      pages: this.pageMetaData,
      subPackages: this.subPageMetaData
    };
    const pagesJson = JSON.stringify(data, null, this.options.minify ? void 0 : 2);
    this.generateDeclaration();
    if (lsatPagesJson === pagesJson) {
      debug.pages("PagesJson Not have change");
      return false;
    }
    writeFileSync(this.resolvedPagesJSONPath, pagesJson);
    lsatPagesJson = pagesJson;
    this.options.onAfterWriteFile(this);
    return true;
  }
  virtualModule() {
    const pages = `export const pages = ${this.resolveRoutes()};`;
    const subPackages = `export const subPackages = ${this.resolveSubRoutes()};`;
    return [pages, subPackages].join("\n");
  }
  resolveRoutes() {
    return JSON.stringify(this.pageMetaData, null, 2);
  }
  resolveSubRoutes() {
    return JSON.stringify(this.subPageMetaData, null, 2);
  }
  generateDeclaration() {
    if (!this.options.dts)
      return;
    debug.declaration("generating");
    return writeDeclaration(this, this.options.dts);
  }
}
function getPagePaths(dir, options) {
  const pagesDirPath = slash(path.resolve(options.root, dir));
  const basePath = slash(path.join(options.root, options.outDir));
  const files = getPageFiles(pagesDirPath, options);
  debug.pages(dir, files);
  const pagePaths = files.map((file) => slash(file)).map((file) => ({
    relativePath: path.relative(basePath, slash(path.resolve(pagesDirPath, file))),
    absolutePath: slash(path.resolve(pagesDirPath, file))
  }));
  return pagePaths;
}

function defineUniPages(config) {
  return config;
}

async function restart() {
  return new Promise((resolve) => {
    const build = spawn(process.argv.shift(), process.argv, {
      cwd: process.cwd(),
      detached: true,
      env: process.env
    });
    build.stdout?.pipe(process.stdout);
    build.stderr?.pipe(process.stderr);
    build.on("close", (code) => {
      resolve(process.exit(code));
    });
  });
}
function VitePluginUniPages(userOptions = {}) {
  let ctx;
  const resolvedPagesJSONPath = path.join(
    process.cwd(),
    userOptions.outDir ?? "src",
    OUTPUT_NAME
  );
  const isValidated = checkPagesJsonFile(resolvedPagesJSONPath);
  return {
    name: "vite-plugin-uni-pages",
    enforce: "pre",
    async configResolved(config) {
      ctx = new PageContext(userOptions, config.root);
      if (config.plugins.some((v) => v.name === "vite-plugin-uni-platform"))
        ctx.withUniPlatform = true;
      const logger = createLogger(void 0, {
        prefix: "[vite-plugin-uni-pages]"
      });
      ctx.setLogger(logger);
      await ctx.updatePagesJSON();
      if (config.command === "build") {
        if (!isValidated) {
          ctx.logger?.warn("In build mode, if `pages.json` does not exist, the plugin cannot create the complete `pages.json` before the uni-app, so it restarts the build.", {
            timestamp: true
          });
          await restart();
        }
        if (config.build.watch)
          ctx.setupWatcher(chokidar.watch([...ctx.options.dirs, ...ctx.options.subPackages]));
      }
    },
    // Applet do not support custom route block, so we need to remove the route block here
    async transform(code, id) {
      if (!/\.n?vue$/.test(id) && !code.includes("</route>"))
        return null;
      const s = new MagicString(code);
      const routeBlockMatches = s.original.matchAll(
        /<route[^>]*>([\s\S]*?)<\/route>/g
      );
      for (const match of routeBlockMatches) {
        const index = match.index;
        const length = match[0].length;
        s.remove(index, index + length);
      }
      if (s.hasChanged()) {
        return {
          code: s.toString(),
          map: s.generateMap({
            source: id,
            includeContent: true,
            file: `${id}.map`
          })
        };
      }
    },
    configureServer(server) {
      ctx.setupViteServer(server);
    },
    resolveId(id) {
      if (id === MODULE_ID_VIRTUAL)
        return RESOLVED_MODULE_ID_VIRTUAL;
    },
    load(id) {
      if (id === RESOLVED_MODULE_ID_VIRTUAL)
        return ctx.virtualModule();
    }
  };
}

export { FILE_EXTENSIONS, MODULE_ID_VIRTUAL, OUTPUT_NAME, PageContext, RESOLVED_MODULE_ID_VIRTUAL, VitePluginUniPages, checkPagesJsonFile, debug, VitePluginUniPages as default, defineUniPages, extsToGlob, getPageFiles, getRouteBlock, getRouteSfcBlock, invalidatePagesModule, isTargetFile, mergePageMetaDataArray, parseCustomBlock, parseSFC, resolveOptions, resolvePageDirs, useCachedPages, writeFileSync };
