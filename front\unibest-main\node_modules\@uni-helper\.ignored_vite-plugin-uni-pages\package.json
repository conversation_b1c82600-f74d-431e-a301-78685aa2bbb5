{"name": "@uni-helper/vite-plugin-uni-pages", "type": "module", "version": "0.2.20", "description": "File system based routing for uni-app applications using Vite", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "https://github.com/uni-helper/vite-plugin-uni-pages#readme", "repository": {"type": "git", "url": "git+https://github.com/uni-helper/vite-plugin-uni-pages.git"}, "bugs": "https://github.com/uni-helper/vite-plugin-uni-pages/issues", "keywords": [], "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./client": {"types": "./client.d.ts"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["client.d.ts", "dist"], "dependencies": {"@uni-helper/uni-env": "^0.1.1", "@vue/compiler-sfc": "^3.4.27", "chokidar": "^3.6.0", "debug": "^4.3.4", "fast-glob": "^3.3.2", "json5": "^2.2.3", "lodash-unified": "^1.0.3", "magic-string": "^0.30.10", "unconfig": "^0.3.13", "yaml": "^2.4.2"}, "devDependencies": {"@antfu/utils": "^0.7.8", "@types/debug": "^4.1.12", "@types/node": "^20.12.11"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}