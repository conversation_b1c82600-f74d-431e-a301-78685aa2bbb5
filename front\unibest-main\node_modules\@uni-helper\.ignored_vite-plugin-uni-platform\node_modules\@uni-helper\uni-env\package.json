{"name": "@uni-helper/uni-env", "type": "module", "version": "0.0.3", "packageManager": "pnpm@8.1.1", "description": "", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "https://github.com/uni-helper/uni-env#readme", "repository": {"type": "git", "url": "git+https://github.com/uni-helper/uni-env.git"}, "bugs": "https://github.com/uni-helper/uni-env/issues", "keywords": [], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.mjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist"], "devDependencies": {"@types/node": "^18.15.11", "bumpp": "^9.1.0", "esno": "^0.16.3", "typescript": "^5.0.4", "unbuild": "^1.2.1", "vitest": "^0.30.1"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub", "release": "bumpp", "start": "esno src/index.ts"}}