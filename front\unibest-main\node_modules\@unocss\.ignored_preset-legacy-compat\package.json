{"name": "@unocss/preset-legacy-compat", "version": "0.59.4", "description": "Collections of legacy compatibility utilities.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unocss/unocss/tree/main/packages/preset-legacy-compat#readme", "repository": {"type": "git", "url": "https://github.com/unocss/unocss", "directory": "packages/preset-legacy-compat"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "unocss-preset"], "sideEffects": false, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "dependencies": {"@unocss/core": "0.59.4"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}