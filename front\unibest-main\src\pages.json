{"globalStyle": {"navigationStyle": "custom", "navigationBarTitleText": "rjb-sias", "navigationBarBackgroundColor": "#f8f8f8", "navigationBarTextStyle": "black", "backgroundColor": "#FFFFFF", "animation": "pop-in", "animationDuration": 300}, "easycom": {"autoscan": true, "custom": {"^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue", "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue"}}, "tabBar": {"color": "#999999", "selectedColor": "#018d71", "backgroundColor": "#F8F8F8", "borderStyle": "black", "height": "50px", "fontSize": "10px", "iconWidth": "24px", "spacing": "3px", "list": [{"iconPath": "static/tabbar/home.png", "selectedIconPath": "static/tabbar/homeHL.png", "pagePath": "pages/index/index", "text": "首页"}, {"iconPath": "static/tabbar/example.png", "selectedIconPath": "static/tabbar/exampleHL.png", "pagePath": "pages/about/about", "text": "关于"}, {"iconPath": "static/tabbar/example.png", "selectedIconPath": "static/tabbar/exampleHL.png", "pagePath": "pages/size-test/index", "text": "尺寸测试"}]}, "pages": [{"path": "pages/index/index", "type": "home"}, {"path": "pages/404/404", "type": "page"}, {"path": "pages/about/about", "type": "page"}, {"path": "pages/about/agreement", "type": "page"}, {"path": "pages/about/index", "type": "page"}, {"path": "pages/about/privacy-policy", "type": "page"}, {"path": "pages/achievement/wall", "type": "page"}, {"path": "pages/aichat/index", "type": "page"}, {"path": "pages/assessment/initial", "type": "page"}, {"path": "pages/assessment/result", "type": "page"}, {"path": "pages/auth/forgetpassword", "type": "page"}, {"path": "pages/auth/login", "type": "page"}, {"path": "pages/auth/register", "type": "page"}, {"path": "pages/interview/detail", "type": "page"}, {"path": "pages/interview/index", "type": "page"}, {"path": "pages/interview/job-detail", "type": "page"}, {"path": "pages/interview/result", "type": "page"}, {"path": "pages/interview/room", "type": "page"}, {"path": "pages/interview/select", "type": "page"}, {"path": "pages/learning/all-question-banks", "type": "page"}, {"path": "pages/learning/all-questions", "type": "page"}, {"path": "pages/learning/book-reader", "type": "page"}, {"path": "pages/learning/book", "type": "page"}, {"path": "pages/learning/community", "type": "page"}, {"path": "pages/learning/data-center", "type": "page"}, {"path": "pages/learning/detail", "type": "page"}, {"path": "pages/learning/index", "type": "page"}, {"path": "pages/learning/learning", "type": "page"}, {"path": "pages/learning/plan", "type": "page"}, {"path": "pages/learning/practice-question", "type": "page"}, {"path": "pages/learning/practice-result", "type": "page"}, {"path": "pages/learning/practice", "type": "page"}, {"path": "pages/learning/question-bank-detail", "type": "page"}, {"path": "pages/learning/question-bank", "type": "page"}, {"path": "pages/learning/question-comments", "type": "page"}, {"path": "pages/learning/question-detail", "type": "page"}, {"path": "pages/learning/recommend", "type": "page"}, {"path": "pages/learning/resource-detail", "type": "page"}, {"path": "pages/learning/resources", "type": "page"}, {"path": "pages/learning/video-player", "type": "page"}, {"path": "pages/learning/video", "type": "page"}, {"path": "pages/message/index", "type": "page"}, {"path": "pages/pay/pay-confirm", "type": "page"}, {"path": "pages/pay/pay", "type": "page"}, {"path": "pages/size-test/index", "type": "page"}, {"path": "pages/user/ability-assessment", "type": "page"}, {"path": "pages/user/agreement", "type": "page"}, {"path": "pages/user/center", "type": "page"}, {"path": "pages/user/feedback-list", "type": "page"}, {"path": "pages/user/feedback", "type": "page"}, {"path": "pages/user/growth-detail", "type": "page"}, {"path": "pages/user/history", "type": "page"}, {"path": "pages/user/more-setting", "type": "page"}, {"path": "pages/user/preference-settings", "type": "page"}, {"path": "pages/user/privacy-policy", "type": "page"}, {"path": "pages/user/privacy", "type": "page"}, {"path": "pages/user/profile", "type": "page"}, {"path": "pages/user/resume-preview", "type": "page"}, {"path": "pages/user/resume", "type": "page"}], "subPackages": []}